<style>
    :root {
        --main-color-one: <?php echo e(get_static_option('main_color_one') ?? '#6176f6'); ?>;
        --main-color-two: <?php echo e(get_static_option('main_color_two') ?? '#2bdfff'); ?>;
        --main-color-one-rgb: <?php echo e('97, 118, 246'); ?>;
        --secondary-color: <?php echo e(get_static_option('secondary_color')); ?>;
        --secondary-color-rgb: <?php echo e('255, 165, 0'); ?>;
        --bg-gradient: <?php echo e('linear-gradient(90deg, #fef0db 0%, #fefbf6 50%, #ecf8f0 100%)'); ?>;
        --section-bg-base: <?php echo e('#6176f6'); ?>;
        --section-bg-1: <?php echo e('#F7F8FF'); ?>;
        --section-bg-2: <?php echo e('#F5F5F5'); ?>;
        --footer-bg-1: <?php echo e('#020418'); ?>;
        --footer-bg-2: <?php echo e('#1E84FE'); ?>;
        --copyright-bg-1: <?php echo e('#323336'); ?>;
        --border-color: <?php echo e('#EAECF0'); ?>;
        --border-color-2: <?php echo e('#ddd'); ?>;
        --heading-color: <?php echo e(get_static_option('heading_color','#1D2635')); ?>;
        --paragraph-color: <?php echo e(get_static_option('paragraph_color','#1D2635')); ?>;
        --body-color: <?php echo e(get_static_option('body_color','#999')); ?>;
        --white: <?php echo e('#fff'); ?>;
        --active-color: <?php echo e('#00C897'); ?>;
        --active-color-rgb: <?php echo e('0, 200, 151'); ?>;
        --success-color: <?php echo e('#65c18c'); ?>;
        --success-color-rgb: <?php echo e('101, 193, 140'); ?>;
        --danger-color: <?php echo e('#f53a3a'); ?>;
        --danger-color-rgb: <?php echo e('245, 58, 58'); ?>;
        --promo-one: <?php echo e('#e3e1ff'); ?>;
        --promo-two: <?php echo e('#ffe6d3'); ?>;
        --promo-three: <?php echo e('#dbf3ff'); ?>;
        --promo-four: <?php echo e('#efffe6'); ?>;
        --promo-five: <?php echo e('#ffc9c9'); ?>;
        --promo-six: <?php echo e('#ceffda'); ?>;
        --promo-seven: <?php echo e('#b2ccfd'); ?>;
        --promo-eight: <?php echo e('#f0bcff'); ?>;
        --heading-font: <?php echo e(get_static_option('heading_font_family')); ?>,sans-serif;
        --body-font: <?php echo e(get_static_option('body_font_family')); ?>,sans-serif;
        --Otomanopee-font: <?php echo e(get_static_option('section_font_family')); ?>,sans-serif;
        
        
    }
</style><?php /**PATH E:\xampp\htdocs\Travel_Offers\core\resources\views/frontend/layout/partials/root-style.blade.php ENDPATH**/ ?>