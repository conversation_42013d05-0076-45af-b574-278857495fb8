/*! For license information please see summernote-lite.min.js.LICENSE.txt */ !function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("jquery"));else if("function"==typeof define&&define.amd)define(["jquery"],e);else{var n=e("object"==typeof exports?require("jquery"):t.jQuery);for(var o in n)("object"==typeof exports?exports:t)[o]=n[o]}}(window,function(t){return function(t){var e={};function n(o){if(e[o])return e[o].exports;var i=e[o]={i:o,l:!1,exports:{}};return t[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,o){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e||4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(o,i,(function(e){return t[e]}).bind(null,i));return o},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=51)}({0:function(e,n){e.exports=t},1:function(t,e,n){"use strict";var o=n(0),i=n.n(o);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r=function(){var t,e;function n(t,e,o,i){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.markup=t,this.children=e,this.options=o,this.callback=i}return t=n,e=[{key:"render",value:function(t){var e=i()(this.markup);if(this.options&&this.options.contents&&e.html(this.options.contents),this.options&&this.options.className&&e.addClass(this.options.className),this.options&&this.options.data&&i.a.each(this.options.data,function(t,n){e.attr("data-"+t,n)}),this.options&&this.options.click&&e.on("click",this.options.click),this.children){var n=e.find(".note-children-container");this.children.forEach(function(t){t.render(n.length?n:e)})}return this.callback&&this.callback(e,this.options),this.options&&this.options.callback&&this.options.callback(e),t&&t.append(e),e}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}();e.a={create:function(t,e){return function(){var n="object"===a(arguments[1])?arguments[1]:arguments[0],o=Array.isArray(arguments[0])?arguments[0]:[];return n&&n.children&&(o=n.children),new r(t,o,n,e)}}}},2:function(t,e){(function(e){t.exports=e}).call(this,{})},3:function(t,e,n){"use strict";var o=n(0),i=n.n(o);i.a.summernote=i.a.summernote||{lang:{}},i.a.extend(i.a.summernote.lang,{"en-US":{font:{bold:"Bold",italic:"Italic",underline:"Underline",clear:"Remove Font Style",height:"Line Height",name:"Font Family",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript",size:"Font Size",sizeunit:"Font Size Unit"},image:{image:"Picture",insert:"Insert Image",resizeFull:"Resize full",resizeHalf:"Resize half",resizeQuarter:"Resize quarter",resizeNone:"Original size",floatLeft:"Float Left",floatRight:"Float Right",floatNone:"Remove float",shapeRounded:"Shape: Rounded",shapeCircle:"Shape: Circle",shapeThumbnail:"Shape: Thumbnail",shapeNone:"Shape: None",dragImageHere:"Drag image or text here",dropImage:"Drop image or Text",selectFromFiles:"Select from files",maximumFileSize:"Maximum file size",maximumFileSizeError:"Maximum file size exceeded.",url:"Image URL",remove:"Remove Image",original:"Original"},video:{video:"Video",videoLink:"Video Link",insert:"Insert Video",url:"Video URL",providers:"(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)"},link:{link:"Link",insert:"Insert Link",unlink:"Unlink",edit:"Edit",textToDisplay:"Text to display",url:"To what URL should this link go?",openInNewWindow:"Open in new window",useProtocol:"Use default protocol"},table:{table:"Table",addRowAbove:"Add row above",addRowBelow:"Add row below",addColLeft:"Add column left",addColRight:"Add column right",delRow:"Delete row",delCol:"Delete column",delTable:"Delete table"},hr:{insert:"Insert Horizontal Rule"},style:{style:"Style",p:"Normal",blockquote:"Quote",pre:"Code",h1:"Header 1",h2:"Header 2",h3:"Header 3",h4:"Header 4",h5:"Header 5",h6:"Header 6"},lists:{unordered:"Unordered list",ordered:"Ordered list"},options:{help:"Help",fullscreen:"Full Screen"},paragraph:{paragraph:"Paragraph",outdent:"Outdent",indent:"Indent",left:"Align left",center:"Align center",right:"Align right",justify:"Justify full"},color:{recent:"Recent Color",more:"More Color",background:"Background Color",foreground:"Text Color",transparent:"Transparent",setTransparent:"Set transparent",reset:"Reset",resetToDefault:"Reset to default",cpSelect:"Select"},shortcut:{shortcuts:"Keyboard shortcuts",close:"Close",textFormatting:"Text formatting",action:"Action",paragraphFormatting:"Paragraph formatting",documentStyle:"Document Style",extraKeys:"Extra keys"},help:{escape:"Escape",insertParagraph:"Insert Paragraph",undo:"Undo the last command",redo:"Redo the last command",tab:"Tab",untab:"Untab",bold:"Set a bold style",italic:"Set a italic style",underline:"Set a underline style",strikethrough:"Set a strikethrough style",removeFormat:"Clean a style",justifyLeft:"Set left align",justifyCenter:"Set center align",justifyRight:"Set right align",justifyFull:"Set full align",insertUnorderedList:"Toggle unordered list",insertOrderedList:"Toggle ordered list",outdent:"Outdent on current paragraph",indent:"Indent on current paragraph",formatPara:"Change current block's format as a paragraph(P tag)",formatH1:"Change current block's format as H1",formatH2:"Change current block's format as H2",formatH3:"Change current block's format as H3",formatH4:"Change current block's format as H4",formatH5:"Change current block's format as H5",formatH6:"Change current block's format as H6",insertHorizontalRule:"Insert horizontal rule","linkDialog.show":"Show Link Dialog"},history:{undo:"Undo",redo:"Redo"},specialChar:{specialChar:"SPECIAL CHARACTERS",select:"Select Special characters"},output:{noSelection:"No Selection Made!"}}});var a="function"==typeof define&&n(2),r=["sans-serif","serif","monospace","cursive","fantasy"];function s(t){return -1===i.a.inArray(t.toLowerCase(),r)?"'".concat(t,"'"):t}var l,c=navigator.userAgent,u=/MSIE|Trident/i.test(c);if(u){var d=/MSIE (\d+[.]\d+)/.exec(c);d&&(l=parseFloat(d[1])),(d=/Trident\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(c))&&(l=parseFloat(d[1]))}var h=/Edge\/\d+/.test(c),f="ontouchstart"in window||navigator.MaxTouchPoints>0||navigator.msMaxTouchPoints>0,p={isMac:navigator.appVersion.indexOf("Mac")>-1,isMSIE:u,isEdge:h,isFF:!h&&/firefox/i.test(c),isPhantom:/PhantomJS/i.test(c),isWebkit:!h&&/webkit/i.test(c),isChrome:!h&&/chrome/i.test(c),isSafari:!h&&/safari/i.test(c)&&!/chrome/i.test(c),browserVersion:l,jqueryVersion:parseFloat(i.a.fn.jquery),isSupportAmd:a,isSupportTouch:f,isFontInstalled:function(t){var e="Comic Sans MS"===t?"Courier New":"Comic Sans MS",n=document.createElement("canvas").getContext("2d");n.font="200px '"+e+"'";var o=n.measureText("mmmmmmmmmmwwwww").width;return n.font="200px "+s(t)+', "'+e+'"',o!==n.measureText("mmmmmmmmmmwwwww").width},isW3CRangeSupport:!!document.createRange,inputEventName:u?"DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted":"input",genericFontFamilies:r,validFontName:s},v=0,m={eq:function(t){return function(e){return t===e}},eq2:function(t,e){return t===e},peq2:function(t){return function(e,n){return e[t]===n[t]}},ok:function(){return!0},fail:function(){return!1},self:function(t){return t},not:function(t){return function(){return!t.apply(t,arguments)}},and:function(t,e){return function(n){return t(n)&&e(n)}},invoke:function(t,e){return function(){return t[e].apply(t,arguments)}},resetUniqueId:function(){v=0},uniqueId:function(t){var e=++v+"";return t?t+e:e},rect2bnd:function(t){var e=i()(document);return{top:t.top+e.scrollTop(),left:t.left+e.scrollLeft(),width:t.right-t.left,height:t.bottom-t.top}},invertObject:function(t){var e={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[t[n]]=n);return e},namespaceToCamel:function(t,e){return(e=e||"")+t.split(".").map(function(t){return t.substring(0,1).toUpperCase()+t.substring(1)}).join("")},debounce:function(t,e,n){var o;return function(){var i=this,a=arguments,r=n&&!o;clearTimeout(o),o=setTimeout(function(){o=null,n||t.apply(i,a)},e),r&&t.apply(i,a)}},isValidUrl:function(t){return/[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/gi.test(t)}};function g(t){return t[0]}function b(t){return t[t.length-1]}function k(t){return t.slice(1)}function y(t,e){if(t&&t.length&&e){if(t.indexOf)return -1!==t.indexOf(e);if(t.contains)return t.contains(e)}return!1}var C={head:g,last:b,initial:function(t){return t.slice(0,t.length-1)},tail:k,prev:function(t,e){if(t&&t.length&&e){var n=t.indexOf(e);return -1===n?null:t[n-1]}return null},next:function(t,e){if(t&&t.length&&e){var n=t.indexOf(e);return -1===n?null:t[n+1]}return null},find:function(t,e){for(var n=0,o=t.length;n<o;n++){var i=t[n];if(e(i))return i}},contains:y,all:function(t,e){for(var n=0,o=t.length;n<o;n++)if(!e(t[n]))return!1;return!0},sum:function(t,e){return e=e||m.self,t.reduce(function(t,n){return t+e(n)},0)},from:function(t){for(var e=[],n=t.length,o=-1;++o<n;)e[o]=t[o];return e},isEmpty:function(t){return!t||!t.length},clusterBy:function(t,e){var n;return t.length?k(t).reduce(function(t,n){var o=b(t);return e(b(o),n)?o[o.length]=n:t[t.length]=[n],t},[[(n=t)[0]]]):[]},compact:function(t){for(var e=[],n=0,o=t.length;n<o;n++)t[n]&&e.push(t[n]);return e},unique:function(t){for(var e=[],n=0,o=t.length;n<o;n++)y(e,t[n])||e.push(t[n]);return e}};function w(t){return t&&i()(t).hasClass("note-editable")}function x(t){return t=t.toUpperCase(),function(e){return e&&e.nodeName.toUpperCase()===t}}function $(t){return t&&3===t.nodeType}function S(t){return t&&/^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT|^AUDIO|^VIDEO|^EMBED/.test(t.nodeName.toUpperCase())}function N(t){return!w(t)&&t&&/^DIV|^P|^LI|^H[1-7]/.test(t.nodeName.toUpperCase())}var T=x("PRE"),_=x("LI"),I=x("TABLE"),E=x("DATA");function P(t){return!(D(t)||R(t)||L(t)||N(t)||I(t)||F(t)||E(t))}function R(t){return t&&/^UL|^OL/.test(t.nodeName.toUpperCase())}var L=x("HR");function A(t){return t&&/^TD|^TH/.test(t.nodeName.toUpperCase())}var F=x("BLOCKQUOTE");function D(t){return A(t)||F(t)||w(t)}var H=x("A"),B=x("BODY"),z=p.isMSIE&&p.browserVersion<11?"&nbsp;":"<br>";function M(t){return $(t)?t.nodeValue.length:t?t.childNodes.length:0}function O(t){var e=M(t);return 0===e||!$(t)&&1===e&&t.innerHTML===z||!(!C.all(t.childNodes,$)||""!==t.innerHTML)}function U(t){S(t)||M(t)||(t.innerHTML=z)}function W(t,e){for(;t;){if(e(t))return t;if(w(t))break;t=t.parentNode}return null}function j(t,e){e=e||m.fail;var n=[];return W(t,function(t){return w(t)||n.push(t),e(t)}),n}function K(t,e){e=e||m.fail;for(var n=[];t&&!e(t);)n.push(t),t=t.nextSibling;return n}function V(t,e){var n=e.nextSibling,o=e.parentNode;return n?o.insertBefore(t,n):o.appendChild(t),t}function q(t,e){return i.a.each(e,function(e,n){t.appendChild(n)}),t}function G(t){return 0===t.offset}function Y(t){return t.offset===M(t.node)}function Z(t){return G(t)||Y(t)}function X(t,e){for(;t&&t!==e;){if(0!==J(t))return!1;t=t.parentNode}return!0}function Q(t,e){if(!e)return!1;for(;t&&t!==e;){if(J(t)!==M(t.parentNode)-1)return!1;t=t.parentNode}return!0}function J(t){for(var e=0;t=t.previousSibling;)e+=1;return e}function tt(t){return!!(t&&t.childNodes&&t.childNodes.length)}function te(t,e){var n,o;if(0===t.offset){if(w(t.node))return null;n=t.node.parentNode,o=J(t.node)}else tt(t.node)?o=M(n=t.node.childNodes[t.offset-1]):(n=t.node,o=e?0:t.offset-1);return{node:n,offset:o}}function tn(t,e){var n,o;if(M(t.node)===t.offset){if(w(t.node))return null;var i=ti(t.node);i?(n=i,o=0):(n=t.node.parentNode,o=J(t.node)+1)}else tt(t.node)?(n=t.node.childNodes[t.offset],o=0):(n=t.node,o=e?M(t.node):t.offset+1);return{node:n,offset:o}}function to(t,e){var n,o;if(O(t.node))return{node:n=t.node.nextSibling,offset:o=0};if(M(t.node)===t.offset){if(w(t.node))return null;var i=ti(t.node);i?(n=i,o=0):(n=t.node.parentNode,o=J(t.node)+1),w(n)&&(n=t.node.nextSibling,o=0)}else if(tt(t.node)){if(o=0,O(n=t.node.childNodes[t.offset]))return null}else if(n=t.node,o=e?M(t.node):t.offset+1,O(n))return null;return{node:n,offset:o}}function ti(t){if(t.nextSibling&&t.parent===t.nextSibling.parent)return $(t.nextSibling)?t.nextSibling:ti(t.nextSibling)}function ta(t,e){return t.node===e.node&&t.offset===e.offset}function tr(t,e){var n=e&&e.isSkipPaddingBlankHTML,o=e&&e.isNotSplitEdgePoint,i=e&&e.isDiscardEmptySplits;if(i&&(n=!0),Z(t)&&($(t.node)||o)){if(G(t))return t.node;if(Y(t))return t.node.nextSibling}if($(t.node))return t.node.splitText(t.offset);var a=t.node.childNodes[t.offset],r=V(t.node.cloneNode(!1),t.node);return q(r,K(a)),n||(U(t.node),U(r)),i&&(O(t.node)&&tc(t.node),O(r))?(tc(r),t.node.nextSibling):r}function ts(t,e,n){var o=j(e.node,m.eq(t));return o.length?1===o.length?tr(e,n):o.reduce(function(t,o){return t===e.node&&(t=tr(e,n)),tr({node:o,offset:t?J(t):M(o)},n)}):null}function tl(t){return document.createElement(t)}function tc(t,e){if(t&&t.parentNode){if(t.removeNode)return t.removeNode(e);var n=t.parentNode;if(!e){for(var o=[],i=0,a=t.childNodes.length;i<a;i++)o.push(t.childNodes[i]);for(var r=0,s=o.length;r<s;r++)n.insertBefore(o[r],t)}n.removeChild(t)}}var tu=x("TEXTAREA");function td(t,e){var n=tu(t[0])?t.val():t.html();return e?n.replace(/[\n\r]/g,""):n}var th={NBSP_CHAR:"\xa0",ZERO_WIDTH_NBSP_CHAR:"\uFEFF",blank:z,emptyPara:"<p>".concat(z,"</p>"),makePredByNodeName:x,isEditable:w,isControlSizing:function(t){return t&&i()(t).hasClass("note-control-sizing")},isText:$,isElement:function(t){return t&&1===t.nodeType},isVoid:S,isPara:N,isPurePara:function(t){return N(t)&&!_(t)},isHeading:function(t){return t&&/^H[1-7]/.test(t.nodeName.toUpperCase())},isInline:P,isBlock:m.not(P),isBodyInline:function(t){return P(t)&&!W(t,N)},isBody:B,isParaInline:function(t){return P(t)&&!!W(t,N)},isPre:T,isList:R,isTable:I,isData:E,isCell:A,isBlockquote:F,isBodyContainer:D,isAnchor:H,isDiv:x("DIV"),isLi:_,isBR:x("BR"),isSpan:x("SPAN"),isB:x("B"),isU:x("U"),isS:x("S"),isI:x("I"),isImg:x("IMG"),isTextarea:tu,deepestChildIsEmpty:function(t){do if(null===t.firstElementChild||""===t.firstElementChild.innerHTML)break;while(t=t.firstElementChild);return O(t)},isEmpty:O,isEmptyAnchor:m.and(H,O),isClosestSibling:function(t,e){return t.nextSibling===e||t.previousSibling===e},withClosestSiblings:function(t,e){e=e||m.ok;var n=[];return t.previousSibling&&e(t.previousSibling)&&n.push(t.previousSibling),n.push(t),t.nextSibling&&e(t.nextSibling)&&n.push(t.nextSibling),n},nodeLength:M,isLeftEdgePoint:G,isRightEdgePoint:Y,isEdgePoint:Z,isLeftEdgeOf:X,isRightEdgeOf:Q,isLeftEdgePointOf:function(t,e){return G(t)&&X(t.node,e)},isRightEdgePointOf:function(t,e){return Y(t)&&Q(t.node,e)},prevPoint:te,nextPoint:tn,nextPointWithEmptyNode:to,isSamePoint:ta,isVisiblePoint:function(t){if($(t.node)||!tt(t.node)||O(t.node))return!0;var e=t.node.childNodes[t.offset-1],n=t.node.childNodes[t.offset];return!(e&&!S(e)||n&&!S(n))},prevPointUntil:function(t,e){for(;t;){if(e(t))return t;t=te(t)}return null},nextPointUntil:function(t,e){for(;t;){if(e(t))return t;t=tn(t)}return null},isCharPoint:function(t){if(!$(t.node))return!1;var e=t.node.nodeValue.charAt(t.offset-1);return e&&" "!==e&&"\xa0"!==e},isSpacePoint:function(t){if(!$(t.node))return!1;var e=t.node.nodeValue.charAt(t.offset-1);return" "===e||"\xa0"===e},walkPoint:function(t,e,n,o){for(var i=t;i&&(n(i),!ta(i,e));)i=to(i,o&&t.node!==i.node&&e.node!==i.node)},ancestor:W,singleChildAncestor:function(t,e){for(t=t.parentNode;t&&1===M(t);){if(e(t))return t;if(w(t))break;t=t.parentNode}return null},listAncestor:j,lastAncestor:function(t,e){var n=j(t);return C.last(n.filter(e))},listNext:K,listPrev:function(t,e){e=e||m.fail;for(var n=[];t&&!e(t);)n.push(t),t=t.previousSibling;return n},listDescendant:function(t,e){var n=[];return e=e||m.ok,function o(i){t!==i&&e(i)&&n.push(i);for(var a=0,r=i.childNodes.length;a<r;a++)o(i.childNodes[a])}(t),n},commonAncestor:function(t,e){for(var n=j(t),o=e;o;o=o.parentNode)if(n.indexOf(o)>-1)return o;return null},wrap:function(t,e){var n=t.parentNode,o=i()("<"+e+">")[0];return n.insertBefore(o,t),o.appendChild(t),o},insertAfter:V,appendChildNodes:q,position:J,hasChildren:tt,makeOffsetPath:function(t,e){return j(e,m.eq(t)).map(J).reverse()},fromOffsetPath:function(t,e){for(var n=t,o=0,i=e.length;o<i;o++)n=n.childNodes.length<=e[o]?n.childNodes[n.childNodes.length-1]:n.childNodes[e[o]];return n},splitTree:ts,splitPoint:function(t,e){var n,o,i=e?N:D,a=j(t.node,i),r=C.last(a)||t.node;i(r)?(n=a[a.length-2],o=r):o=(n=r).parentNode;var s=n&&ts(n,t,{isSkipPaddingBlankHTML:e,isNotSplitEdgePoint:e});return s||o!==t.node||(s=t.node.childNodes[t.offset]),{rightNode:s,container:o}},create:tl,createText:function(t){return document.createTextNode(t)},remove:tc,removeWhile:function(t,e){for(;t&&!w(t)&&e(t);){var n=t.parentNode;tc(t),t=n}},replace:function(t,e){if(t.nodeName.toUpperCase()===e.toUpperCase())return t;var n=tl(e);return t.style.cssText&&(n.style.cssText=t.style.cssText),q(n,C.from(t.childNodes)),V(n,t),tc(t),n},html:function(t,e){var n=td(t);return e&&(n=(n=n.replace(/<(\/?)(\b(?!!)[^>\s]*)(.*?)(\s*\/?>)/g,function(t,e,n){n=n.toUpperCase();var o=/^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(n)&&!!e,i=/^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(n);return t+(o||i?"\n":"")})).trim()),n},value:td,posFromPlaceholder:function(t){var e=i()(t),n=e.offset(),o=e.outerHeight(!0);return{left:n.left,top:n.top+o}},attachEvents:function(t,e){Object.keys(e).forEach(function(n){t.on(n,e[n])})},detachEvents:function(t,e){Object.keys(e).forEach(function(n){t.off(n,e[n])})},isCustomStyleTag:function(t){return t&&!$(t)&&C.contains(t.classList,"note-styletag")}},tf=function(){var t,e;function n(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.$note=t,this.memos={},this.modules={},this.layoutInfo={},this.options=i.a.extend(!0,{},e),i.a.summernote.ui=i.a.summernote.ui_template(this.options),this.ui=i.a.summernote.ui,this.initialize()}return t=n,e=[{key:"initialize",value:function(){return this.layoutInfo=this.ui.createLayout(this.$note),this._initialize(),this.$note.hide(),this}},{key:"destroy",value:function(){this._destroy(),this.$note.removeData("summernote"),this.ui.removeLayout(this.$note,this.layoutInfo)}},{key:"reset",value:function(){var t=this.isDisabled();this.code(th.emptyPara),this._destroy(),this._initialize(),t&&this.disable()}},{key:"_initialize",value:function(){var t=this;this.options.id=m.uniqueId(i.a.now()),this.options.container=this.options.container||this.layoutInfo.editor;var e=i.a.extend({},this.options.buttons);Object.keys(e).forEach(function(n){t.memo("button."+n,e[n])});var n=i.a.extend({},this.options.modules,i.a.summernote.plugins||{});Object.keys(n).forEach(function(e){t.module(e,n[e],!0)}),Object.keys(this.modules).forEach(function(e){t.initializeModule(e)})}},{key:"_destroy",value:function(){var t=this;Object.keys(this.modules).reverse().forEach(function(e){t.removeModule(e)}),Object.keys(this.memos).forEach(function(e){t.removeMemo(e)}),this.triggerEvent("destroy",this)}},{key:"code",value:function(t){var e=this.invoke("codeview.isActivated");if(void 0===t)return this.invoke("codeview.sync"),e?this.layoutInfo.codable.val():this.layoutInfo.editable.html();e?this.invoke("codeview.sync",t):this.layoutInfo.editable.html(t),this.$note.val(t),this.triggerEvent("change",t,this.layoutInfo.editable)}},{key:"isDisabled",value:function(){return"false"===this.layoutInfo.editable.attr("contenteditable")}},{key:"enable",value:function(){this.layoutInfo.editable.attr("contenteditable",!0),this.invoke("toolbar.activate",!0),this.triggerEvent("disable",!1),this.options.editing=!0}},{key:"disable",value:function(){this.invoke("codeview.isActivated")&&this.invoke("codeview.deactivate"),this.layoutInfo.editable.attr("contenteditable",!1),this.options.editing=!1,this.invoke("toolbar.deactivate",!0),this.triggerEvent("disable",!0)}},{key:"triggerEvent",value:function(){var t=C.head(arguments),e=C.tail(C.from(arguments)),n=this.options.callbacks[m.namespaceToCamel(t,"on")];n&&n.apply(this.$note[0],e),this.$note.trigger("summernote."+t,e)}},{key:"initializeModule",value:function(t){var e=this.modules[t];e.shouldInitialize=e.shouldInitialize||m.ok,e.shouldInitialize()&&(e.initialize&&e.initialize(),e.events&&th.attachEvents(this.$note,e.events))}},{key:"module",value:function(t,e,n){if(1===arguments.length)return this.modules[t];this.modules[t]=new e(this),n||this.initializeModule(t)}},{key:"removeModule",value:function(t){var e=this.modules[t];e.shouldInitialize()&&(e.events&&th.detachEvents(this.$note,e.events),e.destroy&&e.destroy()),delete this.modules[t]}},{key:"memo",value:function(t,e){if(1===arguments.length)return this.memos[t];this.memos[t]=e}},{key:"removeMemo",value:function(t){this.memos[t]&&this.memos[t].destroy&&this.memos[t].destroy(),delete this.memos[t]}},{key:"createInvokeHandlerAndUpdateState",value:function(t,e){var n=this;return function(o){n.createInvokeHandler(t,e)(o),n.invoke("buttons.updateCurrentStyle")}}},{key:"createInvokeHandler",value:function(t,e){var n=this;return function(o){o.preventDefault();var a=i()(o.target);n.invoke(t,e||a.closest("[data-value]").data("value"),a)}}},{key:"invoke",value:function(){var t=C.head(arguments),e=C.tail(C.from(arguments)),n=t.split("."),o=n.length>1,i=o&&C.head(n),a=o?C.last(n):C.head(n),r=this.modules[i||"editor"];return!i&&this[a]?this[a].apply(this,e):r&&r[a]&&r.shouldInitialize()?r[a].apply(r,e):void 0}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}();function tp(t,e){var n,o,i=t.parentElement(),a=document.body.createTextRange(),r=C.from(i.childNodes);for(n=0;n<r.length;n++)if(!th.isText(r[n])){if(a.moveToElementText(r[n]),a.compareEndPoints("StartToStart",t)>=0)break;o=r[n]}if(0!==n&&th.isText(r[n-1])){var s=document.body.createTextRange(),l=null;s.moveToElementText(o||i),s.collapse(!o),l=o?o.nextSibling:i.firstChild;var c=t.duplicate();c.setEndPoint("StartToStart",s);for(var u=c.text.replace(/[\r\n]/g,"").length;u>l.nodeValue.length&&l.nextSibling;)u-=l.nodeValue.length,l=l.nextSibling;l.nodeValue,e&&l.nextSibling&&th.isText(l.nextSibling)&&u===l.nodeValue.length&&(u-=l.nodeValue.length,l=l.nextSibling),i=l,n=u}return{cont:i,offset:n}}function tv(t){var e=document.body.createTextRange(),n=function t(e,n){var o,i;if(th.isText(e)){var a=th.listPrev(e,m.not(th.isText)),r=C.last(a).previousSibling;o=r||e.parentNode,n+=C.sum(C.tail(a),th.nodeLength),i=!r}else{if(o=e.childNodes[n]||e,th.isText(o))return t(o,0);n=0,i=!1}return{node:o,collapseToStart:i,offset:n}}(t.node,t.offset);return e.moveToElementText(n.node),e.collapse(n.collapseToStart),e.moveStart("character",n.offset),e}i.a.fn.extend({summernote:function(){var t=i.a.type(C.head(arguments)),e=i.a.extend({},i.a.summernote.options,"object"===t?C.head(arguments):{});e.langInfo=i.a.extend(!0,{},i.a.summernote.lang["en-US"],i.a.summernote.lang[e.lang]),e.icons=i.a.extend(!0,{},i.a.summernote.options.icons,e.icons),e.tooltip="auto"===e.tooltip?!p.isSupportTouch:e.tooltip,this.each(function(t,n){var o=i()(n);if(!o.data("summernote")){var a=new tf(o,e);o.data("summernote",a),o.data("summernote").triggerEvent("init",a.layoutInfo)}});var n=this.first();if(n.length){var o=n.data("summernote");if("string"===t)return o.invoke.apply(o,C.from(arguments));e.focus&&o.invoke("editor.focus")}return this}});var tm=function(){var t,e;function n(t,e,o,i){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.sc=t,this.so=e,this.ec=o,this.eo=i,this.isOnEditable=this.makeIsOn(th.isEditable),this.isOnList=this.makeIsOn(th.isList),this.isOnAnchor=this.makeIsOn(th.isAnchor),this.isOnCell=this.makeIsOn(th.isCell),this.isOnData=this.makeIsOn(th.isData)}return t=n,e=[{key:"nativeRange",value:function(){if(p.isW3CRangeSupport){var t=document.createRange();return t.setStart(this.sc,this.so),t.setEnd(this.ec,this.eo),t}var e=tv({node:this.sc,offset:this.so});return e.setEndPoint("EndToEnd",tv({node:this.ec,offset:this.eo})),e}},{key:"getPoints",value:function(){return{sc:this.sc,so:this.so,ec:this.ec,eo:this.eo}}},{key:"getStartPoint",value:function(){return{node:this.sc,offset:this.so}}},{key:"getEndPoint",value:function(){return{node:this.ec,offset:this.eo}}},{key:"select",value:function(){var t=this.nativeRange();if(p.isW3CRangeSupport){var e=document.getSelection();e.rangeCount>0&&e.removeAllRanges(),e.addRange(t)}else t.select();return this}},{key:"scrollIntoView",value:function(t){var e=i()(t).height();return t.scrollTop+e<this.sc.offsetTop&&(t.scrollTop+=Math.abs(t.scrollTop+e-this.sc.offsetTop)),this}},{key:"normalize",value:function(){var t=function(t,e){if(!t||th.isVisiblePoint(t)&&(!th.isEdgePoint(t)||th.isRightEdgePoint(t)&&!e||th.isLeftEdgePoint(t)&&e||th.isRightEdgePoint(t)&&e&&th.isVoid(t.node.nextSibling)||th.isLeftEdgePoint(t)&&!e&&th.isVoid(t.node.previousSibling)||th.isBlock(t.node)&&th.isEmpty(t.node)))return t;var n=th.ancestor(t.node,th.isBlock),o=!1;if(!o){var i=th.prevPoint(t)||{node:null};o=(th.isLeftEdgePointOf(t,n)||th.isVoid(i.node))&&!e}var a=!1;if(!a){var r=th.nextPoint(t)||{node:null};a=(th.isRightEdgePointOf(t,n)||th.isVoid(r.node))&&e}if(o||a){if(th.isVisiblePoint(t))return t;e=!e}return(e?th.nextPointUntil(th.nextPoint(t),th.isVisiblePoint):th.prevPointUntil(th.prevPoint(t),th.isVisiblePoint))||t},e=t(this.getEndPoint(),!1),o=this.isCollapsed()?e:t(this.getStartPoint(),!0);return new n(o.node,o.offset,e.node,e.offset)}},{key:"nodes",value:function(t,e){t=t||m.ok;var n=e&&e.includeAncestor,o=e&&e.fullyContains,i=this.getStartPoint(),a=this.getEndPoint(),r=[],s=[];return th.walkPoint(i,a,function(e){var i;th.isEditable(e.node)||(o?(th.isLeftEdgePoint(e)&&s.push(e.node),th.isRightEdgePoint(e)&&C.contains(s,e.node)&&(i=e.node)):i=n?th.ancestor(e.node,t):e.node,i&&t(i)&&r.push(i))},!0),C.unique(r)}},{key:"commonAncestor",value:function(){return th.commonAncestor(this.sc,this.ec)}},{key:"expand",value:function(t){var e=th.ancestor(this.sc,t),o=th.ancestor(this.ec,t);if(!e&&!o)return new n(this.sc,this.so,this.ec,this.eo);var i=this.getPoints();return e&&(i.sc=e,i.so=0),o&&(i.ec=o,i.eo=th.nodeLength(o)),new n(i.sc,i.so,i.ec,i.eo)}},{key:"collapse",value:function(t){return t?new n(this.sc,this.so,this.sc,this.so):new n(this.ec,this.eo,this.ec,this.eo)}},{key:"splitText",value:function(){var t=this.sc===this.ec,e=this.getPoints();return th.isText(this.ec)&&!th.isEdgePoint(this.getEndPoint())&&this.ec.splitText(this.eo),th.isText(this.sc)&&!th.isEdgePoint(this.getStartPoint())&&(e.sc=this.sc.splitText(this.so),e.so=0,t&&(e.ec=e.sc,e.eo=this.eo-this.so)),new n(e.sc,e.so,e.ec,e.eo)}},{key:"deleteContents",value:function(){if(this.isCollapsed())return this;var t=this.splitText(),e=t.nodes(null,{fullyContains:!0}),o=th.prevPointUntil(t.getStartPoint(),function(t){return!C.contains(e,t.node)}),a=[];return i.a.each(e,function(t,e){var n=e.parentNode;o.node!==n&&1===th.nodeLength(n)&&a.push(n),th.remove(e,!1)}),i.a.each(a,function(t,e){th.remove(e,!1)}),new n(o.node,o.offset,o.node,o.offset).normalize()}},{key:"makeIsOn",value:function(t){return function(){var e=th.ancestor(this.sc,t);return!!e&&e===th.ancestor(this.ec,t)}}},{key:"isLeftEdgeOf",value:function(t){if(!th.isLeftEdgePoint(this.getStartPoint()))return!1;var e=th.ancestor(this.sc,t);return e&&th.isLeftEdgeOf(this.sc,e)}},{key:"isCollapsed",value:function(){return this.sc===this.ec&&this.so===this.eo}},{key:"wrapBodyInlineWithPara",value:function(){if(th.isBodyContainer(this.sc)&&th.isEmpty(this.sc))return this.sc.innerHTML=th.emptyPara,new n(this.sc.firstChild,0,this.sc.firstChild,0);var t,e=this.normalize();if(th.isParaInline(this.sc)||th.isPara(this.sc))return e;if(th.isInline(e.sc)){var o=th.listAncestor(e.sc,m.not(th.isInline));t=C.last(o),th.isInline(t)||(t=o[o.length-2]||e.sc.childNodes[e.so])}else t=e.sc.childNodes[e.so>0?e.so-1:0];if(t){var i=th.listPrev(t,th.isParaInline).reverse();if((i=i.concat(th.listNext(t.nextSibling,th.isParaInline))).length){var a=th.wrap(C.head(i),"p");th.appendChildNodes(a,C.tail(i))}}return this.normalize()}},{key:"insertNode",value:function(t){var e=this;(th.isText(t)||th.isInline(t))&&(e=this.wrapBodyInlineWithPara().deleteContents());var n=th.splitPoint(e.getStartPoint(),th.isInline(t));return n.rightNode?(n.rightNode.parentNode.insertBefore(t,n.rightNode),th.isEmpty(n.rightNode)&&th.isPara(t)&&n.rightNode.parentNode.removeChild(n.rightNode)):n.container.appendChild(t),t}},{key:"pasteHTML",value:function(t){t=i.a.trim(t);var e=i()("<div></div>").html(t)[0],n=C.from(e.childNodes),o=this,a=!1;return o.so>=0&&(n=n.reverse(),a=!0),n=n.map(function(t){return o.insertNode(t)}),a&&(n=n.reverse()),n}},{key:"toString",value:function(){var t=this.nativeRange();return p.isW3CRangeSupport?t.toString():t.text}},{key:"getWordRange",value:function(t){var e=this.getEndPoint();if(!th.isCharPoint(e))return this;var o=th.prevPointUntil(e,function(t){return!th.isCharPoint(t)});return t&&(e=th.nextPointUntil(e,function(t){return!th.isCharPoint(t)})),new n(o.node,o.offset,e.node,e.offset)}},{key:"getWordsRange",value:function(t){var e=this.getEndPoint(),o=function(t){return!th.isCharPoint(t)&&!th.isSpacePoint(t)};if(o(e))return this;var i=th.prevPointUntil(e,o);return t&&(e=th.nextPointUntil(e,o)),new n(i.node,i.offset,e.node,e.offset)}},{key:"getWordsMatchRange",value:function(t){var e=this.getEndPoint(),o=th.prevPointUntil(e,function(o){if(!th.isCharPoint(o)&&!th.isSpacePoint(o))return!0;var i=new n(o.node,o.offset,e.node,e.offset),a=t.exec(i.toString());return a&&0===a.index}),i=new n(o.node,o.offset,e.node,e.offset),a=i.toString(),r=t.exec(a);return r&&r[0].length===a.length?i:null}},{key:"bookmark",value:function(t){return{s:{path:th.makeOffsetPath(t,this.sc),offset:this.so},e:{path:th.makeOffsetPath(t,this.ec),offset:this.eo}}}},{key:"paraBookmark",value:function(t){return{s:{path:C.tail(th.makeOffsetPath(C.head(t),this.sc)),offset:this.so},e:{path:C.tail(th.makeOffsetPath(C.last(t),this.ec)),offset:this.eo}}}},{key:"getClientRects",value:function(){return this.nativeRange().getClientRects()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tg={create:function(t,e,n,o){if(4===arguments.length)return new tm(t,e,n,o);if(2===arguments.length)return new tm(t,e,n=t,o=e);var i=this.createFromSelection();if(!i&&1===arguments.length){var a=arguments[0];return th.isEditable(a)&&(a=a.lastChild),this.createFromBodyElement(a,th.emptyPara===arguments[0].innerHTML)}return i},createFromBodyElement:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.createFromNode(t).collapse(e)},createFromSelection:function(){var t,e,n,o;if(p.isW3CRangeSupport){var i=document.getSelection();if(!i||0===i.rangeCount||th.isBody(i.anchorNode))return null;var a=i.getRangeAt(0);t=a.startContainer,e=a.startOffset,n=a.endContainer,o=a.endOffset}else{var r=document.selection.createRange(),s=r.duplicate();s.collapse(!1);var l=r;l.collapse(!0);var c=tp(l,!0),u=tp(s,!1);th.isText(c.node)&&th.isLeftEdgePoint(c)&&th.isTextNode(u.node)&&th.isRightEdgePoint(u)&&u.node.nextSibling===c.node&&(c=u),t=c.cont,e=c.offset,n=u.cont,o=u.offset}return new tm(t,e,n,o)},createFromNode:function(t){var e=t,n=0,o=t,i=th.nodeLength(o);return th.isVoid(e)&&(n=th.listPrev(e).length-1,e=e.parentNode),th.isBR(o)?(i=th.listPrev(o).length-1,o=o.parentNode):th.isVoid(o)&&(i=th.listPrev(o).length,o=o.parentNode),this.create(e,n,o,i)},createFromNodeBefore:function(t){return this.createFromNode(t).collapse(!0)},createFromNodeAfter:function(t){return this.createFromNode(t).collapse()},createFromBookmark:function(t,e){var n=th.fromOffsetPath(t,e.s.path),o=e.s.offset,i=th.fromOffsetPath(t,e.e.path),a=e.e.offset;return new tm(n,o,i,a)},createFromParaBookmark:function(t,e){var n=t.s.offset,o=t.e.offset,i=th.fromOffsetPath(C.head(e),t.s.path),a=th.fromOffsetPath(C.last(e),t.e.path);return new tm(i,n,a,o)}},tb={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,SPACE:32,DELETE:46,LEFT:37,UP:38,RIGHT:39,DOWN:40,NUM0:48,NUM1:49,NUM2:50,NUM3:51,NUM4:52,NUM5:53,NUM6:54,NUM7:55,NUM8:56,B:66,E:69,I:73,J:74,K:75,L:76,R:82,S:83,U:85,V:86,Y:89,Z:90,SLASH:191,LEFTBRACKET:219,BACKSLASH:220,RIGHTBRACKET:221,HOME:36,END:35,PAGEUP:33,PAGEDOWN:34},tk={isEdit:function(t){return C.contains([tb.BACKSPACE,tb.TAB,tb.ENTER,tb.SPACE,tb.DELETE],t)},isMove:function(t){return C.contains([tb.LEFT,tb.UP,tb.RIGHT,tb.DOWN],t)},isNavigation:function(t){return C.contains([tb.HOME,tb.END,tb.PAGEUP,tb.PAGEDOWN],t)},nameFromCode:m.invertObject(tb),code:tb},ty=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.stack=[],this.stackOffset=-1,this.context=t,this.$editable=t.layoutInfo.editable,this.editable=this.$editable[0]}return t=n,e=[{key:"makeSnapshot",value:function(){var t=tg.create(this.editable);return{contents:this.$editable.html(),bookmark:t&&t.isOnEditable()?t.bookmark(this.editable):{s:{path:[],offset:0},e:{path:[],offset:0}}}}},{key:"applySnapshot",value:function(t){null!==t.contents&&this.$editable.html(t.contents),null!==t.bookmark&&tg.createFromBookmark(this.editable,t.bookmark).select()}},{key:"rewind",value:function(){this.$editable.html()!==this.stack[this.stackOffset].contents&&this.recordUndo(),this.stackOffset=0,this.applySnapshot(this.stack[this.stackOffset])}},{key:"commit",value:function(){this.stack=[],this.stackOffset=-1,this.recordUndo()}},{key:"reset",value:function(){this.stack=[],this.stackOffset=-1,this.$editable.html(""),this.recordUndo()}},{key:"undo",value:function(){this.$editable.html()!==this.stack[this.stackOffset].contents&&this.recordUndo(),this.stackOffset>0&&(this.stackOffset--,this.applySnapshot(this.stack[this.stackOffset]))}},{key:"redo",value:function(){this.stack.length-1>this.stackOffset&&(this.stackOffset++,this.applySnapshot(this.stack[this.stackOffset]))}},{key:"recordUndo",value:function(){this.stackOffset++,this.stack.length>this.stackOffset&&(this.stack=this.stack.slice(0,this.stackOffset)),this.stack.push(this.makeSnapshot()),this.stack.length>this.context.options.historyLimit&&(this.stack.shift(),this.stackOffset-=1)}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tC=function(){var t,e;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n)}return t=n,e=[{key:"jQueryCSS",value:function(t,e){if(p.jqueryVersion<1.9){var n={};return i.a.each(e,function(e,o){n[o]=t.css(o)}),n}return t.css(e)}},{key:"fromNode",value:function(t){var e=this.jQueryCSS(t,["font-family","font-size","text-align","list-style-type","line-height"])||{},n=t[0].style.fontSize||e["font-size"];return e["font-size"]=parseInt(n,10),e["font-size-unit"]=n.match(/[a-z%]+$/),e}},{key:"stylePara",value:function(t,e){i.a.each(t.nodes(th.isPara,{includeAncestor:!0}),function(t,n){i()(n).css(e)})}},{key:"styleNodes",value:function(t,e){t=t.splitText();var n=e&&e.nodeName||"SPAN",o=!(!e||!e.expandClosestSibling),a=!(!e||!e.onlyPartialContains);if(t.isCollapsed())return[t.insertNode(th.create(n))];var r=th.makePredByNodeName(n),s=t.nodes(th.isText,{fullyContains:!0}).map(function(t){return th.singleChildAncestor(t,r)||th.wrap(t,n)});if(o){if(a){var l=t.nodes();r=m.and(r,function(t){return C.contains(l,t)})}return s.map(function(t){var e=th.withClosestSiblings(t,r),n=C.head(e),o=C.tail(e);return i.a.each(o,function(t,e){th.appendChildNodes(n,e.childNodes),th.remove(e)}),C.head(e)})}return s}},{key:"current",value:function(t){var e=i()(th.isElement(t.sc)?t.sc:t.sc.parentNode),n=this.fromNode(e);try{n=i.a.extend(n,{"font-bold":document.queryCommandState("bold")?"bold":"normal","font-italic":document.queryCommandState("italic")?"italic":"normal","font-underline":document.queryCommandState("underline")?"underline":"normal","font-subscript":document.queryCommandState("subscript")?"subscript":"normal","font-superscript":document.queryCommandState("superscript")?"superscript":"normal","font-strikethrough":document.queryCommandState("strikethrough")?"strikethrough":"normal","font-family":document.queryCommandValue("fontname")||n["font-family"]})}catch(o){}if(t.isOnList()){var a=["circle","disc","disc-leading-zero","square"].indexOf(n["list-style-type"])>-1;n["list-style"]=a?"unordered":"ordered"}else n["list-style"]="none";var r=th.ancestor(t.sc,th.isPara);if(r&&r.style["line-height"])n["line-height"]=r.style.lineHeight;else{var s=parseInt(n["line-height"],10)/parseInt(n["font-size"],10);n["line-height"]=s.toFixed(1)}return n.anchor=t.isOnAnchor()&&th.ancestor(t.sc,th.isAnchor),n.ancestors=th.listAncestor(t.sc,th.isEditable),n.range=t,n}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tw=function(){var t,e;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n)}return t=n,e=[{key:"insertOrderedList",value:function(t){this.toggleList("OL",t)}},{key:"insertUnorderedList",value:function(t){this.toggleList("UL",t)}},{key:"indent",value:function(t){var e=this,n=tg.create(t).wrapBodyInlineWithPara(),o=n.nodes(th.isPara,{includeAncestor:!0}),a=C.clusterBy(o,m.peq2("parentNode"));i.a.each(a,function(t,n){var o=C.head(n);if(th.isLi(o)){var a=e.findList(o.previousSibling);a?n.map(function(t){return a.appendChild(t)}):(e.wrapList(n,o.parentNode.nodeName),n.map(function(t){return t.parentNode}).map(function(t){return e.appendToPrevious(t)}))}else i.a.each(n,function(t,e){i()(e).css("marginLeft",function(t,e){return(parseInt(e,10)||0)+25})})}),n.select()}},{key:"outdent",value:function(t){var e=this,n=tg.create(t).wrapBodyInlineWithPara(),o=n.nodes(th.isPara,{includeAncestor:!0}),a=C.clusterBy(o,m.peq2("parentNode"));i.a.each(a,function(t,n){var o=C.head(n);th.isLi(o)?e.releaseList([n]):i.a.each(n,function(t,e){i()(e).css("marginLeft",function(t,e){return(e=parseInt(e,10)||0)>25?e-25:""})})}),n.select()}},{key:"toggleList",value:function(t,e){var n=this,o=tg.create(e).wrapBodyInlineWithPara(),a=o.nodes(th.isPara,{includeAncestor:!0}),r=o.paraBookmark(a),s=C.clusterBy(a,m.peq2("parentNode"));if(C.find(a,th.isPurePara)){var l=[];i.a.each(s,function(e,o){l=l.concat(n.wrapList(o,t))}),a=l}else{var c=o.nodes(th.isList,{includeAncestor:!0}).filter(function(e){return!i.a.nodeName(e,t)});c.length?i.a.each(c,function(e,n){th.replace(n,t)}):a=this.releaseList(s,!0)}tg.createFromParaBookmark(r,a).select()}},{key:"wrapList",value:function(t,e){var n=C.head(t),o=C.last(t),i=th.isList(n.previousSibling)&&n.previousSibling,a=th.isList(o.nextSibling)&&o.nextSibling,r=i||th.insertAfter(th.create(e||"UL"),o);return t=t.map(function(t){return th.isPurePara(t)?th.replace(t,"LI"):t}),th.appendChildNodes(r,t),a&&(th.appendChildNodes(r,C.from(a.childNodes)),th.remove(a)),t}},{key:"releaseList",value:function(t,e){var n=this,o=[];return i.a.each(t,function(t,a){var r=C.head(a),s=C.last(a),l=e?th.lastAncestor(r,th.isList):r.parentNode,c=l.parentNode;if("LI"===l.parentNode.nodeName)a.map(function(t){var e=n.findNextSiblings(t);c.nextSibling?c.parentNode.insertBefore(t,c.nextSibling):c.parentNode.appendChild(t),e.length&&(n.wrapList(e,l.nodeName),t.appendChild(e[0].parentNode))}),0===l.children.length&&c.removeChild(l),0===c.childNodes.length&&c.parentNode.removeChild(c);else{var u=l.childNodes.length>1?th.splitTree(l,{node:s.parentNode,offset:th.position(s)+1},{isSkipPaddingBlankHTML:!0}):null,d=th.splitTree(l,{node:r.parentNode,offset:th.position(r)},{isSkipPaddingBlankHTML:!0});a=e?th.listDescendant(d,th.isLi):C.from(d.childNodes).filter(th.isLi),!e&&th.isList(l.parentNode)||(a=a.map(function(t){return th.replace(t,"P")})),i.a.each(C.from(a).reverse(),function(t,e){th.insertAfter(e,l)});var h=C.compact([l,d,u]);i.a.each(h,function(t,e){var n=[e].concat(th.listDescendant(e,th.isList));i.a.each(n.reverse(),function(t,e){th.nodeLength(e)||th.remove(e,!0)})})}o=o.concat(a)}),o}},{key:"appendToPrevious",value:function(t){return t.previousSibling?th.appendChildNodes(t.previousSibling,[t]):this.wrapList([t],"LI")}},{key:"findList",value:function(t){return t?C.find(t.children,function(t){return["OL","UL"].indexOf(t.nodeName)>-1}):null}},{key:"findNextSiblings",value:function(t){for(var e=[];t.nextSibling;)e.push(t.nextSibling),t=t.nextSibling;return e}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tx=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.bullet=new tw,this.options=t.options}return t=n,e=[{key:"insertTab",value:function(t,e){var n=th.createText(Array(e+1).join(th.NBSP_CHAR));(t=t.deleteContents()).insertNode(n,!0),(t=tg.create(n,e)).select()}},{key:"insertParagraph",value:function(t,e){e=(e=(e=e||tg.create(t)).deleteContents()).wrapBodyInlineWithPara();var n,o=th.ancestor(e.sc,th.isPara);if(o){if(th.isLi(o)&&(th.isEmpty(o)||th.deepestChildIsEmpty(o)))return void this.bullet.toggleList(o.parentNode.nodeName);var a=null;if(1===this.options.blockquoteBreakingLevel?a=th.ancestor(o,th.isBlockquote):2===this.options.blockquoteBreakingLevel&&(a=th.lastAncestor(o,th.isBlockquote)),a){n=i()(th.emptyPara)[0],th.isRightEdgePoint(e.getStartPoint())&&th.isBR(e.sc.nextSibling)&&i()(e.sc.nextSibling).remove();var r=th.splitTree(a,e.getStartPoint(),{isDiscardEmptySplits:!0});r?r.parentNode.insertBefore(n,r):th.insertAfter(n,a)}else{n=th.splitTree(o,e.getStartPoint());var s=th.listDescendant(o,th.isEmptyAnchor);s=s.concat(th.listDescendant(n,th.isEmptyAnchor)),i.a.each(s,function(t,e){th.remove(e)}),(th.isHeading(n)||th.isPre(n)||th.isCustomStyleTag(n))&&th.isEmpty(n)&&(n=th.replace(n,"p"))}}else{var l=e.sc.childNodes[e.so];n=i()(th.emptyPara)[0],l?e.sc.insertBefore(n,l):e.sc.appendChild(n)}tg.create(n,0).normalize().select().scrollIntoView(t)}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),t$=function t(e,n,o,i){var a={colPos:0,rowPos:0},r=[],s=[];function l(t,e,n,o,i,a,s){r[t]||(r[t]=[]),r[t][e]={baseRow:n,baseCell:o,isRowSpan:i,isColSpan:a,isVirtual:s}}function c(t,e,n,o){return{baseCell:t.baseCell,action:e,virtualTable:{rowIndex:n,cellIndex:o}}}function u(t,e){if(!r[t]||!r[t][e])return e;for(var n=e;r[t][n];)if(n++,!r[t][n])return n}function d(t,e){var n=u(t.rowIndex,e.cellIndex),o=e.colSpan>1,i=e.rowSpan>1,r=t.rowIndex===a.rowPos&&e.cellIndex===a.colPos;l(t.rowIndex,n,t,e,i,o,!1);var s=e.attributes.rowSpan?parseInt(e.attributes.rowSpan.value,10):0;if(s>1)for(var c=1;c<s;c++){var d=t.rowIndex+c;h(d,n,e,r),l(d,n,t,e,!0,o,!0)}var f=e.attributes.colSpan?parseInt(e.attributes.colSpan.value,10):0;if(f>1)for(var p=1;p<f;p++){var v=u(t.rowIndex,n+p);h(t.rowIndex,v,e,r),l(t.rowIndex,v,t,e,i,!0,!0)}}function h(t,e,n,o){t===a.rowPos&&a.colPos>=n.cellIndex&&n.cellIndex<=e&&!o&&a.colPos++}function f(e){switch(n){case t.where.Column:if(e.isColSpan)return t.resultAction.SubtractSpanCount;break;case t.where.Row:if(!e.isVirtual&&e.isRowSpan)return t.resultAction.AddCell;if(e.isRowSpan)return t.resultAction.SubtractSpanCount}return t.resultAction.RemoveCell}function p(e){switch(n){case t.where.Column:if(e.isColSpan)return t.resultAction.SumSpanCount;if(e.isRowSpan&&e.isVirtual)return t.resultAction.Ignore;break;case t.where.Row:if(e.isRowSpan)return t.resultAction.SumSpanCount;if(e.isColSpan&&e.isVirtual)return t.resultAction.Ignore}return t.resultAction.AddCell}this.getActionList=function(){for(var e=n===t.where.Row?a.rowPos:-1,i=n===t.where.Column?a.colPos:-1,l=0,u=!0;u;){var d=e>=0?e:l,h=i>=0?i:l,v=r[d];if(!v)return u=!1,s;var m=v[h];if(!m)return u=!1,s;var g=t.resultAction.Ignore;switch(o){case t.requestAction.Add:g=p(m);break;case t.requestAction.Delete:g=f(m)}s.push(c(m,g,d,h)),l++}return s},e&&e.tagName&&("td"===e.tagName.toLowerCase()||"th"===e.tagName.toLowerCase())&&(a.colPos=e.cellIndex,e.parentElement&&e.parentElement.tagName&&"tr"===e.parentElement.tagName.toLowerCase()&&(a.rowPos=e.parentElement.rowIndex)),function(){for(var t=i.rows,e=0;e<t.length;e++)for(var n=t[e].cells,o=0;o<n.length;o++)d(t[e],n[o])}()};t$.where={Row:0,Column:1},t$.requestAction={Add:0,Delete:1},t$.resultAction={Ignore:0,SubtractSpanCount:1,RemoveCell:2,AddCell:3,SumSpanCount:4};var tS=function(){var t,e;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n)}return t=n,e=[{key:"tab",value:function(t,e){var n=th.ancestor(t.commonAncestor(),th.isCell),o=th.ancestor(n,th.isTable),i=th.listDescendant(o,th.isCell),a=C[e?"prev":"next"](i,n);a&&tg.create(a,0).select()}},{key:"addRow",value:function(t,e){for(var n=th.ancestor(t.commonAncestor(),th.isCell),o=i()(n).closest("tr"),a=this.recoverAttributes(o),r=i()("<tr"+a+"></tr>"),s=new t$(n,t$.where.Row,t$.requestAction.Add,i()(o).closest("table")[0]).getActionList(),l=0;l<s.length;l++){var c=s[l],u=this.recoverAttributes(c.baseCell);switch(c.action){case t$.resultAction.AddCell:r.append("<td"+u+">"+th.blank+"</td>");break;case t$.resultAction.SumSpanCount:if("top"===e&&(c.baseCell.parent?c.baseCell.closest("tr").rowIndex:0)<=o[0].rowIndex){var d=i()("<div></div>").append(i()("<td"+u+">"+th.blank+"</td>").removeAttr("rowspan")).html();r.append(d);break}var h=parseInt(c.baseCell.rowSpan,10);h++,c.baseCell.setAttribute("rowSpan",h)}}if("top"===e)o.before(r);else{if(n.rowSpan>1){var f=o[0].rowIndex+(n.rowSpan-2);return void i()(i()(o).parent().find("tr")[f]).after(i()(r))}o.after(r)}}},{key:"addCol",value:function(t,e){var n=th.ancestor(t.commonAncestor(),th.isCell),o=i()(n).closest("tr");i()(o).siblings().push(o);for(var a=new t$(n,t$.where.Column,t$.requestAction.Add,i()(o).closest("table")[0]).getActionList(),r=0;r<a.length;r++){var s=a[r],l=this.recoverAttributes(s.baseCell);switch(s.action){case t$.resultAction.AddCell:"right"===e?i()(s.baseCell).after("<td"+l+">"+th.blank+"</td>"):i()(s.baseCell).before("<td"+l+">"+th.blank+"</td>");break;case t$.resultAction.SumSpanCount:if("right"===e){var c=parseInt(s.baseCell.colSpan,10);c++,s.baseCell.setAttribute("colSpan",c)}else i()(s.baseCell).before("<td"+l+">"+th.blank+"</td>")}}}},{key:"recoverAttributes",value:function(t){var e="";if(!t)return e;for(var n=t.attributes||[],o=0;o<n.length;o++)"id"!==n[o].name.toLowerCase()&&n[o].specified&&(e+=" "+n[o].name+"='"+n[o].value+"'");return e}},{key:"deleteRow",value:function(t){for(var e=th.ancestor(t.commonAncestor(),th.isCell),n=i()(e).closest("tr"),o=n.children("td, th").index(i()(e)),a=n[0].rowIndex,r=new t$(e,t$.where.Row,t$.requestAction.Delete,i()(n).closest("table")[0]).getActionList(),s=0;s<r.length;s++)if(r[s]){var l=r[s].baseCell,c=r[s].virtualTable,u=l.rowSpan&&l.rowSpan>1,d=u?parseInt(l.rowSpan,10):0;switch(r[s].action){case t$.resultAction.Ignore:continue;case t$.resultAction.AddCell:var h=n.next("tr")[0];if(!h)continue;var f=n[0].cells[o];u&&(d>2?(d--,h.insertBefore(f,h.cells[o]),h.cells[o].setAttribute("rowSpan",d),h.cells[o].innerHTML=""):2===d&&(h.insertBefore(f,h.cells[o]),h.cells[o].removeAttribute("rowSpan"),h.cells[o].innerHTML=""));continue;case t$.resultAction.SubtractSpanCount:u&&(d>2?(d--,l.setAttribute("rowSpan",d),c.rowIndex!==a&&l.cellIndex===o&&(l.innerHTML="")):2===d&&(l.removeAttribute("rowSpan"),c.rowIndex!==a&&l.cellIndex===o&&(l.innerHTML="")));continue;case t$.resultAction.RemoveCell:continue}}n.remove()}},{key:"deleteCol",value:function(t){for(var e=th.ancestor(t.commonAncestor(),th.isCell),n=i()(e).closest("tr"),o=n.children("td, th").index(i()(e)),a=new t$(e,t$.where.Column,t$.requestAction.Delete,i()(n).closest("table")[0]).getActionList(),r=0;r<a.length;r++)if(a[r])switch(a[r].action){case t$.resultAction.Ignore:continue;case t$.resultAction.SubtractSpanCount:var s=a[r].baseCell;if(s.colSpan&&s.colSpan>1){var l=s.colSpan?parseInt(s.colSpan,10):0;l>2?(l--,s.setAttribute("colSpan",l),s.cellIndex===o&&(s.innerHTML="")):2===l&&(s.removeAttribute("colSpan"),s.cellIndex===o&&(s.innerHTML=""))}continue;case t$.resultAction.RemoveCell:th.remove(a[r].baseCell,!0);continue}}},{key:"createTable",value:function(t,e,n){for(var o,a=[],r=0;r<t;r++)a.push("<td>"+th.blank+"</td>");o=a.join("");for(var s,l=[],c=0;c<e;c++)l.push("<tr>"+o+"</tr>");s=l.join("");var u=i()("<table>"+s+"</table>");return n&&n.tableClassName&&u.addClass(n.tableClassName),u[0]}},{key:"deleteTable",value:function(t){var e=th.ancestor(t.commonAncestor(),th.isCell);i()(e).closest("table").remove()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),t9=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.$note=t.layoutInfo.note,this.$editor=t.layoutInfo.editor,this.$editable=t.layoutInfo.editable,this.options=t.options,this.lang=this.options.langInfo,this.editable=this.$editable[0],this.lastRange=null,this.snapshot=null,this.style=new tC,this.table=new tS,this.typing=new tx(t),this.bullet=new tw,this.history=new ty(t),this.context.memo("help.escape",this.lang.help.escape),this.context.memo("help.undo",this.lang.help.undo),this.context.memo("help.redo",this.lang.help.redo),this.context.memo("help.tab",this.lang.help.tab),this.context.memo("help.untab",this.lang.help.untab),this.context.memo("help.insertParagraph",this.lang.help.insertParagraph),this.context.memo("help.insertOrderedList",this.lang.help.insertOrderedList),this.context.memo("help.insertUnorderedList",this.lang.help.insertUnorderedList),this.context.memo("help.indent",this.lang.help.indent),this.context.memo("help.outdent",this.lang.help.outdent),this.context.memo("help.formatPara",this.lang.help.formatPara),this.context.memo("help.insertHorizontalRule",this.lang.help.insertHorizontalRule),this.context.memo("help.fontName",this.lang.help.fontName);for(var o=["bold","italic","underline","strikethrough","superscript","subscript","justifyLeft","justifyCenter","justifyRight","justifyFull","formatBlock","removeFormat","backColor"],a=0,r=o.length;a<r;a++)this[o[a]]=function(t){return function(n){e.beforeCommand(),document.execCommand(t,!1,n),e.afterCommand(!0)}}(o[a]),this.context.memo("help."+o[a],this.lang.help[o[a]]);this.fontName=this.wrapCommand(function(t){return e.fontStyling("font-family",p.validFontName(t))}),this.fontSize=this.wrapCommand(function(t){var n=e.currentStyle()["font-size-unit"];return e.fontStyling("font-size",t+n)}),this.fontSizeUnit=this.wrapCommand(function(t){var n=e.currentStyle()["font-size"];return e.fontStyling("font-size",n+t)});for(var s=1;s<=6;s++)this["formatH"+s]=function(t){return function(){e.formatBlock("H"+t)}}(s),this.context.memo("help.formatH"+s,this.lang.help["formatH"+s]);this.insertParagraph=this.wrapCommand(function(){e.typing.insertParagraph(e.editable)}),this.insertOrderedList=this.wrapCommand(function(){e.bullet.insertOrderedList(e.editable)}),this.insertUnorderedList=this.wrapCommand(function(){e.bullet.insertUnorderedList(e.editable)}),this.indent=this.wrapCommand(function(){e.bullet.indent(e.editable)}),this.outdent=this.wrapCommand(function(){e.bullet.outdent(e.editable)}),this.insertNode=this.wrapCommand(function(t){e.isLimited(i()(t).text().length)||(e.getLastRange().insertNode(t),e.setLastRange(tg.createFromNodeAfter(t).select()))}),this.insertText=this.wrapCommand(function(t){if(!e.isLimited(t.length)){var n=e.getLastRange().insertNode(th.createText(t));e.setLastRange(tg.create(n,th.nodeLength(n)).select())}}),this.pasteHTML=this.wrapCommand(function(t){if(!e.isLimited(t.length)){t=e.context.invoke("codeview.purify",t);var n=e.getLastRange().pasteHTML(t);e.setLastRange(tg.createFromNodeAfter(C.last(n)).select())}}),this.formatBlock=this.wrapCommand(function(t,n){var o=e.options.callbacks.onApplyCustomStyle;o?o.call(e,n,e.context,e.onFormatBlock):e.onFormatBlock(t,n)}),this.insertHorizontalRule=this.wrapCommand(function(){var t=e.getLastRange().insertNode(th.create("HR"));t.nextSibling&&e.setLastRange(tg.create(t.nextSibling,0).normalize().select())}),this.lineHeight=this.wrapCommand(function(t){e.style.stylePara(e.getLastRange(),{lineHeight:t})}),this.createLink=this.wrapCommand(function(t){var n=t.url,o=t.text,a=t.isNewWindow,r=t.checkProtocol,s=t.range||e.getLastRange(),l=o.length-s.toString().length;if(!(l>0&&e.isLimited(l))){var c=s.toString()!==o;"string"==typeof n&&(n=n.trim()),e.options.onCreateLink?n=e.options.onCreateLink(n):r&&(n=/^([A-Za-z][A-Za-z0-9+-.]*\:|#|\/)/.test(n)?n:e.options.defaultProtocol+n);var u=[];if(c){var d=(s=s.deleteContents()).insertNode(i()("<A>"+o+"</A>")[0]);u.push(d)}else u=e.style.styleNodes(s,{nodeName:"A",expandClosestSibling:!0,onlyPartialContains:!0});i.a.each(u,function(t,e){i()(e).attr("href",n),a?i()(e).attr("target","_blank"):i()(e).removeAttr("target")}),e.setLastRange(e.createRangeFromList(u).select())}}),this.color=this.wrapCommand(function(t){var e=t.foreColor,n=t.backColor;e&&document.execCommand("foreColor",!1,e),n&&document.execCommand("backColor",!1,n)}),this.foreColor=this.wrapCommand(function(t){document.execCommand("foreColor",!1,t)}),this.insertTable=this.wrapCommand(function(t){var n=t.split("x");e.getLastRange().deleteContents().insertNode(e.table.createTable(n[0],n[1],e.options))}),this.removeMedia=this.wrapCommand(function(){var t=i()(e.restoreTarget()).parent();t.closest("figure").length?t.closest("figure").remove():t=i()(e.restoreTarget()).detach(),e.context.triggerEvent("media.delete",t,e.$editable)}),this.floatMe=this.wrapCommand(function(t){var n=i()(e.restoreTarget());n.toggleClass("note-float-left","left"===t),n.toggleClass("note-float-right","right"===t),n.css("float","none"===t?"":t)}),this.resize=this.wrapCommand(function(t){var n=i()(e.restoreTarget());0===(t=parseFloat(t))?n.css("width",""):n.css({width:100*t+"%",height:""})})}return t=n,e=[{key:"initialize",value:function(){var t=this;this.$editable.on("keydown",function(e){if(e.keyCode===tk.code.ENTER&&t.context.triggerEvent("enter",e),t.context.triggerEvent("keydown",e),t.snapshot=t.history.makeSnapshot(),t.hasKeyShortCut=!1,e.isDefaultPrevented()||(t.options.shortcuts?t.hasKeyShortCut=t.handleKeyMap(e):t.preventDefaultEditableShortCuts(e)),t.isLimited(1,e)){var n=t.getLastRange();if(n.eo-n.so==0)return!1}t.setLastRange(),t.options.recordEveryKeystroke&&!1===t.hasKeyShortCut&&t.history.recordUndo()}).on("keyup",function(e){t.setLastRange(),t.context.triggerEvent("keyup",e)}).on("focus",function(e){t.setLastRange(),t.context.triggerEvent("focus",e)}).on("blur",function(e){t.context.triggerEvent("blur",e)}).on("mousedown",function(e){t.context.triggerEvent("mousedown",e)}).on("mouseup",function(e){t.setLastRange(),t.history.recordUndo(),t.context.triggerEvent("mouseup",e)}).on("scroll",function(e){t.context.triggerEvent("scroll",e)}).on("paste",function(e){t.setLastRange(),t.context.triggerEvent("paste",e)}).on("input",function(){t.isLimited(0)&&t.snapshot&&t.history.applySnapshot(t.snapshot)}),this.$editable.attr("spellcheck",this.options.spellCheck),this.$editable.attr("autocorrect",this.options.spellCheck),this.options.disableGrammar&&this.$editable.attr("data-gramm",!1),this.$editable.html(th.html(this.$note)||th.emptyPara),this.$editable.on(p.inputEventName,m.debounce(function(){t.context.triggerEvent("change",t.$editable.html(),t.$editable)},10)),this.$editable.on("focusin",function(e){t.context.triggerEvent("focusin",e)}).on("focusout",function(e){t.context.triggerEvent("focusout",e)}),this.options.airMode?this.options.overrideContextMenu&&this.$editor.on("contextmenu",function(e){return t.context.triggerEvent("contextmenu",e),!1}):(this.options.width&&this.$editor.outerWidth(this.options.width),this.options.height&&this.$editable.outerHeight(this.options.height),this.options.maxHeight&&this.$editable.css("max-height",this.options.maxHeight),this.options.minHeight&&this.$editable.css("min-height",this.options.minHeight)),this.history.recordUndo(),this.setLastRange()}},{key:"destroy",value:function(){this.$editable.off()}},{key:"handleKeyMap",value:function(t){var e=this.options.keyMap[p.isMac?"mac":"pc"],n=[];t.metaKey&&n.push("CMD"),t.ctrlKey&&!t.altKey&&n.push("CTRL"),t.shiftKey&&n.push("SHIFT");var o=tk.nameFromCode[t.keyCode];o&&n.push(o);var i=e[n.join("+")];if("TAB"!==o||this.options.tabDisable){if(i){if(!1!==this.context.invoke(i))return t.preventDefault(),!0}else tk.isEdit(t.keyCode)&&this.afterCommand()}else this.afterCommand();return!1}},{key:"preventDefaultEditableShortCuts",value:function(t){(t.ctrlKey||t.metaKey)&&C.contains([66,73,85],t.keyCode)&&t.preventDefault()}},{key:"isLimited",value:function(t,e){return t=t||0,(void 0===e||!(tk.isMove(e.keyCode)||tk.isNavigation(e.keyCode)||e.ctrlKey||e.metaKey||C.contains([tk.code.BACKSPACE,tk.code.DELETE],e.keyCode)))&&this.options.maxTextLength>0&&this.$editable.text().length+t>this.options.maxTextLength}},{key:"createRange",value:function(){return this.focus(),this.setLastRange(),this.getLastRange()}},{key:"createRangeFromList",value:function(t){var e=tg.createFromNodeBefore(C.head(t)).getStartPoint(),n=tg.createFromNodeAfter(C.last(t)).getEndPoint();return tg.create(e.node,e.offset,n.node,n.offset)}},{key:"setLastRange",value:function(t){t?this.lastRange=t:(this.lastRange=tg.create(this.editable),0===i()(this.lastRange.sc).closest(".note-editable").length&&(this.lastRange=tg.createFromBodyElement(this.editable)))}},{key:"getLastRange",value:function(){return this.lastRange||this.setLastRange(),this.lastRange}},{key:"saveRange",value:function(t){t&&this.getLastRange().collapse().select()}},{key:"restoreRange",value:function(){this.lastRange&&(this.lastRange.select(),this.focus())}},{key:"saveTarget",value:function(t){this.$editable.data("target",t)}},{key:"clearTarget",value:function(){this.$editable.removeData("target")}},{key:"restoreTarget",value:function(){return this.$editable.data("target")}},{key:"currentStyle",value:function(){var t=tg.create();return t&&(t=t.normalize()),t?this.style.current(t):this.style.fromNode(this.$editable)}},{key:"styleFromNode",value:function(t){return this.style.fromNode(t)}},{key:"undo",value:function(){this.context.triggerEvent("before.command",this.$editable.html()),this.history.undo(),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}},{key:"commit",value:function(){this.context.triggerEvent("before.command",this.$editable.html()),this.history.commit(),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}},{key:"redo",value:function(){this.context.triggerEvent("before.command",this.$editable.html()),this.history.redo(),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}},{key:"beforeCommand",value:function(){this.context.triggerEvent("before.command",this.$editable.html()),document.execCommand("styleWithCSS",!1,this.options.styleWithCSS),this.focus()}},{key:"afterCommand",value:function(t){this.normalizeContent(),this.history.recordUndo(),t||this.context.triggerEvent("change",this.$editable.html(),this.$editable)}},{key:"tab",value:function(){var t=this.getLastRange();if(t.isCollapsed()&&t.isOnCell())this.table.tab(t);else{if(0===this.options.tabSize)return!1;this.isLimited(this.options.tabSize)||(this.beforeCommand(),this.typing.insertTab(t,this.options.tabSize),this.afterCommand())}}},{key:"untab",value:function(){var t=this.getLastRange();if(t.isCollapsed()&&t.isOnCell())this.table.tab(t,!0);else if(0===this.options.tabSize)return!1}},{key:"wrapCommand",value:function(t){return function(){this.beforeCommand(),t.apply(this,arguments),this.afterCommand()}}},{key:"insertImage",value:function(t,e){var n,o=this;return(n=t,i.a.Deferred(function(t){var e=i()("<img>");e.one("load",function(){e.off("error abort"),t.resolve(e)}).one("error abort",function(){e.off("load").detach(),t.reject(e)}).css({display:"none"}).appendTo(document.body).attr("src",n)}).promise()).then(function(t){o.beforeCommand(),"function"==typeof e?e(t):("string"==typeof e&&t.attr("data-filename",e),t.css("width",Math.min(o.$editable.width(),t.width()))),t.show(),o.getLastRange().insertNode(t[0]),o.setLastRange(tg.createFromNodeAfter(t[0]).select()),o.afterCommand()}).fail(function(t){o.context.triggerEvent("image.upload.error",t)})}},{key:"insertImagesAsDataURL",value:function(t){var e=this;i.a.each(t,function(t,n){var o,a=n.name;e.options.maximumImageFileSize&&e.options.maximumImageFileSize<n.size?e.context.triggerEvent("image.upload.error",e.lang.image.maximumFileSizeError):(o=n,i.a.Deferred(function(t){i.a.extend(new FileReader,{onload:function(e){var n=e.target.result;t.resolve(n)},onerror:function(e){t.reject(e)}}).readAsDataURL(o)}).promise()).then(function(t){return e.insertImage(t,a)}).fail(function(){e.context.triggerEvent("image.upload.error")})})}},{key:"insertImagesOrCallback",value:function(t){this.options.callbacks.onImageUpload?this.context.triggerEvent("image.upload",t):this.insertImagesAsDataURL(t)}},{key:"getSelectedText",value:function(){var t=this.getLastRange();return t.isOnAnchor()&&(t=tg.createFromNode(th.ancestor(t.sc,th.isAnchor))),t.toString()}},{key:"onFormatBlock",value:function(t,e){if(document.execCommand("FormatBlock",!1,p.isMSIE?"<"+t+">":t),e&&e.length&&(e[0].tagName.toUpperCase()!==t.toUpperCase()&&(e=e.find(t)),e&&e.length)){var n=e[0].className||"";if(n){var o=this.createRange();i()([o.sc,o.ec]).closest(t).addClass(n)}}}},{key:"formatPara",value:function(){this.formatBlock("P")}},{key:"fontStyling",value:function(t,e){var n=this.getLastRange();if(""!==n){var o=this.style.styleNodes(n);if(this.$editor.find(".note-status-output").html(""),i()(o).css(t,e),n.isCollapsed()){var a=C.head(o);a&&!th.nodeLength(a)&&(a.innerHTML=th.ZERO_WIDTH_NBSP_CHAR,tg.createFromNode(a.firstChild).select(),this.setLastRange(),this.$editable.data("bogus",a))}else this.setLastRange(this.createRangeFromList(o).select())}else{var r=i.a.now();this.$editor.find(".note-status-output").html('<div id="note-status-output-'+r+'" class="alert alert-info">'+this.lang.output.noSelection+"</div>"),setTimeout(function(){i()("#note-status-output-"+r).remove()},5e3)}}},{key:"unlink",value:function(){var t=this.getLastRange();if(t.isOnAnchor()){var e=th.ancestor(t.sc,th.isAnchor);(t=tg.createFromNode(e)).select(),this.setLastRange(),this.beforeCommand(),document.execCommand("unlink"),this.afterCommand()}}},{key:"getLinkInfo",value:function(){var t=this.getLastRange().expand(th.isAnchor),e=i()(C.head(t.nodes(th.isAnchor))),n={range:t,text:t.toString(),url:e.length?e.attr("href"):""};return e.length&&(n.isNewWindow="_blank"===e.attr("target")),n}},{key:"addRow",value:function(t){var e=this.getLastRange(this.$editable);e.isCollapsed()&&e.isOnCell()&&(this.beforeCommand(),this.table.addRow(e,t),this.afterCommand())}},{key:"addCol",value:function(t){var e=this.getLastRange(this.$editable);e.isCollapsed()&&e.isOnCell()&&(this.beforeCommand(),this.table.addCol(e,t),this.afterCommand())}},{key:"deleteRow",value:function(){var t=this.getLastRange(this.$editable);t.isCollapsed()&&t.isOnCell()&&(this.beforeCommand(),this.table.deleteRow(t),this.afterCommand())}},{key:"deleteCol",value:function(){var t=this.getLastRange(this.$editable);t.isCollapsed()&&t.isOnCell()&&(this.beforeCommand(),this.table.deleteCol(t),this.afterCommand())}},{key:"deleteTable",value:function(){var t=this.getLastRange(this.$editable);t.isCollapsed()&&t.isOnCell()&&(this.beforeCommand(),this.table.deleteTable(t),this.afterCommand())}},{key:"resizeTo",value:function(t,e,n){var o;if(n){var i=t.y/t.x,a=e.data("ratio");o={width:a>i?t.x:t.y/a,height:a>i?t.x*a:t.y}}else o={width:t.x,height:t.y};e.css(o)}},{key:"hasFocus",value:function(){return this.$editable.is(":focus")}},{key:"focus",value:function(){this.hasFocus()||this.$editable.focus()}},{key:"isEmpty",value:function(){return th.isEmpty(this.$editable[0])||th.emptyPara===this.$editable.html()}},{key:"empty",value:function(){this.context.invoke("code",th.emptyPara)}},{key:"normalizeContent",value:function(){this.$editable[0].normalize()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tN=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.$editable=t.layoutInfo.editable}return t=n,e=[{key:"initialize",value:function(){this.$editable.on("paste",this.pasteByEvent.bind(this))}},{key:"pasteByEvent",value:function(t){var e=this,n=t.originalEvent.clipboardData;if(n&&n.items&&n.items.length){var o=n.items.length>1?n.items[1]:C.head(n.items);"file"===o.kind&&-1!==o.type.indexOf("image/")?(this.context.invoke("editor.insertImagesOrCallback",[o.getAsFile()]),t.preventDefault()):"string"===o.kind&&this.context.invoke("editor.isLimited",n.getData("Text").length)&&t.preventDefault()}else if(window.clipboardData){var i=window.clipboardData.getData("text");this.context.invoke("editor.isLimited",i.length)&&t.preventDefault()}setTimeout(function(){e.context.invoke("editor.afterCommand")},10)}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tT=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.$eventListener=i()(document),this.$editor=t.layoutInfo.editor,this.$editable=t.layoutInfo.editable,this.options=t.options,this.lang=this.options.langInfo,this.documentEventHandlers={},this.$dropzone=i()('<div class="note-dropzone"><div class="note-dropzone-message"></div></div>').prependTo(this.$editor)}return t=n,e=[{key:"initialize",value:function(){this.options.disableDragAndDrop?(this.documentEventHandlers.onDrop=function(t){t.preventDefault()},this.$eventListener=this.$dropzone,this.$eventListener.on("drop",this.documentEventHandlers.onDrop)):this.attachDragAndDropEvent()}},{key:"attachDragAndDropEvent",value:function(){var t=this,e=i()(),n=this.$dropzone.find(".note-dropzone-message");this.documentEventHandlers.onDragenter=function(o){var i=t.context.invoke("codeview.isActivated"),a=t.$editor.width()>0&&t.$editor.height()>0;i||e.length||!a||(t.$editor.addClass("dragover"),t.$dropzone.width(t.$editor.width()),t.$dropzone.height(t.$editor.height()),n.text(t.lang.image.dragImageHere)),e=e.add(o.target)},this.documentEventHandlers.onDragleave=function(n){(e=e.not(n.target)).length&&"BODY"!==n.target.nodeName||(e=i()(),t.$editor.removeClass("dragover"))},this.documentEventHandlers.onDrop=function(){e=i()(),t.$editor.removeClass("dragover")},this.$eventListener.on("dragenter",this.documentEventHandlers.onDragenter).on("dragleave",this.documentEventHandlers.onDragleave).on("drop",this.documentEventHandlers.onDrop),this.$dropzone.on("dragenter",function(){t.$dropzone.addClass("hover"),n.text(t.lang.image.dropImage)}).on("dragleave",function(){t.$dropzone.removeClass("hover"),n.text(t.lang.image.dragImageHere)}),this.$dropzone.on("drop",function(e){var n=e.originalEvent.dataTransfer;e.preventDefault(),n&&n.files&&n.files.length?(t.$editable.focus(),t.context.invoke("editor.insertImagesOrCallback",n.files)):i.a.each(n.types,function(e,o){if(!(o.toLowerCase().indexOf("_moz_")>-1)){var a=n.getData(o);o.toLowerCase().indexOf("text")>-1?t.context.invoke("editor.pasteHTML",a):i()(a).each(function(e,n){t.context.invoke("editor.insertNode",n)})}})}).on("dragover",!1)}},{key:"destroy",value:function(){var t=this;Object.keys(this.documentEventHandlers).forEach(function(e){t.$eventListener.off(e.substr(2).toLowerCase(),t.documentEventHandlers[e])}),this.documentEventHandlers={}}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}();function t_(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=Array(e);n<e;n++)o[n]=t[n];return o}!function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.$editor=t.layoutInfo.editor,this.$editable=t.layoutInfo.editable,this.$codable=t.layoutInfo.codable,this.options=t.options,this.CodeMirrorConstructor=window.CodeMirror,this.options.codemirror.CodeMirrorConstructor&&(this.CodeMirrorConstructor=this.options.codemirror.CodeMirrorConstructor)}t=n,e=[{key:"sync",value:function(t){var e=this.isActivated(),n=this.CodeMirrorConstructor;e&&(t?n?this.$codable.data("cmEditor").getDoc().setValue(t):this.$codable.val(t):n&&this.$codable.data("cmEditor").save())}},{key:"initialize",value:function(){var t=this;this.$codable.on("keyup",function(e){e.keyCode===tk.code.ESCAPE&&t.deactivate()})}},{key:"isActivated",value:function(){return this.$editor.hasClass("codeview")}},{key:"toggle",value:function(){this.isActivated()?this.deactivate():this.activate(),this.context.triggerEvent("codeview.toggled")}},{key:"purify",value:function(t){if(this.options.codeviewFilter&&(t=t.replace(this.options.codeviewFilterRegex,""),this.options.codeviewIframeFilter)){var e=this.options.codeviewIframeWhitelistSrc.concat(this.options.codeviewIframeWhitelistSrcBase);t=t.replace(/(<iframe.*?>.*?(?:<\/iframe>)?)/gi,function(t){if(/<.+src(?==?('|"|\s)?)[\s\S]+src(?=('|"|\s)?)[^>]*?>/i.test(t))return"";var n,o=function(t){if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(t=function(t,e){if(t){if("string"==typeof t)return t_(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t_(t,e):void 0}}(t))){var e=0,n=function(){};return{s:n,n:function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}},e:function(t){throw t},f:n}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i,a=!0,r=!1;return{s:function(){o=t[Symbol.iterator]()},n:function(){var t=o.next();return a=t.done,t},e:function(t){r=!0,i=t},f:function(){try{a||null==o.return||o.return()}finally{if(r)throw i}}}}(e);try{for(o.s();!(n=o.n()).done;){var i=n.value;if(RegExp('src="(https?:)?//'+i.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")+'/(.+)"').test(t))return t}}catch(a){o.e(a)}finally{o.f()}return""})}return t}},{key:"activate",value:function(){var t=this,e=this.CodeMirrorConstructor;if(this.$codable.val(th.html(this.$editable,this.options.prettifyHtml)),this.$codable.height(this.$editable.height()),this.context.invoke("toolbar.updateCodeview",!0),this.context.invoke("airPopover.updateCodeview",!0),this.$editor.addClass("codeview"),this.$codable.focus(),e){var n=e.fromTextArea(this.$codable[0],this.options.codemirror);if(this.options.codemirror.tern){var o=new e.TernServer(this.options.codemirror.tern);n.ternServer=o,n.on("cursorActivity",function(t){o.updateArgHints(t)})}n.on("blur",function(e){t.context.triggerEvent("blur.codeview",n.getValue(),e)}),n.on("change",function(){t.context.triggerEvent("change.codeview",n.getValue(),n)}),n.setSize(null,this.$editable.outerHeight()),this.$codable.data("cmEditor",n)}else this.$codable.on("blur",function(e){t.context.triggerEvent("blur.codeview",t.$codable.val(),e)}),this.$codable.on("input",function(){t.context.triggerEvent("change.codeview",t.$codable.val(),t.$codable)})}},{key:"deactivate",value:function(){if(this.CodeMirrorConstructor){var t=this.$codable.data("cmEditor");this.$codable.val(t.getValue()),t.toTextArea()}var e=this.purify(th.value(this.$codable,this.options.prettifyHtml)||th.emptyPara),n=this.$editable.html()!==e;this.$editable.html(e),this.$editable.height(this.options.height?this.$codable.height():"auto"),this.$editor.removeClass("codeview"),n&&this.context.triggerEvent("change",this.$editable.html(),this.$editable),this.$editable.focus(),this.context.invoke("toolbar.updateCodeview",!1),this.context.invoke("airPopover.updateCodeview",!1)}},{key:"destroy",value:function(){this.isActivated()&&this.deactivate()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e)}();var tI=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.$document=i()(document),this.$statusbar=t.layoutInfo.statusbar,this.$editable=t.layoutInfo.editable,this.options=t.options}return t=n,e=[{key:"initialize",value:function(){var t=this;this.options.airMode||this.options.disableResizeEditor?this.destroy():this.$statusbar.on("mousedown",function(e){e.preventDefault(),e.stopPropagation();var n=t.$editable.offset().top-t.$document.scrollTop(),o=function(e){var o=e.clientY-(n+24);o=t.options.minheight>0?Math.max(o,t.options.minheight):o,o=t.options.maxHeight>0?Math.min(o,t.options.maxHeight):o,t.$editable.height(o)};t.$document.on("mousemove",o).one("mouseup",function(){t.$document.off("mousemove",o)})})}},{key:"destroy",value:function(){this.$statusbar.off(),this.$statusbar.addClass("locked")}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tE=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.$editor=t.layoutInfo.editor,this.$toolbar=t.layoutInfo.toolbar,this.$editable=t.layoutInfo.editable,this.$codable=t.layoutInfo.codable,this.$window=i()(window),this.$scrollbar=i()("html, body"),this.onResize=function(){e.resizeTo({h:e.$window.height()-e.$toolbar.outerHeight()})}}return t=n,e=[{key:"resizeTo",value:function(t){this.$editable.css("height",t.h),this.$codable.css("height",t.h),this.$codable.data("cmeditor")&&this.$codable.data("cmeditor").setsize(null,t.h)}},{key:"toggle",value:function(){this.$editor.toggleClass("fullscreen"),this.isFullscreen()?(this.$editable.data("orgHeight",this.$editable.css("height")),this.$editable.data("orgMaxHeight",this.$editable.css("maxHeight")),this.$editable.css("maxHeight",""),this.$window.on("resize",this.onResize).trigger("resize"),this.$scrollbar.css("overflow","hidden")):(this.$window.off("resize",this.onResize),this.resizeTo({h:this.$editable.data("orgHeight")}),this.$editable.css("maxHeight",this.$editable.css("orgMaxHeight")),this.$scrollbar.css("overflow","visible")),this.context.invoke("toolbar.updateFullscreen",this.isFullscreen())}},{key:"isFullscreen",value:function(){return this.$editor.hasClass("fullscreen")}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tP=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.$document=i()(document),this.$editingArea=t.layoutInfo.editingArea,this.options=t.options,this.lang=this.options.langInfo,this.events={"summernote.mousedown":function(t,n){e.update(n.target,n)&&n.preventDefault()},"summernote.keyup summernote.scroll summernote.change summernote.dialog.shown":function(){e.update()},"summernote.disable summernote.blur":function(){e.hide()},"summernote.codeview.toggled":function(){e.update()}}}return t=n,e=[{key:"initialize",value:function(){var t=this;this.$handle=i()(['<div class="note-handle">','<div class="note-control-selection">','<div class="note-control-selection-bg"></div>','<div class="note-control-holder note-control-nw"></div>','<div class="note-control-holder note-control-ne"></div>','<div class="note-control-holder note-control-sw"></div>','<div class="',this.options.disableResizeImage?"note-control-holder":"note-control-sizing",' note-control-se"></div>',this.options.disableResizeImage?"":'<div class="note-control-selection-info"></div>',"</div>","</div>",].join("")).prependTo(this.$editingArea),this.$handle.on("mousedown",function(e){if(th.isControlSizing(e.target)){e.preventDefault(),e.stopPropagation();var n=t.$handle.find(".note-control-selection").data("target"),o=n.offset(),i=t.$document.scrollTop(),a=function(e){t.context.invoke("editor.resizeTo",{x:e.clientX-o.left,y:e.clientY-(o.top-i)},n,!e.shiftKey),t.update(n[0],e)};t.$document.on("mousemove",a).one("mouseup",function(e){e.preventDefault(),t.$document.off("mousemove",a),t.context.invoke("editor.afterCommand")}),n.data("ratio")||n.data("ratio",n.height()/n.width())}}),this.$handle.on("wheel",function(e){e.preventDefault(),t.update()})}},{key:"destroy",value:function(){this.$handle.remove()}},{key:"update",value:function(t,e){if(this.context.isDisabled())return!1;var n=th.isImg(t),o=this.$handle.find(".note-control-selection");if(this.context.invoke("imagePopover.update",t,e),n){var a=i()(t),r=a.position(),s={left:r.left+parseInt(a.css("marginLeft"),10),top:r.top+parseInt(a.css("marginTop"),10)},l={w:a.outerWidth(!1),h:a.outerHeight(!1)};o.css({display:"block",left:s.left,top:s.top,width:l.w,height:l.h}).data("target",a);var c=new Image;c.src=a.attr("src");var u=l.w+"x"+l.h+" ("+this.lang.image.original+": "+c.width+"x"+c.height+")";o.find(".note-control-selection-info").text(u),this.context.invoke("editor.saveTarget",t)}else this.hide();return n}},{key:"hide",value:function(){this.context.invoke("editor.clearTarget"),this.$handle.children().hide()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tR=/^([A-Za-z][A-Za-z0-9+-.]*\:[\/]{2}|tel:|mailto:[A-Z0-9._%+-]+@)?(www\.)?(.+)$/i,tL=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.options=t.options,this.events={"summernote.keyup":function(t,n){n.isDefaultPrevented()||e.handleKeyup(n)},"summernote.keydown":function(t,n){e.handleKeydown(n)}}}return t=n,e=[{key:"initialize",value:function(){this.lastWordRange=null}},{key:"destroy",value:function(){this.lastWordRange=null}},{key:"replace",value:function(){if(this.lastWordRange){var t=this.lastWordRange.toString(),e=t.match(tR);if(e&&(e[1]||e[2])){var n=e[1]?t:"http://"+t,o=this.options.showDomainOnlyForAutolink?t.replace(/^(?:https?:\/\/)?(?:tel?:?)?(?:mailto?:?)?(?:www\.)?/i,"").split("/")[0]:t,a=i()("<a />").html(o).attr("href",n)[0];this.context.options.linkTargetBlank&&i()(a).attr("target","_blank"),this.lastWordRange.insertNode(a),this.lastWordRange=null,this.context.invoke("editor.focus")}}}},{key:"handleKeydown",value:function(t){if(C.contains([tk.code.ENTER,tk.code.SPACE],t.keyCode)){var e=this.context.invoke("editor.createRange").getWordRange();this.lastWordRange=e}}},{key:"handleKeyup",value:function(t){C.contains([tk.code.ENTER,tk.code.SPACE],t.keyCode)&&this.replace()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tA=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.$note=t.layoutInfo.note,this.events={"summernote.change":function(){e.$note.val(t.invoke("code"))}}}return t=n,e=[{key:"shouldInitialize",value:function(){return th.isTextarea(this.$note[0])}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tF=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.options=t.options.replace||{},this.keys=[tk.code.ENTER,tk.code.SPACE,tk.code.PERIOD,tk.code.COMMA,tk.code.SEMICOLON,tk.code.SLASH],this.previousKeydownCode=null,this.events={"summernote.keyup":function(t,n){n.isDefaultPrevented()||e.handleKeyup(n)},"summernote.keydown":function(t,n){e.handleKeydown(n)}}}return t=n,e=[{key:"shouldInitialize",value:function(){return!!this.options.match}},{key:"initialize",value:function(){this.lastWord=null}},{key:"destroy",value:function(){this.lastWord=null}},{key:"replace",value:function(){if(this.lastWord){var t=this,e=this.lastWord.toString();this.options.match(e,function(e){if(e){var n="";"string"==typeof e?n=th.createText(e):e instanceof jQuery?n=e[0]:e instanceof Node&&(n=e),n&&(t.lastWord.insertNode(n),t.lastWord=null,t.context.invoke("editor.focus"))}})}}},{key:"handleKeydown",value:function(t){if(this.previousKeydownCode&&C.contains(this.keys,this.previousKeydownCode))this.previousKeydownCode=t.keyCode;else{if(C.contains(this.keys,t.keyCode)){var e=this.context.invoke("editor.createRange").getWordRange();this.lastWord=e}this.previousKeydownCode=t.keyCode}}},{key:"handleKeyup",value:function(t){C.contains(this.keys,t.keyCode)&&this.replace()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tD=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.$editingArea=t.layoutInfo.editingArea,this.options=t.options,!0===this.options.inheritPlaceholder&&(this.options.placeholder=this.context.$note.attr("placeholder")||this.options.placeholder),this.events={"summernote.init summernote.change":function(){e.update()},"summernote.codeview.toggled":function(){e.update()}}}return t=n,e=[{key:"shouldInitialize",value:function(){return!!this.options.placeholder}},{key:"initialize",value:function(){var t=this;this.$placeholder=i()('<div class="note-placeholder">'),this.$placeholder.on("click",function(){t.context.invoke("focus")}).html(this.options.placeholder).prependTo(this.$editingArea),this.update()}},{key:"destroy",value:function(){this.$placeholder.remove()}},{key:"update",value:function(){var t=!this.context.invoke("codeview.isActivated")&&this.context.invoke("editor.isEmpty");this.$placeholder.toggle(t)}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tH=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.ui=i.a.summernote.ui,this.context=t,this.$toolbar=t.layoutInfo.toolbar,this.options=t.options,this.lang=this.options.langInfo,this.invertedKeyMap=m.invertObject(this.options.keyMap[p.isMac?"mac":"pc"])}return t=n,e=[{key:"representShortcut",value:function(t){var e=this.invertedKeyMap[t];return this.options.shortcuts&&e?(p.isMac&&(e=e.replace("CMD","⌘").replace("SHIFT","⇧"))," ("+(e=e.replace("BACKSLASH","\\").replace("SLASH","/").replace("LEFTBRACKET","[").replace("RIGHTBRACKET","]"))+")"):""}},{key:"button",value:function(t){return!this.options.tooltip&&t.tooltip&&delete t.tooltip,t.container=this.options.container,this.ui.button(t)}},{key:"initialize",value:function(){this.addToolbarButtons(),this.addImagePopoverButtons(),this.addLinkPopoverButtons(),this.addTablePopoverButtons(),this.fontInstalledMap={}}},{key:"destroy",value:function(){delete this.fontInstalledMap}},{key:"isFontInstalled",value:function(t){return Object.prototype.hasOwnProperty.call(this.fontInstalledMap,t)||(this.fontInstalledMap[t]=p.isFontInstalled(t)||C.contains(this.options.fontNamesIgnoreCheck,t)),this.fontInstalledMap[t]}},{key:"isFontDeservedToAdd",value:function(t){return""!==(t=t.toLowerCase())&&this.isFontInstalled(t)&&-1===p.genericFontFamilies.indexOf(t)}},{key:"colorPalette",value:function(t,e,n,o){var a=this;return this.ui.buttonGroup({className:"note-color "+t,children:[this.button({className:"note-current-color-button",contents:this.ui.icon(this.options.icons.font+" note-recent-color"),tooltip:e,click:function(t){var e=i()(t.currentTarget);n&&o?a.context.invoke("editor.color",{backColor:e.attr("data-backColor"),foreColor:e.attr("data-foreColor")}):n?a.context.invoke("editor.color",{backColor:e.attr("data-backColor")}):o&&a.context.invoke("editor.color",{foreColor:e.attr("data-foreColor")})},callback:function(t){var e=t.find(".note-recent-color");n&&(e.css("background-color",a.options.colorButton.backColor),t.attr("data-backColor",a.options.colorButton.backColor)),o?(e.css("color",a.options.colorButton.foreColor),t.attr("data-foreColor",a.options.colorButton.foreColor)):e.css("color","transparent")}}),this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents("",this.options),tooltip:this.lang.color.more,data:{toggle:"dropdown"}}),this.ui.dropdown({items:(n?['<div class="note-palette">','<div class="note-palette-title">'+this.lang.color.background+"</div>","<div>",'<button type="button" class="note-color-reset btn btn-light btn-default" data-event="backColor" data-value="transparent">',this.lang.color.transparent,"</button>","</div>",'<div class="note-holder" data-event="backColor"><!-- back colors --></div>',"<div>",'<button type="button" class="note-color-select btn btn-light btn-default" data-event="openPalette" data-value="backColorPicker">',this.lang.color.cpSelect,"</button>",'<input type="color" id="backColorPicker" class="note-btn note-color-select-btn" value="'+this.options.colorButton.backColor+'" data-event="backColorPalette">',"</div>",'<div class="note-holder-custom" id="backColorPalette" data-event="backColor"></div>',"</div>",].join(""):"")+(o?['<div class="note-palette">','<div class="note-palette-title">'+this.lang.color.foreground+"</div>","<div>",'<button type="button" class="note-color-reset btn btn-light btn-default" data-event="removeFormat" data-value="foreColor">',this.lang.color.resetToDefault,"</button>","</div>",'<div class="note-holder" data-event="foreColor"><!-- fore colors --></div>',"<div>",'<button type="button" class="note-color-select btn btn-light btn-default" data-event="openPalette" data-value="foreColorPicker">',this.lang.color.cpSelect,"</button>",'<input type="color" id="foreColorPicker" class="note-btn note-color-select-btn" value="'+this.options.colorButton.foreColor+'" data-event="foreColorPalette">',"</div>",'<div class="note-holder-custom" id="foreColorPalette" data-event="foreColor"></div>',"</div>",].join(""):""),callback:function(t){t.find(".note-holder").each(function(t,e){var n=i()(e);n.append(a.ui.palette({colors:a.options.colors,colorsName:a.options.colorsName,eventName:n.data("event"),container:a.options.container,tooltip:a.options.tooltip}).render())});var e=[["#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF"]];t.find(".note-holder-custom").each(function(t,n){var o=i()(n);o.append(a.ui.palette({colors:e,colorsName:e,eventName:o.data("event"),container:a.options.container,tooltip:a.options.tooltip}).render())}),t.find("input[type=color]").each(function(e,n){i()(n).change(function(){var e=t.find("#"+i()(this).data("event")).find(".note-color-btn").first(),n=this.value.toUpperCase();e.css("background-color",n).attr("aria-label",n).attr("data-value",n).attr("data-original-title",n),e.click()})})},click:function(e){e.stopPropagation();var n=i()("."+t).find(".note-dropdown-menu"),o=i()(e.target),r=o.data("event"),s=o.attr("data-value");if("openPalette"===r){var l=n.find("#"+s),c=i()(n.find("#"+l.data("event")).find(".note-color-row")[0]),u=c.find(".note-color-btn").last().detach(),d=l.val();u.css("background-color",d).attr("aria-label",d).attr("data-value",d).attr("data-original-title",d),c.prepend(u),l.click()}else{if(C.contains(["backColor","foreColor"],r)){var h=o.closest(".note-color").find(".note-recent-color"),f=o.closest(".note-color").find(".note-current-color-button");h.css("backColor"===r?"background-color":"color",s),f.attr("data-"+r,s)}a.context.invoke("editor."+r,s)}}}),]}).render()}},{key:"addToolbarButtons",value:function(){var t=this;this.context.memo("button.style",function(){return t.ui.buttonGroup([t.button({className:"dropdown-toggle",contents:t.ui.dropdownButtonContents(t.ui.icon(t.options.icons.magic),t.options),tooltip:t.lang.style.style,data:{toggle:"dropdown"}}),t.ui.dropdown({className:"dropdown-style",items:t.options.styleTags,title:t.lang.style.style,template:function(e){"string"==typeof e&&(e={tag:e,title:Object.prototype.hasOwnProperty.call(t.lang.style,e)?t.lang.style[e]:e});var n=e.tag,o=e.title;return"<"+n+(e.style?' style="'+e.style+'" ':"")+(e.className?' class="'+e.className+'"':"")+">"+o+"</"+n+">"},click:t.context.createInvokeHandler("editor.formatBlock")}),]).render()});for(var e=0,n=this.options.styleTags.length;e<n;e++)(function(e,n){var o=t.options.styleTags[e];t.context.memo("button.style."+o,function(){return t.button({className:"note-btn-style-"+o,contents:'<div data-value="'+o+'">'+o.toUpperCase()+"</div>",tooltip:t.lang.style[o],click:t.context.createInvokeHandler("editor.formatBlock")}).render()})})(e);this.context.memo("button.bold",function(){return t.button({className:"note-btn-bold",contents:t.ui.icon(t.options.icons.bold),tooltip:t.lang.font.bold+t.representShortcut("bold"),click:t.context.createInvokeHandlerAndUpdateState("editor.bold")}).render()}),this.context.memo("button.italic",function(){return t.button({className:"note-btn-italic",contents:t.ui.icon(t.options.icons.italic),tooltip:t.lang.font.italic+t.representShortcut("italic"),click:t.context.createInvokeHandlerAndUpdateState("editor.italic")}).render()}),this.context.memo("button.underline",function(){return t.button({className:"note-btn-underline",contents:t.ui.icon(t.options.icons.underline),tooltip:t.lang.font.underline+t.representShortcut("underline"),click:t.context.createInvokeHandlerAndUpdateState("editor.underline")}).render()}),this.context.memo("button.clear",function(){return t.button({contents:t.ui.icon(t.options.icons.eraser),tooltip:t.lang.font.clear+t.representShortcut("removeFormat"),click:t.context.createInvokeHandler("editor.removeFormat")}).render()}),this.context.memo("button.strikethrough",function(){return t.button({className:"note-btn-strikethrough",contents:t.ui.icon(t.options.icons.strikethrough),tooltip:t.lang.font.strikethrough+t.representShortcut("strikethrough"),click:t.context.createInvokeHandlerAndUpdateState("editor.strikethrough")}).render()}),this.context.memo("button.superscript",function(){return t.button({className:"note-btn-superscript",contents:t.ui.icon(t.options.icons.superscript),tooltip:t.lang.font.superscript,click:t.context.createInvokeHandlerAndUpdateState("editor.superscript")}).render()}),this.context.memo("button.subscript",function(){return t.button({className:"note-btn-subscript",contents:t.ui.icon(t.options.icons.subscript),tooltip:t.lang.font.subscript,click:t.context.createInvokeHandlerAndUpdateState("editor.subscript")}).render()}),this.context.memo("button.fontname",function(){var e=t.context.invoke("editor.currentStyle");return t.options.addDefaultFonts&&i.a.each(e["font-family"].split(","),function(e,n){n=n.trim().replace(/['"]+/g,""),t.isFontDeservedToAdd(n)&&-1===t.options.fontNames.indexOf(n)&&t.options.fontNames.push(n)}),t.ui.buttonGroup([t.button({className:"dropdown-toggle",contents:t.ui.dropdownButtonContents('<span class="note-current-fontname"></span>',t.options),tooltip:t.lang.font.name,data:{toggle:"dropdown"}}),t.ui.dropdownCheck({className:"dropdown-fontname",checkClassName:t.options.icons.menuCheck,items:t.options.fontNames.filter(t.isFontInstalled.bind(t)),title:t.lang.font.name,template:function(t){return'<span style="font-family: '+p.validFontName(t)+'">'+t+"</span>"},click:t.context.createInvokeHandlerAndUpdateState("editor.fontName")}),]).render()}),this.context.memo("button.fontsize",function(){return t.ui.buttonGroup([t.button({className:"dropdown-toggle",contents:t.ui.dropdownButtonContents('<span class="note-current-fontsize"></span>',t.options),tooltip:t.lang.font.size,data:{toggle:"dropdown"}}),t.ui.dropdownCheck({className:"dropdown-fontsize",checkClassName:t.options.icons.menuCheck,items:t.options.fontSizes,title:t.lang.font.size,click:t.context.createInvokeHandlerAndUpdateState("editor.fontSize")}),]).render()}),this.context.memo("button.fontsizeunit",function(){return t.ui.buttonGroup([t.button({className:"dropdown-toggle",contents:t.ui.dropdownButtonContents('<span class="note-current-fontsizeunit"></span>',t.options),tooltip:t.lang.font.sizeunit,data:{toggle:"dropdown"}}),t.ui.dropdownCheck({className:"dropdown-fontsizeunit",checkClassName:t.options.icons.menuCheck,items:t.options.fontSizeUnits,title:t.lang.font.sizeunit,click:t.context.createInvokeHandlerAndUpdateState("editor.fontSizeUnit")}),]).render()}),this.context.memo("button.color",function(){return t.colorPalette("note-color-all",t.lang.color.recent,!0,!0)}),this.context.memo("button.forecolor",function(){return t.colorPalette("note-color-fore",t.lang.color.foreground,!1,!0)}),this.context.memo("button.backcolor",function(){return t.colorPalette("note-color-back",t.lang.color.background,!0,!1)}),this.context.memo("button.ul",function(){return t.button({contents:t.ui.icon(t.options.icons.unorderedlist),tooltip:t.lang.lists.unordered+t.representShortcut("insertUnorderedList"),click:t.context.createInvokeHandler("editor.insertUnorderedList")}).render()}),this.context.memo("button.ol",function(){return t.button({contents:t.ui.icon(t.options.icons.orderedlist),tooltip:t.lang.lists.ordered+t.representShortcut("insertOrderedList"),click:t.context.createInvokeHandler("editor.insertOrderedList")}).render()});var o=this.button({contents:this.ui.icon(this.options.icons.alignLeft),tooltip:this.lang.paragraph.left+this.representShortcut("justifyLeft"),click:this.context.createInvokeHandler("editor.justifyLeft")}),a=this.button({contents:this.ui.icon(this.options.icons.alignCenter),tooltip:this.lang.paragraph.center+this.representShortcut("justifyCenter"),click:this.context.createInvokeHandler("editor.justifyCenter")}),r=this.button({contents:this.ui.icon(this.options.icons.alignRight),tooltip:this.lang.paragraph.right+this.representShortcut("justifyRight"),click:this.context.createInvokeHandler("editor.justifyRight")}),s=this.button({contents:this.ui.icon(this.options.icons.alignJustify),tooltip:this.lang.paragraph.justify+this.representShortcut("justifyFull"),click:this.context.createInvokeHandler("editor.justifyFull")}),l=this.button({contents:this.ui.icon(this.options.icons.outdent),tooltip:this.lang.paragraph.outdent+this.representShortcut("outdent"),click:this.context.createInvokeHandler("editor.outdent")}),c=this.button({contents:this.ui.icon(this.options.icons.indent),tooltip:this.lang.paragraph.indent+this.representShortcut("indent"),click:this.context.createInvokeHandler("editor.indent")});this.context.memo("button.justifyLeft",m.invoke(o,"render")),this.context.memo("button.justifyCenter",m.invoke(a,"render")),this.context.memo("button.justifyRight",m.invoke(r,"render")),this.context.memo("button.justifyFull",m.invoke(s,"render")),this.context.memo("button.outdent",m.invoke(l,"render")),this.context.memo("button.indent",m.invoke(c,"render")),this.context.memo("button.paragraph",function(){return t.ui.buttonGroup([t.button({className:"dropdown-toggle",contents:t.ui.dropdownButtonContents(t.ui.icon(t.options.icons.alignLeft),t.options),tooltip:t.lang.paragraph.paragraph,data:{toggle:"dropdown"}}),t.ui.dropdown([t.ui.buttonGroup({className:"note-align",children:[o,a,r,s]}),t.ui.buttonGroup({className:"note-list",children:[l,c]})]),]).render()}),this.context.memo("button.height",function(){return t.ui.buttonGroup([t.button({className:"dropdown-toggle",contents:t.ui.dropdownButtonContents(t.ui.icon(t.options.icons.textHeight),t.options),tooltip:t.lang.font.height,data:{toggle:"dropdown"}}),t.ui.dropdownCheck({items:t.options.lineHeights,checkClassName:t.options.icons.menuCheck,className:"dropdown-line-height",title:t.lang.font.height,click:t.context.createInvokeHandler("editor.lineHeight")}),]).render()}),this.context.memo("button.table",function(){return t.ui.buttonGroup([t.button({className:"dropdown-toggle",contents:t.ui.dropdownButtonContents(t.ui.icon(t.options.icons.table),t.options),tooltip:t.lang.table.table,data:{toggle:"dropdown"}}),t.ui.dropdown({title:t.lang.table.table,className:"note-table",items:'<div class="note-dimension-picker"><div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"></div><div class="note-dimension-picker-highlighted"></div><div class="note-dimension-picker-unhighlighted"></div></div><div class="note-dimension-display">1 x 1</div>'}),],{callback:function(e){e.find(".note-dimension-picker-mousecatcher").css({width:t.options.insertTableMaxSize.col+"em",height:t.options.insertTableMaxSize.row+"em"}).mousedown(t.context.createInvokeHandler("editor.insertTable")).on("mousemove",t.tableMoveHandler.bind(t))}}).render()}),this.context.memo("button.link",function(){return t.button({contents:t.ui.icon(t.options.icons.link),tooltip:t.lang.link.link+t.representShortcut("linkDialog.show"),click:t.context.createInvokeHandler("linkDialog.show")}).render()}),this.context.memo("button.picture",function(){return t.button({contents:t.ui.icon(t.options.icons.picture),tooltip:t.lang.image.image,click:t.context.createInvokeHandler("imageDialog.show")}).render()}),this.context.memo("button.video",function(){return t.button({contents:t.ui.icon(t.options.icons.video),tooltip:t.lang.video.video,click:t.context.createInvokeHandler("videoDialog.show")}).render()}),this.context.memo("button.hr",function(){return t.button({contents:t.ui.icon(t.options.icons.minus),tooltip:t.lang.hr.insert+t.representShortcut("insertHorizontalRule"),click:t.context.createInvokeHandler("editor.insertHorizontalRule")}).render()}),this.context.memo("button.fullscreen",function(){return t.button({className:"btn-fullscreen note-codeview-keep",contents:t.ui.icon(t.options.icons.arrowsAlt),tooltip:t.lang.options.fullscreen,click:t.context.createInvokeHandler("fullscreen.toggle")}).render()}),this.context.memo("button.codeview",function(){return t.button({className:"btn-codeview note-codeview-keep",contents:t.ui.icon(t.options.icons.code),tooltip:t.lang.options.codeview,click:t.context.createInvokeHandler("codeview.toggle")}).render()}),this.context.memo("button.redo",function(){return t.button({contents:t.ui.icon(t.options.icons.redo),tooltip:t.lang.history.redo+t.representShortcut("redo"),click:t.context.createInvokeHandler("editor.redo")}).render()}),this.context.memo("button.undo",function(){return t.button({contents:t.ui.icon(t.options.icons.undo),tooltip:t.lang.history.undo+t.representShortcut("undo"),click:t.context.createInvokeHandler("editor.undo")}).render()}),this.context.memo("button.help",function(){return t.button({contents:t.ui.icon(t.options.icons.question),tooltip:t.lang.options.help,click:t.context.createInvokeHandler("helpDialog.show")}).render()})}},{key:"addImagePopoverButtons",value:function(){var t=this;this.context.memo("button.resizeFull",function(){return t.button({contents:'<span class="note-fontsize-10">100%</span>',tooltip:t.lang.image.resizeFull,click:t.context.createInvokeHandler("editor.resize","1")}).render()}),this.context.memo("button.resizeHalf",function(){return t.button({contents:'<span class="note-fontsize-10">50%</span>',tooltip:t.lang.image.resizeHalf,click:t.context.createInvokeHandler("editor.resize","0.5")}).render()}),this.context.memo("button.resizeQuarter",function(){return t.button({contents:'<span class="note-fontsize-10">25%</span>',tooltip:t.lang.image.resizeQuarter,click:t.context.createInvokeHandler("editor.resize","0.25")}).render()}),this.context.memo("button.resizeNone",function(){return t.button({contents:t.ui.icon(t.options.icons.rollback),tooltip:t.lang.image.resizeNone,click:t.context.createInvokeHandler("editor.resize","0")}).render()}),this.context.memo("button.floatLeft",function(){return t.button({contents:t.ui.icon(t.options.icons.floatLeft),tooltip:t.lang.image.floatLeft,click:t.context.createInvokeHandler("editor.floatMe","left")}).render()}),this.context.memo("button.floatRight",function(){return t.button({contents:t.ui.icon(t.options.icons.floatRight),tooltip:t.lang.image.floatRight,click:t.context.createInvokeHandler("editor.floatMe","right")}).render()}),this.context.memo("button.floatNone",function(){return t.button({contents:t.ui.icon(t.options.icons.rollback),tooltip:t.lang.image.floatNone,click:t.context.createInvokeHandler("editor.floatMe","none")}).render()}),this.context.memo("button.removeMedia",function(){return t.button({contents:t.ui.icon(t.options.icons.trash),tooltip:t.lang.image.remove,click:t.context.createInvokeHandler("editor.removeMedia")}).render()})}},{key:"addLinkPopoverButtons",value:function(){var t=this;this.context.memo("button.linkDialogShow",function(){return t.button({contents:t.ui.icon(t.options.icons.link),tooltip:t.lang.link.edit,click:t.context.createInvokeHandler("linkDialog.show")}).render()}),this.context.memo("button.unlink",function(){return t.button({contents:t.ui.icon(t.options.icons.unlink),tooltip:t.lang.link.unlink,click:t.context.createInvokeHandler("editor.unlink")}).render()})}},{key:"addTablePopoverButtons",value:function(){var t=this;this.context.memo("button.addRowUp",function(){return t.button({className:"btn-md",contents:t.ui.icon(t.options.icons.rowAbove),tooltip:t.lang.table.addRowAbove,click:t.context.createInvokeHandler("editor.addRow","top")}).render()}),this.context.memo("button.addRowDown",function(){return t.button({className:"btn-md",contents:t.ui.icon(t.options.icons.rowBelow),tooltip:t.lang.table.addRowBelow,click:t.context.createInvokeHandler("editor.addRow","bottom")}).render()}),this.context.memo("button.addColLeft",function(){return t.button({className:"btn-md",contents:t.ui.icon(t.options.icons.colBefore),tooltip:t.lang.table.addColLeft,click:t.context.createInvokeHandler("editor.addCol","left")}).render()}),this.context.memo("button.addColRight",function(){return t.button({className:"btn-md",contents:t.ui.icon(t.options.icons.colAfter),tooltip:t.lang.table.addColRight,click:t.context.createInvokeHandler("editor.addCol","right")}).render()}),this.context.memo("button.deleteRow",function(){return t.button({className:"btn-md",contents:t.ui.icon(t.options.icons.rowRemove),tooltip:t.lang.table.delRow,click:t.context.createInvokeHandler("editor.deleteRow")}).render()}),this.context.memo("button.deleteCol",function(){return t.button({className:"btn-md",contents:t.ui.icon(t.options.icons.colRemove),tooltip:t.lang.table.delCol,click:t.context.createInvokeHandler("editor.deleteCol")}).render()}),this.context.memo("button.deleteTable",function(){return t.button({className:"btn-md",contents:t.ui.icon(t.options.icons.trash),tooltip:t.lang.table.delTable,click:t.context.createInvokeHandler("editor.deleteTable")}).render()})}},{key:"build",value:function(t,e){for(var n=0,o=e.length;n<o;n++){for(var i=e[n],a=Array.isArray(i)?i[0]:i,r=Array.isArray(i)?1===i.length?[i[0]]:i[1]:[i],s=this.ui.buttonGroup({className:"note-"+a}).render(),l=0,c=r.length;l<c;l++){var u=this.context.memo("button."+r[l]);u&&s.append("function"==typeof u?u(this.context):u)}s.appendTo(t)}}},{key:"updateCurrentStyle",value:function(t){var e=this,n=t||this.$toolbar,o=this.context.invoke("editor.currentStyle");if(this.updateBtnStates(n,{".note-btn-bold":function(){return"bold"===o["font-bold"]},".note-btn-italic":function(){return"italic"===o["font-italic"]},".note-btn-underline":function(){return"underline"===o["font-underline"]},".note-btn-subscript":function(){return"subscript"===o["font-subscript"]},".note-btn-superscript":function(){return"superscript"===o["font-superscript"]},".note-btn-strikethrough":function(){return"strikethrough"===o["font-strikethrough"]}}),o["font-family"]){var a=o["font-family"].split(",").map(function(t){return t.replace(/[\'\"]/g,"").replace(/\s+$/,"").replace(/^\s+/,"")}),r=C.find(a,this.isFontInstalled.bind(this));n.find(".dropdown-fontname a").each(function(t,e){var n=i()(e),o=n.data("value")+""==r+"";n.toggleClass("checked",o)}),n.find(".note-current-fontname").text(r).css("font-family",r)}if(o["font-size"]){var s=o["font-size"];n.find(".dropdown-fontsize a").each(function(t,e){var n=i()(e),o=n.data("value")+""==s+"";n.toggleClass("checked",o)}),n.find(".note-current-fontsize").text(s);var l=o["font-size-unit"];n.find(".dropdown-fontsizeunit a").each(function(t,e){var n=i()(e),o=n.data("value")+""==l+"";n.toggleClass("checked",o)}),n.find(".note-current-fontsizeunit").text(l)}if(o["line-height"]){var c=o["line-height"];n.find(".dropdown-line-height li a").each(function(t,n){var o=i()(n).data("value")+""==c+"";e.className=o?"checked":""})}}},{key:"updateBtnStates",value:function(t,e){var n=this;i.a.each(e,function(e,o){n.ui.toggleBtnActive(t.find(e),o())})}},{key:"tableMoveHandler",value:function(t){var e,n=i()(t.target.parentNode),o=n.next(),a=n.find(".note-dimension-picker-mousecatcher"),r=n.find(".note-dimension-picker-highlighted"),s=n.find(".note-dimension-picker-unhighlighted");if(void 0===t.offsetX){var l=i()(t.target).offset();e={x:t.pageX-l.left,y:t.pageY-l.top}}else e={x:t.offsetX,y:t.offsetY};var c=Math.ceil(e.x/18)||1,u=Math.ceil(e.y/18)||1;r.css({width:c+"em",height:u+"em"}),a.data("value",c+"x"+u),c>3&&c<this.options.insertTableMaxSize.col&&s.css({width:c+1+"em"}),u>3&&u<this.options.insertTableMaxSize.row&&s.css({height:u+1+"em"}),o.html(c+" x "+u)}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tB=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.$window=i()(window),this.$document=i()(document),this.ui=i.a.summernote.ui,this.$note=t.layoutInfo.note,this.$editor=t.layoutInfo.editor,this.$toolbar=t.layoutInfo.toolbar,this.$editable=t.layoutInfo.editable,this.$statusbar=t.layoutInfo.statusbar,this.options=t.options,this.isFollowing=!1,this.followScroll=this.followScroll.bind(this)}return t=n,e=[{key:"shouldInitialize",value:function(){return!this.options.airMode}},{key:"initialize",value:function(){var t=this;this.options.toolbar=this.options.toolbar||[],this.options.toolbar.length?this.context.invoke("buttons.build",this.$toolbar,this.options.toolbar):this.$toolbar.hide(),this.options.toolbarContainer&&this.$toolbar.appendTo(this.options.toolbarContainer),this.changeContainer(!1),this.$note.on("summernote.keyup summernote.mouseup summernote.change",function(){t.context.invoke("buttons.updateCurrentStyle")}),this.context.invoke("buttons.updateCurrentStyle"),this.options.followingToolbar&&this.$window.on("scroll resize",this.followScroll)}},{key:"destroy",value:function(){this.$toolbar.children().remove(),this.options.followingToolbar&&this.$window.off("scroll resize",this.followScroll)}},{key:"followScroll",value:function(){if(this.$editor.hasClass("fullscreen"))return!1;var t=this.$editor.outerHeight(),e=this.$editor.width(),n=this.$toolbar.height(),o=this.$statusbar.height(),a=0;this.options.otherStaticBar&&(a=i()(this.options.otherStaticBar).outerHeight());var r=this.$document.scrollTop(),s=this.$editor.offset().top,l=s-a,c=s+t-a-n-o;!this.isFollowing&&r>l&&r<c-n?(this.isFollowing=!0,this.$editable.css({marginTop:this.$toolbar.outerHeight()}),this.$toolbar.css({position:"fixed",top:a,width:e,zIndex:1e3})):this.isFollowing&&(r<l||r>c)&&(this.isFollowing=!1,this.$toolbar.css({position:"relative",top:0,width:"100%",zIndex:"auto"}),this.$editable.css({marginTop:""}))}},{key:"changeContainer",value:function(t){t?this.$toolbar.prependTo(this.$editor):this.options.toolbarContainer&&this.$toolbar.appendTo(this.options.toolbarContainer),this.options.followingToolbar&&this.followScroll()}},{key:"updateFullscreen",value:function(t){this.ui.toggleBtnActive(this.$toolbar.find(".btn-fullscreen"),t),this.changeContainer(t)}},{key:"updateCodeview",value:function(t){this.ui.toggleBtnActive(this.$toolbar.find(".btn-codeview"),t),t?this.deactivate():this.activate()}},{key:"activate",value:function(t){var e=this.$toolbar.find("button");t||(e=e.not(".note-codeview-keep")),this.ui.toggleBtn(e,!0)}},{key:"deactivate",value:function(t){var e=this.$toolbar.find("button");t||(e=e.not(".note-codeview-keep")),this.ui.toggleBtn(e,!1)}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tz=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.ui=i.a.summernote.ui,this.$body=i()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo,t.memo("help.linkDialog.show",this.options.langInfo.help["linkDialog.show"])}return t=n,e=[{key:"initialize",value:function(){var t=this.options.dialogsInBody?this.$body:this.options.container,e=['<div class="form-group note-form-group">','<label for="note-dialog-link-txt-'.concat(this.options.id,'" class="note-form-label">').concat(this.lang.link.textToDisplay,"</label>"),'<input id="note-dialog-link-txt-'.concat(this.options.id,'" class="note-link-text form-control note-form-control note-input" type="text"/>'),"</div>",'<div class="form-group note-form-group">','<label for="note-dialog-link-url-'.concat(this.options.id,'" class="note-form-label">').concat(this.lang.link.url,"</label>"),'<input id="note-dialog-link-url-'.concat(this.options.id,'" class="note-link-url form-control note-form-control note-input" type="text" value="http://"/>'),"</div>",this.options.disableLinkTarget?"":i()("<div/>").append(this.ui.checkbox({className:"sn-checkbox-open-in-new-window",text:this.lang.link.openInNewWindow,checked:!0}).render()).html(),i()("<div/>").append(this.ui.checkbox({className:"sn-checkbox-use-protocol",text:this.lang.link.useProtocol,checked:!0}).render()).html(),].join(""),n='<input type="button" href="#" class="'.concat("btn btn-primary note-btn note-btn-primary note-link-btn",'" value="').concat(this.lang.link.insert,'" disabled>');this.$dialog=this.ui.dialog({className:"link-dialog",title:this.lang.link.insert,fade:this.options.dialogsFade,body:e,footer:n}).render().appendTo(t)}},{key:"destroy",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:"bindEnterKey",value:function(t,e){t.on("keypress",function(t){t.keyCode===tk.code.ENTER&&(t.preventDefault(),e.trigger("click"))})}},{key:"toggleLinkBtn",value:function(t,e,n){this.ui.toggleBtn(t,e.val()&&n.val())}},{key:"showLinkDialog",value:function(t){var e=this;return i.a.Deferred(function(n){var o=e.$dialog.find(".note-link-text"),i=e.$dialog.find(".note-link-url"),a=e.$dialog.find(".note-link-btn"),r=e.$dialog.find(".sn-checkbox-open-in-new-window input[type=checkbox]"),s=e.$dialog.find(".sn-checkbox-use-protocol input[type=checkbox]");e.ui.onDialogShown(e.$dialog,function(){e.context.triggerEvent("dialog.shown"),!t.url&&m.isValidUrl(t.text)&&(t.url=t.text),o.on("input paste propertychange",function(){t.text=o.val(),e.toggleLinkBtn(a,o,i)}).val(t.text),i.on("input paste propertychange",function(){t.text||o.val(i.val()),e.toggleLinkBtn(a,o,i)}).val(t.url),p.isSupportTouch||i.trigger("focus"),e.toggleLinkBtn(a,o,i),e.bindEnterKey(i,a),e.bindEnterKey(o,a);var l=void 0!==t.isNewWindow?t.isNewWindow:e.context.options.linkTargetBlank;r.prop("checked",l);var c=!t.url&&e.context.options.useProtocol;s.prop("checked",c),a.one("click",function(a){a.preventDefault(),n.resolve({range:t.range,url:i.val(),text:o.val(),isNewWindow:r.is(":checked"),checkProtocol:s.is(":checked")}),e.ui.hideDialog(e.$dialog)})}),e.ui.onDialogHidden(e.$dialog,function(){o.off(),i.off(),a.off(),"pending"===n.state()&&n.reject()}),e.ui.showDialog(e.$dialog)}).promise()}},{key:"show",value:function(){var t=this,e=this.context.invoke("editor.getLinkInfo");this.context.invoke("editor.saveRange"),this.showLinkDialog(e).then(function(e){t.context.invoke("editor.restoreRange"),t.context.invoke("editor.createLink",e)}).fail(function(){t.context.invoke("editor.restoreRange")})}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tM=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.ui=i.a.summernote.ui,this.options=t.options,this.events={"summernote.keyup summernote.mouseup summernote.change summernote.scroll":function(){e.update()},"summernote.disable summernote.dialog.shown summernote.blur":function(){e.hide()}}}return t=n,e=[{key:"shouldInitialize",value:function(){return!C.isEmpty(this.options.popover.link)}},{key:"initialize",value:function(){this.$popover=this.ui.popover({className:"note-link-popover",callback:function(t){t.find(".popover-content,.note-popover-content").prepend('<span><a target="_blank"></a>&nbsp;</span>')}}).render().appendTo(this.options.container);var t=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",t,this.options.popover.link),this.$popover.on("mousedown",function(t){t.preventDefault()})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"update",value:function(){if(this.context.invoke("editor.hasFocus")){var t=this.context.invoke("editor.getLastRange");if(t.isCollapsed()&&t.isOnAnchor()){var e=th.ancestor(t.sc,th.isAnchor),n=i()(e).attr("href");this.$popover.find("a").attr("href",n).text(n);var o=th.posFromPlaceholder(e),a=i()(this.options.container).offset();o.top-=a.top,o.left-=a.left,this.$popover.css({display:"block",left:o.left,top:o.top})}else this.hide()}else this.hide()}},{key:"hide",value:function(){this.$popover.hide()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tO=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.ui=i.a.summernote.ui,this.$body=i()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo}return t=n,e=[{key:"initialize",value:function(){var t="";if(this.options.maximumImageFileSize){var e=Math.floor(Math.log(this.options.maximumImageFileSize)/Math.log(1024)),n=1*(this.options.maximumImageFileSize/Math.pow(1024,e)).toFixed(2)+" "+" KMGTP"[e]+"B";t="<small>".concat(this.lang.image.maximumFileSize+" : "+n,"</small>")}var o=this.options.dialogsInBody?this.$body:this.options.container,i=['<div class="form-group note-form-group note-group-select-from-files">','<label for="note-dialog-image-file-'+this.options.id+'" class="note-form-label">'+this.lang.image.selectFromFiles+"</label>",'<input id="note-dialog-image-file-'+this.options.id+'" class="note-image-input form-control-file note-form-control note-input" ',' type="file" name="files" accept="image/*" multiple="multiple"/>',t,"</div>",'<div class="form-group note-group-image-url">','<label for="note-dialog-image-url-'+this.options.id+'" class="note-form-label">'+this.lang.image.url+"</label>",'<input id="note-dialog-image-url-'+this.options.id+'" class="note-image-url form-control note-form-control note-input" type="text"/>',"</div>",].join(""),a='<input type="button" href="#" class="'.concat("btn btn-primary note-btn note-btn-primary note-image-btn",'" value="').concat(this.lang.image.insert,'" disabled>');this.$dialog=this.ui.dialog({title:this.lang.image.insert,fade:this.options.dialogsFade,body:i,footer:a}).render().appendTo(o)}},{key:"destroy",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:"bindEnterKey",value:function(t,e){t.on("keypress",function(t){t.keyCode===tk.code.ENTER&&(t.preventDefault(),e.trigger("click"))})}},{key:"show",value:function(){var t=this;this.context.invoke("editor.saveRange"),this.showImageDialog().then(function(e){t.ui.hideDialog(t.$dialog),t.context.invoke("editor.restoreRange"),"string"==typeof e?t.options.callbacks.onImageLinkInsert?t.context.triggerEvent("image.link.insert",e):t.context.invoke("editor.insertImage",e):t.context.invoke("editor.insertImagesOrCallback",e)}).fail(function(){t.context.invoke("editor.restoreRange")})}},{key:"showImageDialog",value:function(){var t=this;return i.a.Deferred(function(e){var n=t.$dialog.find(".note-image-input"),o=t.$dialog.find(".note-image-url"),i=t.$dialog.find(".note-image-btn");t.ui.onDialogShown(t.$dialog,function(){t.context.triggerEvent("dialog.shown"),n.replaceWith(n.clone().on("change",function(t){e.resolve(t.target.files||t.target.value)}).val("")),o.on("input paste propertychange",function(){t.ui.toggleBtn(i,o.val())}).val(""),p.isSupportTouch||o.trigger("focus"),i.click(function(t){t.preventDefault(),e.resolve(o.val())}),t.bindEnterKey(o,i)}),t.ui.onDialogHidden(t.$dialog,function(){n.off(),o.off(),i.off(),"pending"===e.state()&&e.reject()}),t.ui.showDialog(t.$dialog)})}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tU=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.ui=i.a.summernote.ui,this.editable=t.layoutInfo.editable[0],this.options=t.options,this.events={"summernote.disable summernote.blur":function(){e.hide()}}}return t=n,e=[{key:"shouldInitialize",value:function(){return!C.isEmpty(this.options.popover.image)}},{key:"initialize",value:function(){this.$popover=this.ui.popover({className:"note-image-popover"}).render().appendTo(this.options.container);var t=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",t,this.options.popover.image),this.$popover.on("mousedown",function(t){t.preventDefault()})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"update",value:function(t,e){if(th.isImg(t)){var n=i()(t).offset(),o=i()(this.options.container).offset(),a={};this.options.popatmouse?(a.left=e.pageX-20,a.top=e.pageY):a=n,a.top-=o.top,a.left-=o.left,this.$popover.css({display:"block",left:a.left,top:a.top})}else this.hide()}},{key:"hide",value:function(){this.$popover.hide()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),t0=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.ui=i.a.summernote.ui,this.options=t.options,this.events={"summernote.mousedown":function(t,n){e.update(n.target)},"summernote.keyup summernote.scroll summernote.change":function(){e.update()},"summernote.disable summernote.blur":function(){e.hide()}}}return t=n,e=[{key:"shouldInitialize",value:function(){return!C.isEmpty(this.options.popover.table)}},{key:"initialize",value:function(){this.$popover=this.ui.popover({className:"note-table-popover"}).render().appendTo(this.options.container);var t=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",t,this.options.popover.table),p.isFF&&document.execCommand("enableInlineTableEditing",!1,!1),this.$popover.on("mousedown",function(t){t.preventDefault()})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"update",value:function(t){if(this.context.isDisabled())return!1;var e=th.isCell(t);if(e){var n=th.posFromPlaceholder(t),o=i()(this.options.container).offset();n.top-=o.top,n.left-=o.left,this.$popover.css({display:"block",left:n.left,top:n.top})}else this.hide();return e}},{key:"hide",value:function(){this.$popover.hide()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tW=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.ui=i.a.summernote.ui,this.$body=i()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo}return t=n,e=[{key:"initialize",value:function(){var t=this.options.dialogsInBody?this.$body:this.options.container,e=['<div class="form-group note-form-group row-fluid">','<label for="note-dialog-video-url-'.concat(this.options.id,'" class="note-form-label">').concat(this.lang.video.url,' <small class="text-muted">').concat(this.lang.video.providers,"</small></label>"),'<input id="note-dialog-video-url-'.concat(this.options.id,'" class="note-video-url form-control note-form-control note-input" type="text"/>'),"</div>",].join(""),n='<input type="button" href="#" class="'.concat("btn btn-primary note-btn note-btn-primary note-video-btn",'" value="').concat(this.lang.video.insert,'" disabled>');this.$dialog=this.ui.dialog({title:this.lang.video.insert,fade:this.options.dialogsFade,body:e,footer:n}).render().appendTo(t)}},{key:"destroy",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:"bindEnterKey",value:function(t,e){t.on("keypress",function(t){t.keyCode===tk.code.ENTER&&(t.preventDefault(),e.trigger("click"))})}},{key:"createVideoNode",value:function(t){var e,n=t.match(/\/\/(?:(?:www|m)\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([\w|-]{11})(?:(?:[\?&]t=)(\S+))?$/),o=t.match(/(?:www\.|\/\/)instagram\.com\/p\/(.[a-zA-Z0-9_-]*)/),a=t.match(/\/\/vine\.co\/v\/([a-zA-Z0-9]+)/),r=t.match(/\/\/(player\.)?vimeo\.com\/([a-z]*\/)*(\d+)[?]?.*/),s=t.match(/.+dailymotion.com\/(video|hub)\/([^_]+)[^#]*(#video=([^_&]+))?/),l=t.match(/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/),c=t.match(/\/\/v\.qq\.com.*?vid=(.+)/),u=t.match(/\/\/v\.qq\.com\/x?\/?(page|cover).*?\/([^\/]+)\.html\??.*/),d=t.match(/^.+.(mp4|m4v)$/),h=t.match(/^.+.(ogg|ogv)$/),f=t.match(/^.+.(webm)$/),p=t.match(/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/);if(n&&11===n[1].length){var v=n[1],m=0;if(void 0!==n[2]){var g=n[2].match(/^(?:(\d+)h)?(?:(\d+)m)?(?:(\d+)s)?$/);if(g)for(var b=[3600,60,1],k=0,y=b.length;k<y;k++)m+=void 0!==g[k+1]?b[k]*parseInt(g[k+1],10):0}e=i()("<iframe>").attr("frameborder",0).attr("src","//www.youtube.com/embed/"+v+(m>0?"?start="+m:"")).attr("width","640").attr("height","360")}else if(o&&o[0].length)e=i()("<iframe>").attr("frameborder",0).attr("src","https://instagram.com/p/"+o[1]+"/embed/").attr("width","612").attr("height","710").attr("scrolling","no").attr("allowtransparency","true");else if(a&&a[0].length)e=i()("<iframe>").attr("frameborder",0).attr("src",a[0]+"/embed/simple").attr("width","600").attr("height","600").attr("class","vine-embed");else if(r&&r[3].length)e=i()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("src","//player.vimeo.com/video/"+r[3]).attr("width","640").attr("height","360");else if(s&&s[2].length)e=i()("<iframe>").attr("frameborder",0).attr("src","//www.dailymotion.com/embed/video/"+s[2]).attr("width","640").attr("height","360");else if(l&&l[1].length)e=i()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("height","498").attr("width","510").attr("src","//player.youku.com/embed/"+l[1]);else if(c&&c[1].length||u&&u[2].length){var C=c&&c[1].length?c[1]:u[2];e=i()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("height","310").attr("width","500").attr("src","https://v.qq.com/txp/iframe/player.html?vid="+C+"&amp;auto=0")}else if(d||h||f)e=i()("<video controls>").attr("src",t).attr("width","640").attr("height","360");else{if(!p||!p[0].length)return!1;e=i()("<iframe>").attr("frameborder",0).attr("src","https://www.facebook.com/plugins/video.php?href="+encodeURIComponent(p[0])+"&show_text=0&width=560").attr("width","560").attr("height","301").attr("scrolling","no").attr("allowtransparency","true")}return e.addClass("note-video-clip"),e[0]}},{key:"show",value:function(){var t=this,e=this.context.invoke("editor.getSelectedText");this.context.invoke("editor.saveRange"),this.showVideoDialog(e).then(function(e){t.ui.hideDialog(t.$dialog),t.context.invoke("editor.restoreRange");var n=t.createVideoNode(e);n&&t.context.invoke("editor.insertNode",n)}).fail(function(){t.context.invoke("editor.restoreRange")})}},{key:"showVideoDialog",value:function(){var t=this;return i.a.Deferred(function(e){var n=t.$dialog.find(".note-video-url"),o=t.$dialog.find(".note-video-btn");t.ui.onDialogShown(t.$dialog,function(){t.context.triggerEvent("dialog.shown"),n.on("input paste propertychange",function(){t.ui.toggleBtn(o,n.val())}),p.isSupportTouch||n.trigger("focus"),o.click(function(t){t.preventDefault(),e.resolve(n.val())}),t.bindEnterKey(n,o)}),t.ui.onDialogHidden(t.$dialog,function(){n.off(),o.off(),"pending"===e.state()&&e.reject()}),t.ui.showDialog(t.$dialog)})}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),tj=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.ui=i.a.summernote.ui,this.$body=i()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo}return t=n,e=[{key:"initialize",value:function(){var t=this.options.dialogsInBody?this.$body:this.options.container;this.$dialog=this.ui.dialog({title:this.lang.options.help,fade:this.options.dialogsFade,body:this.createShortcutList(),footer:'<p class="text-center"><a href="http://summernote.org/" target="_blank">Summernote 0.8.18</a> \xb7 <a href="https://github.com/summernote/summernote" target="_blank">Project</a> \xb7 <a href="https://github.com/summernote/summernote/issues" target="_blank">Issues</a></p>',callback:function(t){t.find(".modal-body,.note-modal-body").css({"max-height":300,overflow:"scroll"})}}).render().appendTo(t)}},{key:"destroy",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:"createShortcutList",value:function(){var t=this,e=this.options.keyMap[p.isMac?"mac":"pc"];return Object.keys(e).map(function(n){var o=e[n],a=i()('<div><div class="help-list-item"></div></div>');return a.append(i()("<label><kbd>"+n+"</kdb></label>").css({width:180,"margin-right":10})).append(i()("<span/>").html(t.context.memo("help."+o)||o)),a.html()}).join("")}},{key:"showHelpDialog",value:function(){var t=this;return i.a.Deferred(function(e){t.ui.onDialogShown(t.$dialog,function(){t.context.triggerEvent("dialog.shown"),e.resolve()}),t.ui.showDialog(t.$dialog)}).promise()}},{key:"show",value:function(){var t=this;this.context.invoke("editor.saveRange"),this.showHelpDialog().then(function(){t.context.invoke("editor.restoreRange")})}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),t1=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.ui=i.a.summernote.ui,this.options=t.options,this.hidable=!0,this.onContextmenu=!1,this.pageX=null,this.pageY=null,this.events={"summernote.contextmenu":function(t){e.options.editing&&(t.preventDefault(),t.stopPropagation(),e.onContextmenu=!0,e.update(!0))},"summernote.mousedown":function(t,n){e.pageX=n.pageX,e.pageY=n.pageY},"summernote.keyup summernote.mouseup summernote.scroll":function(t,n){e.options.editing&&!e.onContextmenu&&(e.pageX=n.pageX,e.pageY=n.pageY,e.update()),e.onContextmenu=!1},"summernote.disable summernote.change summernote.dialog.shown summernote.blur":function(){e.hide()},"summernote.focusout":function(){e.$popover.is(":active,:focus")||e.hide()}}}return t=n,e=[{key:"shouldInitialize",value:function(){return this.options.airMode&&!C.isEmpty(this.options.popover.air)}},{key:"initialize",value:function(){var t=this;this.$popover=this.ui.popover({className:"note-air-popover"}).render().appendTo(this.options.container);var e=this.$popover.find(".popover-content");this.context.invoke("buttons.build",e,this.options.popover.air),this.$popover.on("mousedown",function(){t.hidable=!1}),this.$popover.on("mouseup",function(){t.hidable=!0})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"update",value:function(t){var e=this.context.invoke("editor.currentStyle");if(!e.range||e.range.isCollapsed()&&!t)this.hide();else{var n={left:this.pageX,top:this.pageY},o=i()(this.options.container).offset();n.top-=o.top,n.left-=o.left,this.$popover.css({display:"block",left:Math.max(n.left,0)+-5,top:n.top+5}),this.context.invoke("buttons.updateCurrentStyle",this.$popover)}}},{key:"updateCodeview",value:function(t){this.ui.toggleBtnActive(this.$popover.find(".btn-codeview"),t),t&&this.hide()}},{key:"hide",value:function(){this.hidable&&this.$popover.hide()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),t4=function(){var t,e;function n(t){var e=this;!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.context=t,this.ui=i.a.summernote.ui,this.$editable=t.layoutInfo.editable,this.options=t.options,this.hint=this.options.hint||[],this.direction=this.options.hintDirection||"bottom",this.hints=Array.isArray(this.hint)?this.hint:[this.hint],this.events={"summernote.keyup":function(t,n){n.isDefaultPrevented()||e.handleKeyup(n)},"summernote.keydown":function(t,n){e.handleKeydown(n)},"summernote.disable summernote.dialog.shown summernote.blur":function(){e.hide()}}}return t=n,e=[{key:"shouldInitialize",value:function(){return this.hints.length>0}},{key:"initialize",value:function(){var t=this;this.lastWordRange=null,this.matchingWord=null,this.$popover=this.ui.popover({className:"note-hint-popover",hideArrow:!0,direction:""}).render().appendTo(this.options.container),this.$popover.hide(),this.$content=this.$popover.find(".popover-content,.note-popover-content"),this.$content.on("click",".note-hint-item",function(e){t.$content.find(".active").removeClass("active"),i()(e.currentTarget).addClass("active"),t.replace()}),this.$popover.on("mousedown",function(t){t.preventDefault()})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"selectItem",value:function(t){this.$content.find(".active").removeClass("active"),t.addClass("active"),this.$content[0].scrollTop=t[0].offsetTop-this.$content.innerHeight()/2}},{key:"moveDown",value:function(){var t=this.$content.find(".note-hint-item.active"),e=t.next();if(e.length)this.selectItem(e);else{var n=t.parent().next();n.length||(n=this.$content.find(".note-hint-group").first()),this.selectItem(n.find(".note-hint-item").first())}}},{key:"moveUp",value:function(){var t=this.$content.find(".note-hint-item.active"),e=t.prev();if(e.length)this.selectItem(e);else{var n=t.parent().prev();n.length||(n=this.$content.find(".note-hint-group").last()),this.selectItem(n.find(".note-hint-item").last())}}},{key:"replace",value:function(){var t=this.$content.find(".note-hint-item.active");if(t.length){var e=this.nodeFromItem(t);if(null!==this.matchingWord&&0===this.matchingWord.length)this.lastWordRange.so=this.lastWordRange.eo;else if(null!==this.matchingWord&&this.matchingWord.length>0&&!this.lastWordRange.isCollapsed()){var n=this.lastWordRange.eo-this.lastWordRange.so-this.matchingWord.length;n>0&&(this.lastWordRange.so+=n)}if(this.lastWordRange.insertNode(e),"next"===this.options.hintSelect){var o=document.createTextNode("");i()(e).after(o),tg.createFromNodeBefore(o).select()}else tg.createFromNodeAfter(e).select();this.lastWordRange=null,this.hide(),this.context.invoke("editor.focus")}}},{key:"nodeFromItem",value:function(t){var e=this.hints[t.data("index")],n=t.data("item"),o=e.content?e.content(n):n;return"string"==typeof o&&(o=th.createText(o)),o}},{key:"createItemTemplates",value:function(t,e){var n=this.hints[t];return e.map(function(e){var o=i()('<div class="note-hint-item"/>');return o.append(n.template?n.template(e):e+""),o.data({index:t,item:e}),o})}},{key:"handleKeydown",value:function(t){this.$popover.is(":visible")&&(t.keyCode===tk.code.ENTER?(t.preventDefault(),this.replace()):t.keyCode===tk.code.UP?(t.preventDefault(),this.moveUp()):t.keyCode===tk.code.DOWN&&(t.preventDefault(),this.moveDown()))}},{key:"searchKeyword",value:function(t,e,n){var o=this.hints[t];if(o&&o.match.test(e)&&o.search){var i=o.match.exec(e);this.matchingWord=i[0],o.search(i[1],n)}else n()}},{key:"createGroup",value:function(t,e){var n=this,o=i()('<div class="note-hint-group note-hint-group-'+t+'"></div>');return this.searchKeyword(t,e,function(e){(e=e||[]).length&&(o.html(n.createItemTemplates(t,e)),n.show())}),o}},{key:"handleKeyup",value:function(t){var e=this;if(!C.contains([tk.code.ENTER,tk.code.UP,tk.code.DOWN],t.keyCode)){var n,o,a=this.context.invoke("editor.getLastRange");if("words"===this.options.hintMode){if(o=(n=a.getWordsRange(a)).toString(),this.hints.forEach(function(t){if(t.match.test(o))return n=a.getWordsMatchRange(t.match),!1}),!n)return void this.hide();o=n.toString()}else o=(n=a.getWordRange()).toString();if(this.hints.length&&o){this.$content.empty();var r=m.rect2bnd(C.last(n.getClientRects())),s=i()(this.options.container).offset();r&&(r.top-=s.top,r.left-=s.left,this.$popover.hide(),this.lastWordRange=n,this.hints.forEach(function(t,n){t.match.test(o)&&e.createGroup(n,o).appendTo(e.$content)}),this.$content.find(".note-hint-item:first").addClass("active"),"top"===this.direction?this.$popover.css({left:r.left,top:r.top-this.$popover.outerHeight()-5}):this.$popover.css({left:r.left,top:r.top+r.height+5}))}else this.hide()}}},{key:"show",value:function(){this.$popover.show()}},{key:"hide",value:function(){this.$popover.hide()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}();i.a.summernote=i.a.extend(i.a.summernote,{version:"0.8.18",plugins:{},dom:th,range:tg,lists:C,options:{langInfo:i.a.summernote.lang["en-US"],editing:!0,modules:{editor:t9,clipboard:tN,dropzone:tT,statusbar:tI,fullscreen:tE,handle:tP,hintPopover:t4,autoLink:tL,autoSync:tA,autoReplace:tF,placeholder:tD,buttons:tH,toolbar:tB,linkDialog:tz,linkPopover:tM,imageDialog:tO,imagePopover:tU,tablePopover:t0,videoDialog:tW,helpDialog:tj,airPopover:t1},buttons:{},lang:"en-US",followingToolbar:!1,toolbarPosition:"top",otherStaticBar:"",codeviewKeepButton:!1,toolbar:[["style",["style"]],["font",["bold","underline","clear"]],["color",["color"]],["para",["ul","ol","paragraph"]],["insert",["link","picture"]],["view",["fullscreen","help"]],],popatmouse:!0,popover:{image:[["resize",["resizeFull","resizeHalf","resizeQuarter","resizeNone"]],["float",["floatLeft","floatRight","floatNone"]],["remove",["removeMedia"]],],link:[["link",["linkDialogShow","unlink"]]],table:[["add",["addRowDown","addRowUp","addColLeft","addColRight"]],["delete",["deleteRow","deleteCol","deleteTable"]],],air:[["color",["color"]],["font",["bold","underline","clear"]],["para",["ul","paragraph"]],["table",["table"]],["insert",["link","picture"]],["view",["fullscreen"]],]},airMode:!1,overrideContextMenu:!1,width:null,height:null,linkTargetBlank:!0,useProtocol:!0,defaultProtocol:"http://",focus:!1,tabDisabled:!1,tabSize:4,styleWithCSS:!1,shortcuts:!0,textareaAutoSync:!0,tooltip:"auto",container:null,maxTextLength:0,blockquoteBreakingLevel:2,spellCheck:!0,disableGrammar:!1,placeholder:null,inheritPlaceholder:!1,recordEveryKeystroke:!1,historyLimit:200,showDomainOnlyForAutolink:!1,hintMode:"word",hintSelect:"after",hintDirection:"bottom",styleTags:["p","blockquote","pre","h1","h2","h3","h4","h5","h6"],fontNames:["Arial","Arial Black","Comic Sans MS","Courier New","Helvetica Neue","Helvetica","Impact","Lucida Grande","Tahoma","Times New Roman","Verdana"],fontNamesIgnoreCheck:[],addDefaultFonts:!0,fontSizes:["8","9","10","11","12","14","18","24","36"],fontSizeUnits:["px","pt"],colors:[["#000000","#424242","#636363","#9C9C94","#CEC6CE","#EFEFEF","#F7F7F7","#FFFFFF"],["#FF0000","#FF9C00","#FFFF00","#00FF00","#00FFFF","#0000FF","#9C00FF","#FF00FF"],["#F7C6CE","#FFE7CE","#FFEFC6","#D6EFD6","#CEDEE7","#CEE7F7","#D6D6E7","#E7D6DE"],["#E79C9C","#FFC69C","#FFE79C","#B5D6A5","#A5C6CE","#9CC6EF","#B5A5D6","#D6A5BD"],["#E76363","#F7AD6B","#FFD663","#94BD7B","#73A5AD","#6BADDE","#8C7BC6","#C67BA5"],["#CE0000","#E79439","#EFC631","#6BA54A","#4A7B8C","#3984C6","#634AA5","#A54A7B"],["#9C0000","#B56308","#BD9400","#397B21","#104A5A","#085294","#311873","#731842"],["#630000","#7B3900","#846300","#295218","#083139","#003163","#21104A","#4A1031"],],colorsName:[["Black","Tundora","Dove Gray","Star Dust","Pale Slate","Gallery","Alabaster","White"],["Red","Orange Peel","Yellow","Green","Cyan","Blue","Electric Violet","Magenta"],["Azalea","Karry","Egg White","Zanah","Botticelli","Tropical Blue","Mischka","Twilight"],["Tonys Pink","Peach Orange","Cream Brulee","Sprout","Casper","Perano","Cold Purple","Careys Pink"],["Mandy","Rajah","Dandelion","Olivine","Gulf Stream","Viking","Blue Marguerite","Puce"],["Guardsman Red","Fire Bush","Golden Dream","Chelsea Cucumber","Smalt Blue","Boston Blue","Butterfly Bush","Cadillac"],["Sangria","Mai Tai","Buddha Gold","Forest Green","Eden","Venice Blue","Meteorite","Claret"],["Rosewood","Cinnamon","Olive","Parsley","Tiber","Midnight Blue","Valentino","Loulou"],],colorButton:{foreColor:"#000000",backColor:"#FFFF00"},lineHeights:["1.0","1.2","1.4","1.5","1.6","1.8","2.0","3.0"],tableClassName:"table table-bordered",insertTableMaxSize:{col:10,row:10},dialogsInBody:!1,dialogsFade:!1,maximumImageFileSize:null,callbacks:{onBeforeCommand:null,onBlur:null,onBlurCodeview:null,onChange:null,onChangeCodeview:null,onDialogShown:null,onEnter:null,onFocus:null,onImageLinkInsert:null,onImageUpload:null,onImageUploadError:null,onInit:null,onKeydown:null,onKeyup:null,onMousedown:null,onMouseup:null,onPaste:null,onScroll:null},codemirror:{mode:"text/html",htmlMode:!0,lineNumbers:!0},codeviewFilter:!1,codeviewFilterRegex:/<\/*(?:applet|b(?:ase|gsound|link)|embed|frame(?:set)?|ilayer|l(?:ayer|ink)|meta|object|s(?:cript|tyle)|t(?:itle|extarea)|xml)[^>]*?>/gi,codeviewIframeFilter:!0,codeviewIframeWhitelistSrc:[],codeviewIframeWhitelistSrcBase:["www.youtube.com","www.youtube-nocookie.com","www.facebook.com","vine.co","instagram.com","player.vimeo.com","www.dailymotion.com","player.youku.com","v.qq.com"],keyMap:{pc:{ESC:"escape",ENTER:"insertParagraph","CTRL+Z":"undo","CTRL+Y":"redo",TAB:"tab","SHIFT+TAB":"untab","CTRL+B":"bold","CTRL+I":"italic","CTRL+U":"underline","CTRL+SHIFT+S":"strikethrough","CTRL+BACKSLASH":"removeFormat","CTRL+SHIFT+L":"justifyLeft","CTRL+SHIFT+E":"justifyCenter","CTRL+SHIFT+R":"justifyRight","CTRL+SHIFT+J":"justifyFull","CTRL+SHIFT+NUM7":"insertUnorderedList","CTRL+SHIFT+NUM8":"insertOrderedList","CTRL+LEFTBRACKET":"outdent","CTRL+RIGHTBRACKET":"indent","CTRL+NUM0":"formatPara","CTRL+NUM1":"formatH1","CTRL+NUM2":"formatH2","CTRL+NUM3":"formatH3","CTRL+NUM4":"formatH4","CTRL+NUM5":"formatH5","CTRL+NUM6":"formatH6","CTRL+ENTER":"insertHorizontalRule","CTRL+K":"linkDialog.show"},mac:{ESC:"escape",ENTER:"insertParagraph","CMD+Z":"undo","CMD+SHIFT+Z":"redo",TAB:"tab","SHIFT+TAB":"untab","CMD+B":"bold","CMD+I":"italic","CMD+U":"underline","CMD+SHIFT+S":"strikethrough","CMD+BACKSLASH":"removeFormat","CMD+SHIFT+L":"justifyLeft","CMD+SHIFT+E":"justifyCenter","CMD+SHIFT+R":"justifyRight","CMD+SHIFT+J":"justifyFull","CMD+SHIFT+NUM7":"insertUnorderedList","CMD+SHIFT+NUM8":"insertOrderedList","CMD+LEFTBRACKET":"outdent","CMD+RIGHTBRACKET":"indent","CMD+NUM0":"formatPara","CMD+NUM1":"formatH1","CMD+NUM2":"formatH2","CMD+NUM3":"formatH3","CMD+NUM4":"formatH4","CMD+NUM5":"formatH5","CMD+NUM6":"formatH6","CMD+ENTER":"insertHorizontalRule","CMD+K":"linkDialog.show"}},icons:{align:"note-icon-align",alignCenter:"note-icon-align-center",alignJustify:"note-icon-align-justify",alignLeft:"note-icon-align-left",alignRight:"note-icon-align-right",rowBelow:"note-icon-row-below",colBefore:"note-icon-col-before",colAfter:"note-icon-col-after",rowAbove:"note-icon-row-above",rowRemove:"note-icon-row-remove",colRemove:"note-icon-col-remove",indent:"note-icon-align-indent",outdent:"note-icon-align-outdent",arrowsAlt:"note-icon-arrows-alt",bold:"note-icon-bold",caret:"note-icon-caret",circle:"note-icon-circle",close:"note-icon-close",code:"note-icon-code",eraser:"note-icon-eraser",floatLeft:"note-icon-float-left",floatRight:"note-icon-float-right",font:"note-icon-font",frame:"note-icon-frame",italic:"note-icon-italic",link:"note-icon-link",unlink:"note-icon-chain-broken",magic:"note-icon-magic",menuCheck:"note-icon-menu-check",minus:"note-icon-minus",orderedlist:"note-icon-orderedlist",pencil:"note-icon-pencil",picture:"note-icon-picture",question:"note-icon-question",redo:"note-icon-redo",rollback:"note-icon-rollback",square:"note-icon-square",strikethrough:"note-icon-strikethrough",subscript:"note-icon-subscript",superscript:"note-icon-superscript",table:"note-icon-table",textHeight:"note-icon-text-height",trash:"note-icon-trash",underline:"note-icon-underline",undo:"note-icon-undo",unorderedlist:"note-icon-unorderedlist",video:"note-icon-video"}}})},51:function(t,e,n){"use strict";n.r(e);var o=n(0),i=n.n(o),a=n(1),r=function(){var t,e;function n(t,e){if(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.$node=t,this.options=i.a.extend({},{title:"",target:e.container,trigger:"hover focus",placement:"bottom"},e),this.$tooltip=i()('<div class="note-tooltip"><div class="note-tooltip-arrow"></div><div class="note-tooltip-content"></div></div>'),"manual"!==this.options.trigger){var o=this.show.bind(this),a=this.hide.bind(this),r=this.toggle.bind(this);this.options.trigger.split(" ").forEach(function(e){"hover"===e?(t.off("mouseenter mouseleave"),t.on("mouseenter",o).on("mouseleave",a)):"click"===e?t.on("click",r):"focus"===e&&t.on("focus",o).on("blur",a)})}}return t=n,e=[{key:"show",value:function(){var t=this.$node,e=t.offset(),n=i()(this.options.target).offset();e.top-=n.top,e.left-=n.left;var o=this.$tooltip,a=this.options.title||t.attr("title")||t.data("title"),r=this.options.placement||t.data("placement");o.addClass(r),o.find(".note-tooltip-content").text(a),o.appendTo(this.options.target);var s=t.outerWidth(),l=t.outerHeight(),c=o.outerWidth(),u=o.outerHeight();"bottom"===r?o.css({top:e.top+l,left:e.left+(s/2-c/2)}):"top"===r?o.css({top:e.top-u,left:e.left+(s/2-c/2)}):"left"===r?o.css({top:e.top+(l/2-u/2),left:e.left-c}):"right"===r&&o.css({top:e.top+(l/2-u/2),left:e.left+s}),o.addClass("in")}},{key:"hide",value:function(){var t=this;this.$tooltip.removeClass("in"),setTimeout(function(){t.$tooltip.remove()},200)}},{key:"toggle",value:function(){this.$tooltip.hasClass("in")?this.hide():this.show()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),s=function(){var t,e;function n(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.$button=t,this.options=i.a.extend({},{target:e.container},e),this.setEvent()}return t=n,e=[{key:"setEvent",value:function(){var t=this;this.$button.on("click",function(e){t.toggle(),e.stopImmediatePropagation()})}},{key:"clear",value:function(){var t=i()(".note-btn-group.open");t.find(".note-btn.active").removeClass("active"),t.removeClass("open")}},{key:"show",value:function(){this.$button.addClass("active"),this.$button.parent().addClass("open");var t=this.$button.next(),e=t.offset(),n=t.outerWidth(),o=i()(window).width(),a=parseFloat(i()(this.options.target).css("margin-right"));e.left+n>o-a?t.css("margin-left",o-a-(e.left+n)):t.css("margin-left","")}},{key:"hide",value:function(){this.$button.removeClass("active"),this.$button.parent().removeClass("open")}},{key:"toggle",value:function(){var t=this.$button.parent().hasClass("open");this.clear(),t?this.hide():this.show()}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}();i()(document).on("click",function(t){i()(t.target).closest(".note-btn-group").length||(i()(".note-btn-group.open").removeClass("open"),i()(".note-btn-group .note-btn.active").removeClass("active"))}),i()(document).on("click.note-dropdown-menu",function(t){i()(t.target).closest(".note-dropdown-menu").parent().removeClass("open"),i()(t.target).closest(".note-dropdown-menu").parent().find(".note-btn.active").removeClass("active")});var l=s,c=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.$modal=t,this.$backdrop=i()('<div class="note-modal-backdrop"/>')}return t=n,e=[{key:"show",value:function(){var t=this;this.$backdrop.appendTo(document.body).show(),this.$modal.addClass("open").show(),this.$modal.trigger("note.modal.show"),this.$modal.off("click",".close").on("click",".close",this.hide.bind(this)),this.$modal.on("keydown",function(e){27===e.which&&(e.preventDefault(),t.hide())})}},{key:"hide",value:function(){this.$modal.removeClass("open").hide(),this.$backdrop.hide(),this.$modal.trigger("note.modal.hide"),this.$modal.off("keydown")}},],function t(e,n){for(var o=0;o<n.length;o++){var i=n[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(t.prototype,e),n}(),u=a.a.create('<div class="note-editor note-frame"/>'),d=a.a.create('<div class="note-toolbar" role="toolbar"/>'),h=a.a.create('<div class="note-editing-area"/>'),f=a.a.create('<textarea class="note-codable" aria-multiline="true"/>'),p=a.a.create('<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"/>'),v=a.a.create('<output class="note-status-output" role="status" aria-live="polite"></output><div class="note-statusbar" role="status"><div class="note-resizebar" aria-label="resize"><div class="note-icon-bar"></div><div class="note-icon-bar"></div><div class="note-icon-bar"></div></div></div>'),m=a.a.create('<div class="note-editor note-airframe"/>'),g=a.a.create('<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"></div><output class="note-status-output" role="status" aria-live="polite"></output>'),b=a.a.create('<div class="note-btn-group">'),k=a.a.create('<button type="button" class="note-btn" tabindex="-1">',function(t,e){e&&e.tooltip&&(t.attr({"aria-label":e.tooltip}),t.data("_lite_tooltip",new r(t,{title:e.tooltip,container:e.container})).on("click",function(t){i()(t.currentTarget).data("_lite_tooltip").hide()})),e.contents&&t.html(e.contents),e&&e.data&&"dropdown"===e.data.toggle&&t.data("_lite_dropdown",new l(t,{container:e.container})),e&&e.codeviewKeepButton&&t.addClass("note-codeview-keep")}),y=a.a.create('<div class="note-dropdown-menu" role="list">',function(t,e){var n=Array.isArray(e.items)?e.items.map(function(t){var n="string"==typeof t?t:t.value||"",o=e.template?e.template(t):t,a=i()('<a class="note-dropdown-item" href="#" data-value="'+n+'" role="listitem" aria-label="'+n+'"></a>');return a.html(o).data("item",t),a}):e.items;t.html(n).attr({"aria-label":e.title}),t.on("click","> .note-dropdown-item",function(t){var n=i()(this),o=n.data("item"),a=n.data("value");o.click?o.click(n):e.itemClick&&e.itemClick(t,o,a)}),e&&e.codeviewKeepButton&&t.addClass("note-codeview-keep")}),C=a.a.create('<div class="note-dropdown-menu note-check" role="list">',function(t,e){var n=Array.isArray(e.items)?e.items.map(function(t){var n="string"==typeof t?t:t.value||"",o=e.template?e.template(t):t,a=i()('<a class="note-dropdown-item" href="#" data-value="'+n+'" role="listitem" aria-label="'+t+'"></a>');return a.html([F(e.checkClassName)," ",o]).data("item",t),a}):e.items;t.html(n).attr({"aria-label":e.title}),t.on("click","> .note-dropdown-item",function(t){var n=i()(this),o=n.data("item"),a=n.data("value");o.click?o.click(n):e.itemClick&&e.itemClick(t,o,a)}),e&&e.codeviewKeepButton&&t.addClass("note-codeview-keep")}),w=function(t,e){return t+" "+F(e.icons.caret,"span")},x=function(t,e){return b([k({className:"dropdown-toggle",contents:t.title+" "+F("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),y({className:t.className,items:t.items,template:t.template,itemClick:t.itemClick}),],{callback:e}).render()},$=function(t,e){return b([k({className:"dropdown-toggle",contents:t.title+" "+F("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),C({className:t.className,checkClassName:t.checkClassName,items:t.items,template:t.template,itemClick:t.itemClick}),],{callback:e}).render()},S=function(t){return b([k({className:"dropdown-toggle",contents:t.title+" "+F("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),y([b({className:"note-align",children:t.items[0]}),b({className:"note-list",children:t.items[1]})]),]).render()},N=function(t){return b([k({className:"dropdown-toggle",contents:t.title+" "+F("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),y({className:"note-table",items:'<div class="note-dimension-picker"><div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"></div><div class="note-dimension-picker-highlighted"></div><div class="note-dimension-picker-unhighlighted"></div></div><div class="note-dimension-display">1 x 1</div>'}),],{callback:function(e){e.find(".note-dimension-picker-mousecatcher").css({width:t.col+"em",height:t.row+"em"}).mousedown(t.itemClick).mousemove(function(e){!function(t,e,n){var o,a=i()(t.target.parentNode),r=a.next(),s=a.find(".note-dimension-picker-mousecatcher"),l=a.find(".note-dimension-picker-highlighted"),c=a.find(".note-dimension-picker-unhighlighted");if(void 0===t.offsetX){var u=i()(t.target).offset();o={x:t.pageX-u.left,y:t.pageY-u.top}}else o={x:t.offsetX,y:t.offsetY};var d=Math.ceil(o.x/18)||1,h=Math.ceil(o.y/18)||1;l.css({width:d+"em",height:h+"em"}),s.data("value",d+"x"+h),d>3&&d<e&&c.css({width:d+1+"em"}),h>3&&h<n&&c.css({height:h+1+"em"}),r.html(d+" x "+h)}(e,t.col,t.row)})}}).render()},T=a.a.create('<div class="note-color-palette"/>',function(t,e){for(var n=[],o=0,a=e.colors.length;o<a;o++){for(var s=e.eventName,l=e.colors[o],c=e.colorsName[o],u=[],d=0,h=l.length;d<h;d++){var f=l[d],p=c[d];u.push(['<button type="button" class="note-btn note-color-btn"','style="background-color:',f,'" ','data-event="',s,'" ','data-value="',f,'" ','data-title="',p,'" ','aria-label="',p,'" ','data-toggle="button" tabindex="-1"></button>',].join(""))}n.push('<div class="note-color-row">'+u.join("")+"</div>")}t.html(n.join("")),t.find(".note-color-btn").each(function(){i()(this).data("_lite_tooltip",new r(i()(this),{container:e.container}))})}),_=function(t,e){return b({className:"note-color",children:[k({className:"note-current-color-button",contents:t.title,tooltip:t.lang.color.recent,click:t.currentClick,callback:function(t){var n=t.find(".note-recent-color");"foreColor"!==e&&(n.css("background-color","#FFFF00"),t.attr("data-backColor","#FFFF00"))}}),k({className:"dropdown-toggle",contents:F("note-icon-caret"),tooltip:t.lang.color.more,data:{toggle:"dropdown"}}),y({items:["<div>",'<div class="note-btn-group btn-background-color">','<div class="note-palette-title">'+t.lang.color.background+"</div>","<div>",'<button type="button" class="note-color-reset note-btn note-btn-block" data-event="backColor" data-value="transparent">',t.lang.color.transparent,"</button>","</div>",'<div class="note-holder" data-event="backColor"></div>','<div class="btn-sm">','<input type="color" id="html5bcp" class="note-btn btn-default" value="#21104A" style="width:100%;" data-value="cp">','<button type="button" class="note-color-reset btn" data-event="backColor" data-value="cpbackColor">',t.lang.color.cpSelect,"</button>","</div>","</div>",'<div class="note-btn-group btn-foreground-color">','<div class="note-palette-title">'+t.lang.color.foreground+"</div>","<div>",'<button type="button" class="note-color-reset note-btn note-btn-block" data-event="removeFormat" data-value="foreColor">',t.lang.color.resetToDefault,"</button>","</div>",'<div class="note-holder" data-event="foreColor"></div>','<div class="btn-sm">','<input type="color" id="html5fcp" class="note-btn btn-default" value="#21104A" style="width:100%;" data-value="cp">','<button type="button" class="note-color-reset btn" data-event="foreColor" data-value="cpforeColor">',t.lang.color.cpSelect,"</button>","</div>","</div>","</div>",].join(""),callback:function(n){n.find(".note-holder").each(function(){var e=i()(this);e.append(T({colors:t.colors,eventName:e.data("event")}).render())}),"fore"===e?(n.find(".btn-background-color").hide(),n.css({"min-width":"210px"})):"back"===e&&(n.find(".btn-foreground-color").hide(),n.css({"min-width":"210px"}))},click:function(n){var o=i()(n.target),a=o.data("event"),r=o.data("value"),s=document.getElementById("html5fcp").value,l=document.getElementById("html5bcp").value;if("cp"===r?n.stopPropagation():"cpbackColor"===r?r=l:"cpforeColor"===r&&(r=s),a&&r){var c=o.closest(".note-color").find(".note-recent-color"),u=o.closest(".note-color").find(".note-current-color-button");c.css("backColor"===a?"background-color":"color",r),u.attr("data-"+a,r),"fore"===e?t.itemClick("foreColor",r):"back"===e?t.itemClick("backColor",r):t.itemClick(a,r)}}}),]}).render()},I=a.a.create('<div class="note-modal" aria-hidden="false" tabindex="-1" role="dialog"/>',function(t,e){e.fade&&t.addClass("fade"),t.attr({"aria-label":e.title}),t.html(['<div class="note-modal-content">',e.title?'<div class="note-modal-header"><button type="button" class="close" aria-label="Close" aria-hidden="true"><i class="note-icon-close"></i></button><h4 class="note-modal-title">'+e.title+"</h4></div>":"",'<div class="note-modal-body">'+e.body+"</div>",e.footer?'<div class="note-modal-footer">'+e.footer+"</div>":"","</div>",].join("")),t.data("modal",new c(t,e))}),E=function(t){var e='<div class="note-form-group"><label for="note-dialog-video-url-'+t.id+'" class="note-form-label">'+t.lang.video.url+' <small class="text-muted">'+t.lang.video.providers+'</small></label><input id="note-dialog-video-url-'+t.id+'" class="note-video-url note-input" type="text"/></div>',n=['<button type="button" href="#" class="note-btn note-btn-primary note-video-btn disabled" disabled>',t.lang.video.insert,"</button>"].join("");return I({title:t.lang.video.insert,fade:t.fade,body:e,footer:n}).render()},P=function(t){var e='<div class="note-form-group note-group-select-from-files"><label for="note-dialog-image-file-'+t.id+'" class="note-form-label">'+t.lang.image.selectFromFiles+'</label><input id="note-dialog-image-file-'+t.id+'" class="note-note-image-input note-input" type="file" name="files" accept="image/*" multiple="multiple"/>'+t.imageLimitation+'</div><div class="note-form-group"><label for="note-dialog-image-url-'+t.id+'" class="note-form-label">'+t.lang.image.url+'</label><input id="note-dialog-image-url-'+t.id+'" class="note-image-url note-input" type="text"/></div>',n=['<button href="#" type="button" class="note-btn note-btn-primary note-btn-large note-image-btn disabled" disabled>',t.lang.image.insert,"</button>"].join("");return I({title:t.lang.image.insert,fade:t.fade,body:e,footer:n}).render()},R=function(t){var e='<div class="note-form-group"><label for="note-dialog-link-txt-'+t.id+'" class="note-form-label">'+t.lang.link.textToDisplay+'</label><input id="note-dialog-link-txt-'+t.id+'" class="note-link-text note-input" type="text"/></div><div class="note-form-group"><label for="note-dialog-link-url-'+t.id+'" class="note-form-label">'+t.lang.link.url+'</label><input id="note-dialog-link-url-'+t.id+'" class="note-link-url note-input" type="text" value="http://"/></div>'+(t.disableLinkTarget?"":'<div class="checkbox"><label for="note-dialog-link-nw-'+t.id+'"><input id="note-dialog-link-nw-'+t.id+'" type="checkbox" checked> '+t.lang.link.openInNewWindow+"</label></div>")+'<div class="checkbox"><label for="note-dialog-link-up-'+t.id+'"><input id="note-dialog-link-up-'+t.id+'" type="checkbox" checked> '+t.lang.link.useProtocol+"</label></div>",n=['<button href="#" type="button" class="note-btn note-btn-primary note-link-btn disabled" disabled>',t.lang.link.insert,"</button>"].join("");return I({className:"link-dialog",title:t.lang.link.insert,fade:t.fade,body:e,footer:n}).render()},L=a.a.create('<div class="note-popover bottom"><div class="note-popover-arrow"></div><div class="popover-content note-children-container"></div></div>',function(t,e){var n=void 0!==e.direction?e.direction:"bottom";t.addClass(n).hide(),e.hideArrow&&t.find(".note-popover-arrow").hide()}),A=a.a.create('<div class="checkbox"></div>',function(t,e){t.html(["<label"+(e.id?' for="note-'+e.id+'"':"")+">",'<input role="checkbox" type="checkbox"'+(e.id?' id="note-'+e.id+'"':""),e.checked?" checked":"",' aria-checked="'+(e.checked?"true":"false")+'"/>',e.text?e.text:"","</label>",].join(""))}),F=function(t,e){return"<"+(e=e||"i")+' class="'+t+'"/>'};n(3),n(6),i.a.summernote=i.a.extend(i.a.summernote,{ui_template:function(t){return{editor:u,toolbar:d,editingArea:h,codable:f,editable:p,statusbar:v,airEditor:m,airEditable:g,buttonGroup:b,button:k,dropdown:y,dropdownCheck:C,dropdownButton:x,dropdownButtonContents:w,dropdownCheckButton:$,paragraphDropdownButton:S,tableDropdownButton:N,colorDropdownButton:_,palette:T,dialog:I,videoDialog:E,imageDialog:P,linkDialog:R,popover:L,checkbox:A,icon:F,options:t,toggleBtn:function(t,e){t.toggleClass("disabled",!e),t.attr("disabled",!e)},toggleBtnActive:function(t,e){t.toggleClass("active",e)},check:function(t,e){t.find(".checked").removeClass("checked"),t.find('[data-value="'+e+'"]').addClass("checked")},onDialogShown:function(t,e){t.one("note.modal.show",e)},onDialogHidden:function(t,e){t.one("note.modal.hide",e)},showDialog:function(t){t.data("modal").show()},hideDialog:function(t){t.data("modal").hide()},getPopoverContent:function(t){return t.find(".note-popover-content")},getDialogBody:function(t){return t.find(".note-modal-body")},createLayout:function(e){var n=(t.airMode?m([h([f(),g()])]):"bottom"===t.toolbarPosition?u([h([f(),p()]),d(),v()]):u([d(),h([f(),p()]),v()])).render();return n.insertAfter(e),{note:e,editor:n,toolbar:n.find(".note-toolbar"),editingArea:n.find(".note-editing-area"),editable:n.find(".note-editable"),codable:n.find(".note-codable"),statusbar:n.find(".note-statusbar")}},removeLayout:function(t,e){t.html(e.editable.html()),e.editor.remove(),t.off("summernote"),t.show()}}},interface:"lite"})},6:function(t,e,n){}})});