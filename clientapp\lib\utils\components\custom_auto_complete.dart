// import 'package:flutter/material.dart';

// class CustomAutoComplete extends StatelessWidget {
//   final Future<Iterable<String>> Function(TextEditingValue) optionsBuilder;
//   final Function(String)? onSelected;
//   final String? hintText;
//   final Function(String)? onFieldSubmitted;
//   final Function()? reset;
//   const CustomAutoComplete(
//       {super.key,
//       required this.optionsBuilder,
//       this.onSelected,
//       this.hintText,
//       this.reset,
//       this.onFieldSubmitted});

//   @override
//   Widget build(BuildContext context) {}
// }
