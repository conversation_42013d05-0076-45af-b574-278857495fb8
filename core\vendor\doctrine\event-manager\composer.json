{"name": "doctrine/event-manager", "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "license": "MIT", "type": "library", "keywords": ["events", "event", "event dispatcher", "event manager", "event system"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^4.28"}, "conflict": {"doctrine/common": "<2.9"}, "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "autoload-dev": {"psr-4": {"Doctrine\\Tests\\Common\\": "tests"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}, "sort-packages": true}}