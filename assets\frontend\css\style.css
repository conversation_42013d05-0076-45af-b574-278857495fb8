/*@import url('https://fonts.googleapis.com/css2?family=Aclonica&display=swap');*/

html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  overflow-x: hidden;
}

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: none;
  -moz-osx-font-smoothing: grayscale;
  /* Firefox */
  -webkit-font-smoothing: antialiased;
  /* WebKit  */
}

body {
  margin: 0;
  color: var(--body-color);
  overflow-x: hidden;
  font-family: var(--body-font);
  font-size: 14px;
  line-height: 26px;
}

@media only screen and (max-width: 480px) {
  body {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  body {
    font-size: 14px;
  }
}

h1 {
  font-size: 64px;
  line-height: 1.0833333333;
}

h2 {
  font-size: 44px;
  line-height: 1.4444444444;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  h2 {
    font-size: 30px;
  }
}

@media only screen and (max-width: 575.98px) {
  h2 {
    font-size: 27px;
  }
}

@media only screen and (max-width: 480px) {
  h2 {
    font-size: 26px;
  }
}

@media only screen and (max-width: 375px) {
  h2 {
    font-size: 24px;
  }
}

h3 {
  font-size: 32px;
  line-height: 1.0833333333;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  h3 {
    font-size: 26px;
  }
}

@media only screen and (max-width: 575.98px) {
  h3 {
    font-size: 25px;
  }
}

@media only screen and (max-width: 480px) {
  h3 {
    font-size: 24px;
  }
}

@media only screen and (max-width: 375px) {
  h3 {
    font-size: 22px;
  }
}

h4 {
  font-size: 24px;
  line-height: 1.0833333333;
}

@media only screen and (max-width: 575.98px) {
  h4 {
    font-size: 23px;
  }
}

@media only screen and (max-width: 375px) {
  h4 {
    font-size: 21px;
  }
}

h5 {
  font-size: 20px;
  line-height: 1.2380952381;
}

@media only screen and (max-width: 375px) {
  h5 {
    font-size: 18px;
  }
}

h6 {
  font-size: 16px;
  line-height: 1.2380952381;
}

@media only screen and (max-width: 375px) {
  h6 {
    font-size: 16px;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
  margin: 0;
  -webkit-transition: 300ms;
  transition: 300ms;
}

p {
  color: var(--paragraph-color);
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  hyphens: auto;
  margin-bottom: 0;
  line-height: 26px;
  font-size: 16px;
}

a {
  color: inherit;
  text-decoration: none;
  color: var(--extra-light-color);
  -webkit-transition: 300ms;
  transition: 300ms;
}

a,
a:hover,
a:focus,
a:active {
  text-decoration: none;
  outline: none;
  color: inherit;
}

pre {
  word-break: break-word;
}

a i {
  padding: 0 2px;
}

img {
  max-width: 100%;
}

.list-style-none {
  margin: 0;
  padding: 0;
  list-style: none;
}

ol {
  counter-reset: counter;
  padding-left: 0;
}

ol li:before {
  counter-increment: counter;
  content: counter(counter);
  font-weight: 500;
  margin-right: 10px;
}

span {
  display: inline-block;
}

.desktop-center {
  text-align: center;
}

.form--control {
  width: 100%;
}

/* Common Style */
.margin-0 {
  margin: 0 !important;
}

.padding-0 {
  padding: 0 !important;
}

.radius-parcent-50 {
  border-radius: 50%;
}

.radius-50 {
  border-radius: 50px;
}

.radius-40 {
  border-radius: 40px;
}

.radius-35 {
  border-radius: 35px;
}

.radius-30 {
  border-radius: 30px;
}

.radius-20 {
  border-radius: 20px;
}

.radius-15 {
  border-radius: 15px;
}

.radius-10 {
  border-radius: 10px;
}

.radius-5 {
  border-radius: 5px;
}

.radius-0 {
  border-radius: 0px !important;
}

.no-shadow {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.single-border-top {
  border-top: 1px solid rgba(221, 221, 221, 0.3);
}

.single-border {
  border: 1px solid rgba(221, 221, 221, 0.3);
}

.color-light {
  color: var(--light-color) !important;
}

.color-extra-light {
  color: var(--extra-light-color) !important;
}

.color-heading {
  color: var(--heading-color);
}

.bg-gray {
  background-color: var(--border-color);
}

.section-bg-1 {
  background-color: var(--section-bg-1);
}

.section-bg-base {
  background-color: var(--section-bg-base);
}

.section-bg-2 {
  background-color: var(--section-bg-2);
}

.section-bg-gradient {
  background: var(--bg-gradient);
}

.footer-bg-1 {
  background-color: var(--footer-bg-1);
}

.footer-bg-2 {
  background-color: var(--footer-bg-2);
}

.copyright-bg-1 {
  background-color: var(--copyright-bg-1);
}

.color-one {
  color: var(--main-color-one);
}

.color-completed {
  color: var(--success-color);
}

.bg-image {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.no-margin {
  margin: 0;
}

.lazyloads {
  display: block;
}

.center-text {
  text-align: center;
}

.flex-between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px 10px;
}

.form-message {
  font-size: 16px;
  line-height: 24px;
  width: 100%;
  padding: 15px 20px;
  border: 1px solid var(--border-color);
  border-radius: 7px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: var(--paragraph-color);
  -webkit-box-shadow: 0 0 10px transparent;
  box-shadow: 0 0 10px transparent;
}

.form-message:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.2);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

/*--------------------------------------------------------------
# Global
--------------------------------------------------------------*/
.fw-400 {
  font-weight: 400 !important;
}

.fw-500 {
  font-weight: 500 !important;
}

.fw-600 {
  font-weight: 600 !important;
}

.fw-700 {
  font-weight: 700 !important;
}

.fw-800 {
  font-weight: 800 !important;
}

.fw-900 {
  font-weight: 900 !important;
}

.fs-12 {
  font-size: 12px !important;
}

.fs-13 {
  font-size: 13px !important;
}

.fs-14 {
  font-size: 14px !important;
}

.fs-15 {
  font-size: 15px !important;
}

.fs-16 {
  font-size: 16px !important;
}

.fs-17 {
  font-size: 17px !important;
}

.fs-18 {
  font-size: 18px !important;
}

.fs-19 {
  font-size: 19px !important;
}

.fs-20 {
  font-size: 20px !important;
}

.fs-22 {
  font-size: 22px !important;
}

.fs-24 {
  font-size: 24px !important;
}

.fs-25 {
  font-size: 25px !important;
}

.fs-26 {
  font-size: 26px !important;
}

.fs-27 {
  font-size: 27px !important;
}

.fs-28 {
  font-size: 28px !important;
}

.fs-30 {
  font-size: 30px !important;
}

.fs-32 {
  font-size: 32px !important;
}

.fs-36 {
  font-size: 36px !important;
}

.fs-46 {
  font-size: 46px !important;
}

.fs-65 {
  font-size: 65px !important;
}

/*--------------------------------------------------------------
# Margin and Padding
--------------------------------------------------------------*/
/*-- Margin Top --*/
.mat-5 {
  margin-top: 5px;
}

.mat-10 {
  margin-top: 10px;
}

.mat-15 {
  margin-top: 15px;
}

.mat-20 {
  margin-top: 20px;
}

.mat-25 {
  margin-top: 25px;
}

.mat-30 {
  margin-top: 30px;
}

.mat-35 {
  margin-top: 35px;
}

.mat-40 {
  margin-top: 40px;
}

.mat-45 {
  margin-top: 45px;
}

.mat-50 {
  margin-top: 50px;
}

.mat-55 {
  margin-top: 55px;
}

.mat-60 {
  margin-top: 60px;
}

.mat-65 {
  margin-top: 65px;
}

.mat-70 {
  margin-top: 70px;
}

.mat-75 {
  margin-top: 75px;
}

.mat-80 {
  margin-top: 80px;
}

.mat-85 {
  margin-top: 85px;
}

.mat-90 {
  margin-top: 90px;
}

.mat-95 {
  margin-top: 95px;
}

.mat-100 {
  margin-top: 100px;
}

.mat-105 {
  margin-top: 105px;
}

.mat-110 {
  margin-top: 110px;
}

.mat-115 {
  margin-top: 115px;
}

.mat-120 {
  margin-top: 120px;
}

.mat-125 {
  margin-top: 125px;
}

.mat-130 {
  margin-top: 130px;
}

.mat-135 {
  margin-top: 135px;
}

.mat-140 {
  margin-top: 140px;
}

.mat-145 {
  margin-top: 145px;
}

.mat-150 {
  margin-top: 150px;
}

.mat-155 {
  margin-top: 155px;
}

.mat-160 {
  margin-top: 160px;
}

.mat-165 {
  margin-top: 165px;
}

.mat-170 {
  margin-top: 170px;
}

.mat-175 {
  margin-top: 175px;
}

.mat-180 {
  margin-top: 180px;
}

.mat-185 {
  margin-top: 185px;
}

.mat-190 {
  margin-top: 190px;
}

.mat-195 {
  margin-top: 195px;
}

.mat-200 {
  margin-top: 200px;
}

/*-- Margin Bottom --*/
.mab-5 {
  margin-bottom: 5px;
}

.mab-10 {
  margin-bottom: 10px;
}

.mab-15 {
  margin-bottom: 15px;
}

.mab-20 {
  margin-bottom: 20px;
}

.mab-25 {
  margin-bottom: 25px;
}

.mab-30 {
  margin-bottom: 30px;
}

.mab-35 {
  margin-bottom: 35px;
}

.mab-40 {
  margin-bottom: 40px;
}

.mab-45 {
  margin-bottom: 45px;
}

.mab-50 {
  margin-bottom: 50px;
}

.mab-55 {
  margin-bottom: 55px;
}

.mab-60 {
  margin-bottom: 60px;
}

.mab-65 {
  margin-bottom: 65px;
}

.mab-70 {
  margin-bottom: 70px;
}

.mab-75 {
  margin-bottom: 75px;
}

.mab-80 {
  margin-bottom: 80px;
}

.mab-85 {
  margin-bottom: 85px;
}

.mab-90 {
  margin-bottom: 90px;
}

.mab-95 {
  margin-bottom: 95px;
}

.mab-100 {
  margin-bottom: 100px;
}

.mab-105 {
  margin-bottom: 105px;
}

.mab-110 {
  margin-bottom: 110px;
}

.mab-115 {
  margin-bottom: 115px;
}

.mab-120 {
  margin-bottom: 120px;
}

.mab-125 {
  margin-bottom: 125px;
}

.mab-130 {
  margin-bottom: 130px;
}

.mab-135 {
  margin-bottom: 135px;
}

.mab-140 {
  margin-bottom: 140px;
}

.mab-145 {
  margin-bottom: 145px;
}

.mab-150 {
  margin-bottom: 150px;
}

.mab-155 {
  margin-bottom: 155px;
}

.mab-160 {
  margin-bottom: 160px;
}

.mab-165 {
  margin-bottom: 165px;
}

.mab-170 {
  margin-bottom: 170px;
}

.mab-175 {
  margin-bottom: 175px;
}

.mab-180 {
  margin-bottom: 180px;
}

.mab-185 {
  margin-bottom: 185px;
}

.mab-190 {
  margin-bottom: 190px;
}

.mab-195 {
  margin-bottom: 195px;
}

.mab-200 {
  margin-bottom: 200px;
}

/*-- Margin Left --*/
.mal-5 {
  margin-left: 5px;
}

.mal-10 {
  margin-left: 10px;
}

.mal-15 {
  margin-left: 15px;
}

.mal-20 {
  margin-left: 20px;
}

.mal-25 {
  margin-left: 25px;
}

.mal-30 {
  margin-left: 30px;
}

.mal-35 {
  margin-left: 35px;
}

.mal-40 {
  margin-left: 40px;
}

.mal-45 {
  margin-left: 45px;
}

.mal-50 {
  margin-left: 50px;
}

.mal-55 {
  margin-left: 55px;
}

.mal-60 {
  margin-left: 60px;
}

.mal-65 {
  margin-left: 65px;
}

.mal-70 {
  margin-left: 70px;
}

.mal-75 {
  margin-left: 75px;
}

.mal-80 {
  margin-left: 80px;
}

.mal-85 {
  margin-left: 85px;
}

.mal-90 {
  margin-left: 90px;
}

.mal-95 {
  margin-left: 95px;
}

.mal-100 {
  margin-left: 100px;
}

.mal-105 {
  margin-left: 105px;
}

.mal-110 {
  margin-left: 110px;
}

.mal-115 {
  margin-left: 115px;
}

.mal-120 {
  margin-left: 120px;
}

.mal-125 {
  margin-left: 125px;
}

.mal-130 {
  margin-left: 130px;
}

.mal-135 {
  margin-left: 135px;
}

.mal-140 {
  margin-left: 140px;
}

.mal-145 {
  margin-left: 145px;
}

.mal-150 {
  margin-left: 150px;
}

.mal-155 {
  margin-left: 155px;
}

.mal-160 {
  margin-left: 160px;
}

.mal-165 {
  margin-left: 165px;
}

.mal-170 {
  margin-left: 170px;
}

.mal-175 {
  margin-left: 175px;
}

.mal-180 {
  margin-left: 180px;
}

.mal-185 {
  margin-left: 185px;
}

.mal-190 {
  margin-left: 190px;
}

.mal-195 {
  margin-left: 195px;
}

.mal-200 {
  margin-left: 200px;
}

/*-- Margin Right --*/
.mar-5 {
  margin-right: 5px;
}

.mar-10 {
  margin-right: 10px;
}

.mar-15 {
  margin-right: 15px;
}

.mar-20 {
  margin-right: 20px;
}

.mar-25 {
  margin-right: 25px;
}

.mar-30 {
  margin-right: 30px;
}

.mar-35 {
  margin-right: 35px;
}

.mar-40 {
  margin-right: 40px;
}

.mar-45 {
  margin-right: 45px;
}

.mar-50 {
  margin-right: 50px;
}

.mar-55 {
  margin-right: 55px;
}

.mar-60 {
  margin-right: 60px;
}

.mar-65 {
  margin-right: 65px;
}

.mar-70 {
  margin-right: 70px;
}

.mar-75 {
  margin-right: 75px;
}

.mar-80 {
  margin-right: 80px;
}

.mar-85 {
  margin-right: 85px;
}

.mar-90 {
  margin-right: 90px;
}

.mar-95 {
  margin-right: 95px;
}

.mar-100 {
  margin-right: 100px;
}

.mar-105 {
  margin-right: 105px;
}

.mar-110 {
  margin-right: 110px;
}

.mar-115 {
  margin-right: 115px;
}

.mar-120 {
  margin-right: 120px;
}

.mar-125 {
  margin-right: 125px;
}

.mar-130 {
  margin-right: 130px;
}

.mar-135 {
  margin-right: 135px;
}

.mar-140 {
  margin-right: 140px;
}

.mar-145 {
  margin-right: 145px;
}

.mar-150 {
  margin-right: 150px;
}

.mar-155 {
  margin-right: 155px;
}

.mar-160 {
  margin-right: 160px;
}

.mar-165 {
  margin-right: 165px;
}

.mar-170 {
  margin-right: 170px;
}

.mar-175 {
  margin-right: 175px;
}

.mar-180 {
  margin-right: 180px;
}

.mar-185 {
  margin-right: 185px;
}

.mar-190 {
  margin-right: 190px;
}

.mar-195 {
  margin-right: 195px;
}

.mar-200 {
  margin-right: 200px;
}

/*-- Padding Top --*/
.pat-5 {
  padding-top: 5px;
}

.pat-10 {
  padding-top: 10px;
}

.pat-15 {
  padding-top: 15px;
}

.pat-20 {
  padding-top: 20px;
}

.pat-25 {
  padding-top: 25px;
}

.pat-30 {
  padding-top: 30px;
}

.pat-35 {
  padding-top: 35px;
}

.pat-40 {
  padding-top: 40px;
}

.pat-45 {
  padding-top: 45px;
}

.pat-50 {
  padding-top: 50px;
}

.pat-55 {
  padding-top: 55px;
}

.pat-60 {
  padding-top: 60px;
}

.pat-65 {
  padding-top: 65px;
}

.pat-70 {
  padding-top: 70px;
}

.pat-75 {
  padding-top: 75px;
}

.pat-80 {
  padding-top: 80px;
}

.pat-85 {
  padding-top: 85px;
}

.pat-90 {
  padding-top: 90px;
}

.pat-95 {
  padding-top: 95px;
}

.pat-100 {
  padding-top: 100px;
}

.pat-105 {
  padding-top: 105px;
}

.pat-110 {
  padding-top: 110px;
}

.pat-115 {
  padding-top: 115px;
}

.pat-120 {
  padding-top: 120px;
}

.pat-125 {
  padding-top: 125px;
}

.pat-130 {
  padding-top: 130px;
}

.pat-135 {
  padding-top: 135px;
}

.pat-140 {
  padding-top: 140px;
}

.pat-145 {
  padding-top: 145px;
}

.pat-150 {
  padding-top: 150px;
}

.pat-155 {
  padding-top: 155px;
}

.pat-160 {
  padding-top: 160px;
}

.pat-165 {
  padding-top: 165px;
}

.pat-170 {
  padding-top: 170px;
}

.pat-175 {
  padding-top: 175px;
}

.pat-180 {
  padding-top: 180px;
}

.pat-185 {
  padding-top: 185px;
}

.pat-190 {
  padding-top: 190px;
}

.pat-195 {
  padding-top: 195px;
}

.pat-200 {
  padding-top: 200px;
}

@media screen and (max-width: 991.98px) {
  .pat-100 {
    padding-top: 70px !important;
  }

  .pab-100 {
    padding-bottom: 70px !important;
  }

  .pat-75 {
    padding-top: 45px !important;
  }

  .pab-75 {
    padding-bottom: 45px !important;
  }

  .pat-50 {
    padding-top: 35px !important;
  }

  .pab-50 {
    padding-bottom: 35px !important;
  }
}

/*-- Padding Bottom --*/
.pab-5 {
  padding-bottom: 5px;
}

.pab-10 {
  padding-bottom: 10px;
}

.pab-15 {
  padding-bottom: 15px;
}

.pab-20 {
  padding-bottom: 20px;
}

.pab-25 {
  padding-bottom: 25px;
}

.pab-30 {
  padding-bottom: 30px;
}

.pab-35 {
  padding-bottom: 35px;
}

.pab-40 {
  padding-bottom: 40px;
}

.pab-45 {
  padding-bottom: 45px;
}

.pab-50 {
  padding-bottom: 50px;
}

.pab-55 {
  padding-bottom: 55px;
}

.pab-60 {
  padding-bottom: 60px;
}

.pab-65 {
  padding-bottom: 65px;
}

.pab-70 {
  padding-bottom: 70px;
}

.pab-75 {
  padding-bottom: 75px;
}

.pab-80 {
  padding-bottom: 80px;
}

.pab-85 {
  padding-bottom: 85px;
}

.pab-90 {
  padding-bottom: 90px;
}

.pab-95 {
  padding-bottom: 95px;
}

.pab-100 {
  padding-bottom: 100px;
}

.pab-105 {
  padding-bottom: 105px;
}

.pab-110 {
  padding-bottom: 110px;
}

.pab-115 {
  padding-bottom: 115px;
}

.pab-120 {
  padding-bottom: 120px;
}

.pab-125 {
  padding-bottom: 125px;
}

.pab-130 {
  padding-bottom: 130px;
}

.pab-135 {
  padding-bottom: 135px;
}

.pab-140 {
  padding-bottom: 140px;
}

.pab-145 {
  padding-bottom: 145px;
}

.pab-150 {
  padding-bottom: 150px;
}

.pab-155 {
  padding-bottom: 155px;
}

.pab-160 {
  padding-bottom: 160px;
}

.pab-165 {
  padding-bottom: 165px;
}

.pab-170 {
  padding-bottom: 170px;
}

.pab-175 {
  padding-bottom: 175px;
}

.pab-180 {
  padding-bottom: 180px;
}

.pab-185 {
  padding-bottom: 185px;
}

.pab-190 {
  padding-bottom: 190px;
}

.pab-195 {
  padding-bottom: 195px;
}

.pab-200 {
  padding-bottom: 200px;
}

/*-- Padding Left --*/
.pal-5 {
  padding-left: 5px;
}

.pal-10 {
  padding-left: 10px;
}

.pal-15 {
  padding-left: 15px;
}

.pal-20 {
  padding-left: 20px;
}

.pal-25 {
  padding-left: 25px;
}

.pal-30 {
  padding-left: 30px;
}

.pal-35 {
  padding-left: 35px;
}

.pal-40 {
  padding-left: 40px;
}

.pal-45 {
  padding-left: 45px;
}

.pal-50 {
  padding-left: 50px;
}

.pal-55 {
  padding-left: 55px;
}

.pal-60 {
  padding-left: 60px;
}

.pal-65 {
  padding-left: 65px;
}

.pal-70 {
  padding-left: 70px;
}

.pal-75 {
  padding-left: 75px;
}

.pal-80 {
  padding-left: 80px;
}

.pal-85 {
  padding-left: 85px;
}

.pal-90 {
  padding-left: 90px;
}

.pal-95 {
  padding-left: 95px;
}

.pal-100 {
  padding-left: 100px;
}

.pal-105 {
  padding-left: 105px;
}

.pal-110 {
  padding-left: 110px;
}

.pal-115 {
  padding-left: 115px;
}

.pal-120 {
  padding-left: 120px;
}

.pal-125 {
  padding-left: 125px;
}

.pal-130 {
  padding-left: 130px;
}

.pal-135 {
  padding-left: 135px;
}

.pal-140 {
  padding-left: 140px;
}

.pal-145 {
  padding-left: 145px;
}

.pal-150 {
  padding-left: 150px;
}

.pal-155 {
  padding-left: 155px;
}

.pal-160 {
  padding-left: 160px;
}

.pal-165 {
  padding-left: 165px;
}

.pal-170 {
  padding-left: 170px;
}

.pal-175 {
  padding-left: 175px;
}

.pal-180 {
  padding-left: 180px;
}

.pal-185 {
  padding-left: 185px;
}

.pal-190 {
  padding-left: 190px;
}

.pal-195 {
  padding-left: 195px;
}

.pal-200 {
  padding-left: 200px;
}

/*-- Padding Right --*/
.par-5 {
  padding-right: 5px;
}

.par-10 {
  padding-right: 10px;
}

.par-15 {
  padding-right: 15px;
}

.par-20 {
  padding-right: 20px;
}

.par-25 {
  padding-right: 25px;
}

.par-30 {
  padding-right: 30px;
}

.par-35 {
  padding-right: 35px;
}

.par-40 {
  padding-right: 40px;
}

.par-45 {
  padding-right: 45px;
}

.par-50 {
  padding-right: 50px;
}

.par-55 {
  padding-right: 55px;
}

.par-60 {
  padding-right: 60px;
}

.par-65 {
  padding-right: 65px;
}

.par-70 {
  padding-right: 70px;
}

.par-75 {
  padding-right: 75px;
}

.par-80 {
  padding-right: 80px;
}

.par-85 {
  padding-right: 85px;
}

.par-90 {
  padding-right: 90px;
}

.par-95 {
  padding-right: 95px;
}

.par-100 {
  padding-right: 100px;
}

.par-105 {
  padding-right: 105px;
}

.par-110 {
  padding-right: 110px;
}

.par-115 {
  padding-right: 115px;
}

.par-120 {
  padding-right: 120px;
}

.par-125 {
  padding-right: 125px;
}

.par-130 {
  padding-right: 130px;
}

.par-135 {
  padding-right: 135px;
}

.par-140 {
  padding-right: 140px;
}

.par-145 {
  padding-right: 145px;
}

.par-150 {
  padding-right: 150px;
}

.par-155 {
  padding-right: 155px;
}

.par-160 {
  padding-right: 160px;
}

.par-165 {
  padding-right: 165px;
}

.par-170 {
  padding-right: 170px;
}

.par-175 {
  padding-right: 175px;
}

.par-180 {
  padding-right: 180px;
}

.par-185 {
  padding-right: 185px;
}

.par-190 {
  padding-right: 190px;
}

.par-195 {
  padding-right: 195px;
}

.par-200 {
  padding-right: 200px;
}

/*--------------------------------------------------------------
# Clearings
--------------------------------------------------------------*/
.clear:before,
.clear:after,
.entry-content:before,
.entry-content:after,
.comment-content:before,
.comment-content:after,
.site-header:before,
.site-header:after,
.site-content:before,
.site-content:after,
.site-footer:before,
.site-footer:after {
  content: "";
  display: table;
  table-layout: fixed;
}

.clear:after,
.entry-content:after,
.comment-content:after,
.site-header:after,
.site-content:after,
.site-footer:after {
  clear: both;
}

/* Preloader Css */
.preloader-inner {
  position: fixed;
  height: 100vh;
  width: 100%;
  left: 0%;
  top: 0%;
  z-index: 99990;
  background-color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.preloader-inner span {
  display: inline-block;
  text-transform: uppercase;
  text-align: center;
  font-size: 70px;
  font-weight: 700;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
  color: var(--heading-color);
  -webkit-text-stroke: 2px var(--heading-color);
}

@media only screen and (max-width: 480px) {
  .preloader-inner span {
    font-size: 50px;
  }
}

.preloader-inner span:nth-child(1) {
  -webkit-animation: scaling 1s linear infinite;
  animation: scaling 1s linear infinite;
}

.preloader-inner span:nth-child(2) {
  -webkit-animation: scaling 1s linear infinite 0.125s;
  animation: scaling 1s linear infinite 0.125s;
}

.preloader-inner span:nth-child(3) {
  -webkit-animation: scaling 1s linear infinite 0.25s;
  animation: scaling 1s linear infinite 0.25s;
}

.preloader-inner span:nth-child(4) {
  -webkit-animation: scaling 1s linear infinite 0.375s;
  animation: scaling 1s linear infinite 0.375s;
}

.preloader-inner span:nth-child(5) {
  -webkit-animation: scaling 1s linear infinite 0.5s;
  animation: scaling 1s linear infinite 0.5s;
}

.preloader-inner span:nth-child(6) {
  -webkit-animation: scaling 1s linear infinite 0.675s;
  animation: scaling 1s linear infinite 0.675s;
}

.preloader-inner span:nth-child(7) {
  -webkit-animation: scaling 1s linear infinite 0.75s;
  animation: scaling 1s linear infinite 0.75s;
}

@-webkit-keyframes scaling {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    color: var(--main-color-one);
    -webkit-text-stroke: 2px var(--main-color-one);
  }

  20% {
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
    color: var(--main-color-one);
    -webkit-text-stroke: 3px var(--main-color-one);
  }

  50% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    color: var(--main-color-one);
    -webkit-text-stroke: 2px var(--main-color-one);
  }
}

@keyframes scaling {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    color: var(--main-color-one);
    -webkit-text-stroke: 2px var(--main-color-one);
  }

  20% {
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
    color: var(--main-color-one);
    -webkit-text-stroke: 3px var(--main-color-one);
  }

  50% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    color: var(--main-color-one);
    -webkit-text-stroke: 2px var(--main-color-one);
  }
}

/*---------------------------------------
    ## Button
---------------------------------------*/
.btn-wrapper {
  display: block;
}

.cmn-btn {
  color: var(--paragraph-color);
  border: 1px solid inherit;
  font-size: 16px;
  font-weight: 500;
  font-family: var(--body-font);
  display: inline-block;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  line-height: 24px;
  padding: 12px 35px;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

@media only screen and (max-width: 575.98px) {
  .cmn-btn {
    padding: 10px 25px;
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .cmn-btn {
    padding: 5px 15px;
    font-size: 14px;
  }
}

.cmn-btn.btn-bg-1 {
  background: var(--main-color-one);
  color: #fff;
  border: 1px solid transparent;
}

.cmn-btn.btn-bg-1:hover {
  background: var(--secondary-color);
}

.cmn-btn.btn-bg-secondary {
  background: var(--secondary-color);
  color: #000;
  border: 2px solid transparent;
}

.cmn-btn.btn-bg-secondary:hover {
  background: rgba(var(--secondary-color-rgb), 0.8);
}

.cmn-btn.btn-bg-gray {
  background-color: var(--border-color);
  color: var(--heading-color);
  border: 2px solid transparent;
}

.cmn-btn.btn-bg-gray:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.cmn-btn.btn-bg-white {
  background-color: #fff;
  color: var(--heading-color);
  border: 2px solid #fff;
}

.cmn-btn.btn-bg-white:hover {
  background-color: var(--main-color-one);
  border-color: #fff;
  color: #fff;
  border-radius: 30px;
}

.cmn-btn.btn-border {
  border: 1px solid var(--border-color);
}

.cmn-btn.btn-border:hover {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

.cmn-btn.btn-outline-1 {
  padding: 12px 35px;
  border: 1px solid var(--main-color-one);
}

.cmn-btn.btn-outline-1.color-one {
  color: var(--main-color-one);
}

.cmn-btn.btn-outline-1:hover {
  background: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .cmn-btn.btn-outline-1 {
    padding: 8px 23px;
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .cmn-btn.btn-outline-1 {
    padding: 5px 13px;
    font-size: 14px;
  }
}

.cmn-btn.btn-outline-white {
  padding: 7px 35px;
  border: 2px solid #fff;
  color: #fff;
}

.cmn-btn.btn-outline-white:hover {
  background: #fff;
  color: var(--main-color-one);
}

@media only screen and (max-width: 575.98px) {
  .cmn-btn.btn-outline-white {
    padding: 8px 23px;
    font-size: 15px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 375px) {
  .cmn-btn.btn-outline-white {
    padding: 4px 13px;
    font-size: 14px;
  }
}

.cmn-btn.btn-medium {
  line-height: 24px;
  padding: 7px 20px;
}

@media only screen and (max-width: 375px) {
  .cmn-btn.btn-medium {
    padding: 8px 15px;
    font-size: 14px;
  }
}

.cmn-btn.btn-small {
  line-height: 24px;
  padding: 6px 15px;
}

@media only screen and (max-width: 375px) {
  .cmn-btn.btn-small {
    padding: 6px 12px;
    font-size: 14px;
  }
}

.center-text .flex-btn {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.flex-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px 10px;
}

.hire-btn {
  font-weight: 400;
  color: var(--light-color);
  font-size: 16px;
  border: 1px solid var(--border-color);
  padding: 7px 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.hire-btn::after {
  content: "\f061";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 16px;
}

.hire-btn:hover {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

/* btn profile */
.btn-profile {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 500;
  font-family: var(--body-font);
  display: inline-block;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  line-height: 24px;
  padding: 7px 20px;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  border: none;
  background-color: unset;
}

@media only screen and (max-width: 575.98px) {
  .btn-profile {
    padding: 6px 20px;
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-profile {
    padding: 5px 15px;
    font-size: 14px;
  }
}

.btn-profile.btn-bg-1 {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-profile.btn-bg-1:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
}

.btn-profile.btn-bg-cancel {
  background-color: var(--danger-color);
  color: #fff;
}

.btn-profile.btn-bg-cancel:hover {
  background-color: rgba(var(--danger-color-rgb), 0.8);
}

.btn-profile.btn-outline-cancel {
  border: 1px solid var(--danger-color);
  color: var(--danger-color);
  padding: 6px 19px;
}

.btn-profile.btn-outline-cancel:hover {
  background-color: var(--danger-color);
  color: #fff;
}

.btn-profile.btn-outline-1 {
  border: 1px solid var(--main-color-one);
  color: var(--main-color-one);
  padding: 6px 19px;
}

@media only screen and (max-width: 575.98px) {
  .btn-profile.btn-outline-1 {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-profile.btn-outline-1 {
    padding: 4px 15px;
  }
}

.btn-profile.btn-outline-1.color-one {
  color: var(--main-color-one);
}

.btn-profile.btn-outline-1:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-profile.btn-outline-gray {
  border: 1px solid var(--border-color);
  padding: 6px 19px;
}

.btn-profile.btn-outline-gray:hover {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .btn-profile.btn-outline-gray {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-profile.btn-outline-gray {
    padding: 4px 15px;
  }
}

.btn-profile.btn-medium {
  padding: 5px 15px;
  font-size: 15px;
}

.btn-profile.btn-small {
  padding: 5px 10px;
  font-size: 15px;
}

.btn-profile.extra-width {
  padding-inline: 50px;
}

.btn-profile.btn-hover-danger:hover {
  background-color: var(--danger-color);
  color: #fff;
  border-color: var(--danger-color);
}

/* Btn Delete Edit */
.btn-crud {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 15px;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  border-radius: 5px;
}

.btn-crud.btn-bg-1 {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-crud.btn-bg-1:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
}

.btn-crud.btn-bg-cancel {
  background-color: var(--danger-color);
  color: #fff;
}

.btn-crud.btn-bg-cancel:hover {
  background-color: rgba(var(--danger-color), 0.5);
}

.btn-crud.btn-outline-1 {
  border: 1px solid var(--main-color-one);
  padding: 6px 19px;
}

@media only screen and (max-width: 575.98px) {
  .btn-crud.btn-outline-1 {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-crud.btn-outline-1 {
    padding: 4px 15px;
  }
}

.btn-crud.btn-outline-1.color-one {
  color: var(--main-color-one);
}

.btn-crud.btn-outline-1:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-crud.btn-outline-gray {
  border: 1px solid var(--border-color);
  padding: 6px 19px;
}

.btn-crud.btn-outline-gray:hover {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .btn-crud.btn-outline-gray {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-crud.btn-outline-gray {
    padding: 4px 15px;
  }
}

.btn-crud.btn-medium {
  padding: 5px 15px;
  font-size: 15px;
}

.btn-crud.btn-small {
  padding: 5px 10px;
  font-size: 15px;
}

.btn-crud.btn-hover-danger:hover {
  background-color: var(--danger-color);
  color: #fff;
  border-color: var(--danger-color);
}

/* Btn Notification */
.btn-notification {
  color: var(--paragraph-color);
  font-size: 15px;
  font-weight: 500;
  font-family: var(--body-font);
  display: inline-block;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  line-height: 20px;
  padding: 5px 10px;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.btn-notification.btn-bg-1 {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-notification.btn-bg-1:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
}

.btn-notification.btn-outline-1 {
  border: 1px solid var(--main-color-one);
  padding: 4px 9px;
}

.btn-notification.btn-outline-1.color-one {
  color: var(--main-color-one);
}

.btn-notification.btn-outline-1:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-notification.btn-outline-gray {
  border: 1px solid var(--border-color);
  padding: 4px 9px;
}

.btn-notification.btn-outline-gray:hover {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .btn-notification.btn-outline-gray {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-notification.btn-outline-gray {
    padding: 4px 15px;
  }
}

.btn-notification.btn-hover-danger:hover {
  background-color: var(--danger-color);
  color: #fff;
  border-color: var(--danger-color);
}

/*---------------------------------------
    ## Back Top
---------------------------------------*/
.back-to-top {
  position: fixed;
  right: 30px;
  bottom: 30px;
  width: 50px;
  height: 50px;
  background-color: var(--main-color-one);
  color: #fff;
  text-align: center;
  line-height: 50px;
  border-radius: 5px;
  z-index: 99;
  font-size: 25px;
  cursor: pointer;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  display: none;
}

@media only screen and (max-width: 575.98px) {
  .back-to-top {
    height: 40px;
    width: 40px;
    font-size: 22px;
    line-height: 40px;
    bottom: 20px;
    right: 20px;
  }
}

.back-to-top.bg-color-two {
  background: var(--secondary-color);
}

.back-to-top.bg-color-three {
  background: var(--main-color-three);
}

.back-to-top.bg-color-four {
  background: var(--main-color-four);
}

.back-to-top.bg-color-five {
  background: var(--main-color-five);
}

/*-------------------------
    Section Title
-------------------------*/
 .section-title {
   max-width: 720px;
   margin-inline: auto;
 }
.section-title.text-left {
  text-align: left;
  max-width: unset;
  margin-inline: auto;
}

.section-title.white-color {
  color: #fff;
}

.section-title.white-color .title {
  color: #fff;
}

.section-title.center-text {
  text-align: center;
  max-width: 650px;
  margin: auto;
}

.section-title.center-text .subtitle {
  padding: 0 10px;
}

.section-title.center-text .subtitle::before {
  width: calc(100% + 300px);
  left: 50%;
  /* IE 9 */
  -webkit-transform: translate(-50%, -50%);
  /* Chrome, Safari, Opera */
  transform: translate(-50%, -50%);
}

.section-title.section-border-bottom {
  border-bottom: 1px solid rgba(221, 221, 221, 0.4);
  padding-bottom: 30px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .section-title.section-border-bottom {
    padding-bottom: 20px;
  }
}

.section-title .subtitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--main-color-one);
  position: relative;
  display: inline-block;
  background-color: #fff;
  margin-bottom: 15px;
  padding: 0 10px 0 0;
}

.section-title .subtitle::before {
  content: "";
  position: absolute;
  height: 2px;
  width: calc(100% + 70px);
  left: 105%;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  background-color: var(--main-color-one);
  z-index: -1;
}

.section-title .title {
  font-size: 42px;
  font-weight: 400;
  line-height: 1.2;
  color: var(--heading-color);
  font-family: var(--Otomanopee-font);
  position: relative;
  margin: -8px 0 0;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .section-title .title {
    font-size: 40px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .section-title .title {
    font-size: 36px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .section-title .title {
    font-size: 32px;
  }
}

@media only screen and (max-width: 575.98px) {
  .section-title .title {
    font-size: 28px;
    line-height: 32px;
  }
}

@media only screen and (max-width: 375px) {
  .section-title .title {
    font-size: 25px;
    line-height: 28px;
  }
}

.section-title .section-para {
  font-size: 18px;
  line-height: 28px;
  margin-top: 20px;
}

@media only screen and (max-width: 480px) {
  .section-title .btn-wrapper .view-all {
    font-size: 15px;
  }
}

.section-title-btn .view-all {
  padding: 10px 30px;
  font-size: 16px;
  font-weight: 400;
  color: var(--light-color);
  border: 1px solid var(--light-color);
  -webkit-transition: 300ms;
  transition: 300ms;
}

.section-title-btn .view-all:hover {
  background-color: var(--light-color);
  color: #fff;
}

/* Nice Select Style */
.js_nice_select {
  width: 100%;
  height: 55px;
  line-height: 55px;
  font-size: 15px;
  font-weight: 400;
  color: var(--paragraph-color);
  border: 1px solid var(--border-color);
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.01);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.01);
  -webkit-transition: 300ms;
  transition: 300ms;
  padding: 0 30px 0 15px;
}

.js_nice_select:focus {
  border: 1px solid var(--body-color);
}

.js_nice_select .nice-select-search-box {
  height: 100%;
}

.js_nice_select .nice-select-search-box .nice-select-search {
  vertical-align: top;
}

.js_nice_select:after {
  border-bottom: 2px solid var(--body-color);
  border-right: 2px solid var(--body-color);
  height: 7px;
  margin-top: -2px;
  right: 20px;
  width: 7px;
}

@media only screen and (max-width: 375px) {
  .js_nice_select:after {
    right: 10px;
  }
}

.js_nice_select.open {
  border: 1px solid #ddd;
}

.js_nice_select.open .list {
  width: 100%;
}

.js_nice_select .list {
  width: 100%;
  margin-top: 10px;
}

.js_nice_select .list .selected.focus {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
}

.js_nice_select .list .option:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  font-weight: 500;
}

.js-select.select-style-two {
  display: block;
  color: var(--paragraph-color);
  border: 1px solid var(--body-color);
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.js-select.select-style-two.open::after {
  border-color: transparent transparent var(--paragraph-color);
  border-width: 0px 4px 5px 4px;
}

.js-select.select-style-two:after {
  border-color: var(--paragraph-color) transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: auto;
  right: 11px;
  margin-top: 0px;
  position: absolute;
  top: 50%;
  width: 0;
  /* IE 9 */
  -webkit-transform: rotate(0deg);
  /* Chrome, Safari, Opera */
  transform: rotate(0deg);
}

@media only screen and (max-width: 375px) {
  .js-select.select-style-two:after {
    right: 10px;
  }
}

/* Mouse Cursor Css */
.mouse-move {
  position: fixed;
  left: 0;
  top: 0;
  pointer-events: none;
  border-radius: 50%;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  visibility: hidden;
}

.mouse-inner {
  margin-left: -3px;
  margin-top: -3px;
  width: 6px;
  height: 6px;
  z-index: 10000001;
  background-color: var(--main-color-one);
  -webkit-transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
  transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.mouse-inner.mouse-hover {
  margin-left: -30px;
  margin-top: -30px;
  width: 60px;
  height: 60px;
  background-color: var(--main-color-one);
  opacity: 0.1;
}

.mouse-outer {
  margin-left: -15px;
  margin-top: -15px;
  width: 30px;
  height: 30px;
  border: 2px solid var(--main-color-one);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 10000000;
  opacity: 0.5;
  -webkit-transition: all 0.08s ease-out;
  transition: all 0.08s ease-out;
}

.mouse-outer.mouse-hover {
  opacity: 0;
}

.mouse-cursor-two .mouse-inner {
  background-color: var(--secondary-color);
}

.mouse-cursor-two .mouse-inner.mouse-hover {
  background-color: var(--secondary-color);
}

.mouse-cursor-two .mouse-outer {
  border-color: var(--secondary-color);
}

/* Slider Custom Css */
.nav-style-one {
  position: relative;
}

.nav-style-one:hover .prev-icon,
.nav-style-one:hover .next-icon {
  visibility: visible;
  opacity: 1;
}

.nav-style-one.nav-color-two .prev-icon,
.nav-style-one.nav-color-two .next-icon {
  background: var(--border-color);
}

.nav-style-one.nav-color-two .prev-icon:hover,
.nav-style-one.nav-color-two .next-icon:hover {
  background-color: var(--secondary-color);
  color: #fff;
}

.nav-style-one .prev-icon,
.nav-style-one .next-icon {
  position: absolute;
  left: -70px;
  bottom: 0%;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  font-size: 30px;
  cursor: pointer;
  z-index: 9;
  background: var(--border-color);
  color: var(--heading-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  visibility: visible;
  opacity: 0;
}

.nav-style-one .prev-icon:hover,
.nav-style-one .next-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

@media (min-width: 1400px) and (max-width: 1599.98px) {

  .nav-style-one .prev-icon,
  .nav-style-one .next-icon {
    left: -25px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {

  .nav-style-one .prev-icon,
  .nav-style-one .next-icon {
    left: -25px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {

  .nav-style-one .prev-icon,
  .nav-style-one .next-icon {
    left: -25px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {

  .nav-style-one .prev-icon,
  .nav-style-one .next-icon {
    left: -25px;
  }
}

@media only screen and (max-width: 575.98px) {

  .nav-style-one .prev-icon,
  .nav-style-one .next-icon {
    left: 0px;
  }
}

.nav-style-one .next-icon {
  left: auto;
  right: -70px;
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .nav-style-one .next-icon {
    right: -25px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .nav-style-one .next-icon {
    right: -25px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .nav-style-one .next-icon {
    right: -25px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .nav-style-one .next-icon {
    right: -25px;
  }
}

@media only screen and (max-width: 575.98px) {
  .nav-style-one .next-icon {
    right: 0px;
  }
}

.append-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px 5px;
}
.append-team,
.append-jobs,
.append-project,
.append-freelancer,
.append-search {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px;
}

.append-team.append-color-two .prev-icon:hover,
.append-team.append-color-two .next-icon:hover,
.append-jobs.append-color-two .prev-icon:hover,
.append-jobs.append-color-two .next-icon:hover,
.append-project.append-color-two .prev-icon:hover,
.append-project.append-color-two .next-icon:hover,
.append-freelancer.append-color-two .prev-icon:hover,
.append-freelancer.append-color-two .next-icon:hover,
.append-search.append-color-two .prev-icon:hover,
.append-search.append-color-two .next-icon:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: #fff;
}

.append-team .prev-icon,
.append-team .next-icon,
.append-jobs .prev-icon,
.append-jobs .next-icon,
.append-project .prev-icon,
.append-project .next-icon,
.append-freelancer .prev-icon,
.append-freelancer .next-icon,
.append-search .prev-icon,
.append-search .next-icon {
  font-size: 30px;
  cursor: pointer;
  border: 1px solid var(--border-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 18px;
  z-index: 9;
  color: var(--heading-color);
  background-color: var(--border-color);
  border-radius: 50%;
  -webkit-transition: 300ms;
  transition: 300ms;
}
@media screen and (max-width: 375px) {
  .append-team .prev-icon,
  .append-team .next-icon,
  .append-jobs .prev-icon,
  .append-jobs .next-icon,
  .append-project .prev-icon,
  .append-project .next-icon,
  .append-freelancer .prev-icon,
  .append-freelancer .next-icon,
  .append-search .prev-icon,
  .append-search .next-icon {
    height: 35px;
    width: 35px;
    font-size: 16px;
  }
}
.append-team .prev-icon:hover,
.append-team .next-icon:hover,
.append-jobs .prev-icon:hover,
.append-jobs .next-icon:hover,
.append-project .prev-icon:hover,
.append-project .next-icon:hover,
.append-freelancer .prev-icon:hover,
.append-freelancer .next-icon:hover,
.append-search .prev-icon:hover,
.append-search .next-icon:hover {
  border-color: var(--main-color-one);
  color: #fff;
  background-color: var(--main-color-one);
}

.append-team .prev-icon,
.append-jobs .prev-icon,
.append-project .prev-icon,
.append-freelancer .prev-icon,
.append-search .prev-icon {
  right: 55px;
}

.append-team .next-icon,
.append-jobs .next-icon,
.append-project .next-icon,
.append-freelancer .next-icon,
.append-search .next-icon {
  background-color: var(--main-color-one);
  color: var(--white);
}
.append-projectCategory,
.append-jobCategory,
.append-testimonial,
.append-testimonial2 {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 20px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  background-color: var(--main-color-one);
  padding: 10px;
  border-radius: 30px;
}
.append-projectCategory span,
.append-jobCategory span,
.append-testimonial span,
.append-testimonial2 span {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}
.append-projectCategory .prev-icon,
.append-projectCategory .next-icon,
.append-jobCategory .prev-icon,
.append-jobCategory .next-icon,
.append-testimonial .prev-icon,
.append-testimonial .next-icon,
.append-testimonial2 .prev-icon,
.append-testimonial2 .next-icon {
  font-size: 30px;
  cursor: pointer;
  border: 1px solid var(--border-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 35px;
  width: 35px;
  font-size: 16px;
  z-index: 9;
  color: var(--heading-color);
  background-color: var(--border-color);
  border-radius: 50%;
  -webkit-transition: 300ms;
  transition: 300ms;
}
.append-projectCategory .prev-icon:hover,
.append-projectCategory .next-icon:hover,
.append-jobCategory .prev-icon:hover,
.append-jobCategory .next-icon:hover,
.append-testimonial .prev-icon:hover,
.append-testimonial .next-icon:hover,
.append-testimonial2 .prev-icon:hover,
.append-testimonial2 .next-icon:hover {
  border-color: var(--secondary-color);
  color: #fff;
  background-color: var(--secondary-color);
}
.append-projectCategory .prev-icon,
.append-jobCategory .prev-icon,
.append-testimonial .prev-icon,
.append-testimonial2 .prev-icon {
  right: 55px;
}

.append-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: 20px;
  position: relative;
}

.append-nav:hover .prev-icon,
.append-nav:hover .next-icon {
  visibility: visible;
  opacity: 1;
}

.append-nav .prev-icon,
.append-nav .next-icon {
  font-size: 20px;
  cursor: pointer;
  z-index: 9;
  background: var(--border-color);
  color: var(--main-color-one);
  -webkit-transition: 300ms;
  transition: 300ms;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  border-radius: 5px;
}

.append-nav .prev-icon:hover,
.append-nav .next-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.append-nav .next-icon {
  background-color: var(--main-color-one);
  color: #fff;
}

.append-dot {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.append-dot .slick-dots {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

.append-dot .slick-dots li {
  text-indent: -9999px;
  height: 10px;
  width: 10px;
  background: #DDD;
  border-radius: 50%;
  display: inline-block;
  margin: 0 5px;
  cursor: pointer;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  position: relative;
}

.append-dot .slick-dots li::before {
  content: "";
  position: absolute;
  height: calc(100% + 8px);
  width: calc(100% + 8px);
  background-color: transparent;
  border: 1px solid var(--main-color-one);
  top: -4px;
  left: -4px;
  border-radius: 50%;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.append-dot .slick-dots li.slick-active {
  background: var(--main-color-one);
}

.append-dot .slick-dots li.slick-active::before {
  visibility: visible;
  opacity: 1;
}

.append-dot .slick-dots li button {
  border: 0;
  outline: none;
}

/* Dot Style */
.dot-style-one.white-dot .slick-dots li {
  background-color: rgba(255, 255, 255, 0.6);
}

.dot-style-one.white-dot .slick-dots li.slick-active {
  background: #fff;
}

.dot-style-one.dot-absolute .slick-dots {
  position: absolute;
  bottom: 30px;
  right: 0px;
}

@media only screen and (max-width: 480px) {
  .dot-style-one.dot-absolute .slick-dots {
    bottom: 20px;
  }
}

@media only screen and (max-width: 375px) {
  .dot-style-one.dot-absolute .slick-dots {
    bottom: 10px;
  }
}

.dot-style-one.dot-color-two .slick-dots li {
  background-color: #DDD;
}

.dot-style-one.dot-color-two .slick-dots li.slick-active {
  background: var(--main-color-two);
}

.dot-style-one .slick-dots {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-top: 30px;
  padding: 10px 0 0;
}

.dot-style-one .slick-dots li {
  text-indent: -9999px;
  height: 10px;
  width: 10px;
  background: #DDD;
  border-radius: 50%;
  display: inline-block;
  margin: 0 5px;
  cursor: pointer;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.dot-style-one .slick-dots li.slick-active {
  background: var(--main-color-one);
  width: 30px;
  border-radius: 10px;
}

.dot-style-one .slick-dots li button {
  border: 0;
  outline: none;
}

.slider-inner-margin .slick-slide {
  margin: 12px;
}

.slider-inner-margin .slick-list {
  margin: -12px;
}
.slick-track {
  margin-left: 0;
}

.slider-inner-margin-10 .slick-slide {
  margin: 5px;
}

.slider-inner-margin-10 .slick-list {
  margin: -5px;
}

.slider-inner-padding .slick-slide {
  padding: 12px;
}

.slider-inner-padding .slick-list {
  padding: -12px;
}

/*---------------------------------------
    11. Faq
---------------------------------------*/
.faq-question.sticky-top {
  z-index: 0;
  top: 80px;
}
.faq-contents .faq-item {
  padding: 0;
  border: 1px solid var(--border-color);
  border-radius: 10px;
}

.faq-contents .faq-item.open {
  border-radius: 10px;
  overflow: hidden;
  background-color: #fff;
  padding: 0px;
  -webkit-box-shadow: 0 0 50px #f0f0f0;
  box-shadow: 0 0 50px #f0f0f0;
  border: 0;
}

.faq-contents .faq-item.open .faq-title {
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.faq-contents .faq-item.open .faq-title::after {
  /* IE 9 */
  -webkit-transform: translateY(-50%) rotate(180deg);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%) rotate(180deg);
}

.faq-contents .faq-item.open .faq-panel .faq-para {
  color: var(--light-color);
}

.faq-contents .faq-item.active .faq-panel {
  display: block;
}

.faq-contents .faq-item:not(:first-child) {
  margin-top: 24px;
}

.faq-contents .faq-item .faq-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  color: var(--heading-color);
  font-family: var(--heading-font);
  padding: 20px;
  background: none;
  border-radius: 10px;
  cursor: pointer;
  position: relative;
  z-index: 1;
  -webkit-transition: 300ms;
  transition: 300ms;
  margin-right: 30px;
}

.faq-contents .faq-item .faq-title::after {
  content: "\f078";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  right: -4px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: 300ms;
  transition: 300ms;
  font-size: 14px;
}

.faq-contents .faq-item .faq-panel {
  padding: 20px;
  display: none;
  border-top: 1px solid var(--border-color);
}

.faq-contents .faq-item .faq-panel .faq-para {
  font-size: 15px;
  line-height: 26px;
  color: var(--paragraph-color);
}

@media only screen and (max-width: 767.98px) {
  .faq-contents .faq-item .faq-panel .faq-para {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .faq-contents .faq-item .faq-panel .faq-para {
    font-size: 14px;
  }
}

.faq-question-padding {
  padding-right: 100px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .faq-question-padding {
    padding-right: 50px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .faq-question-padding {
    padding-right: 30px;
  }
}

@media only screen and (max-width: 575.98px) {
  .faq-question-padding {
    padding-right: 30px;
  }
}

@media only screen and (max-width: 480px) {
  .faq-question-padding {
    padding-right: 20px;
  }
}

@media only screen and (max-width: 375px) {
  .faq-question-padding {
    padding-right: 10px;
  }
}

.faq-question-submit {
  position: relative;
  display: inline-block;
}

.faq-question-submit::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background-color: #ffdcae;
  -webkit-filter: blur(100px);
  filter: blur(100px);
}

.faq-question-submit-thumb img {
  -webkit-animation: 10s roundRotate linear infinite;
  animation: 10s roundRotate linear infinite;
}

.faq-question-submit-icon {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  display: table;
}

.faq-question-submit-icon .faq-svg-path {
  -webkit-animation: 4s upDown linear infinite;
  animation: 4s upDown linear infinite;
}

@-webkit-keyframes upDown {
  0% {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
    opacity: 0.5;
    color: var(--heading-color);
    fill: var(--heading-color);
  }

  50% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    color: var(--main-color-one);
    fill: var(--main-color-one);
  }

  100% {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
    opacity: 0.5;
    color: var(--secondary-color);
    fill: var(--secondary-color);
  }
}

@keyframes upDown {
  0% {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
    opacity: 0.5;
    color: var(--heading-color);
    fill: var(--heading-color);
  }

  50% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    color: var(--main-color-one);
    fill: var(--main-color-one);
  }

  100% {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
    opacity: 0.5;
    color: var(--secondary-color);
    fill: var(--secondary-color);
  }
}

@-webkit-keyframes roundRotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  50% {
    -webkit-transform: rotate(0px);
    transform: rotate(0px);
  }

  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

@keyframes roundRotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  50% {
    -webkit-transform: rotate(0px);
    transform: rotate(0px);
  }

  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

/* Faq tab block */
.faq-flex-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 50px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .faq-flex-wrapper {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
  }
}

.faq-flex-wrapper .faq-flex-list {
  -ms-flex-preferred-size: 40%;
  flex-basis: 40%;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .faq-flex-wrapper .faq-flex-list {
    -ms-flex-preferred-size: 35%;
    flex-basis: 35%;
  }
}

.faq-flex-wrapper .faq-flex-item {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.faq-block-tab.tabs {
  display: block;
}

.faq-block-tab.tabs li {
  display: block;
  background-color: #f3f3f3;
  color: var(--heading-color);
  padding: 10px 20px;
  border-radius: 5px;
  position: relative;
}

.faq-block-tab.tabs li:not(:last-child) {
  margin-bottom: 20px;
}

.faq-block-tab.tabs li::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 3px;
  top: 0;
  right: 0;
  background-color: transparent;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.faq-block-tab.tabs li.active {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
}

.faq-block-tab.tabs li.active::after {
  background-color: var(--main-color-one);
}

/* Contact Support Faq Css */
.question-faq-wrapper .faq-contents .faq-item {
  padding: 0;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  background-color: #fff;
}

.question-faq-wrapper .faq-contents .faq-item.open {
  -webkit-box-shadow: none;
  box-shadow: none;
  background-color: var(--main-color-one);
}

.question-faq-wrapper .faq-contents .faq-item.open .faq-title {
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #fff;
  background-color: unset;
}

.question-faq-wrapper .faq-contents .faq-item.open .faq-title::after {
  /* IE 9 */
  -webkit-transform: translateY(-50%) rotate(180deg);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%) rotate(180deg);
  color: #fff;
}

.question-faq-wrapper .faq-contents .faq-item.open .faq-panel {
  border-color: rgba(255, 255, 255, 0.2);
}

.question-faq-wrapper .faq-contents .faq-item.open .faq-panel .faq-para {
  color: rgba(255, 255, 255, 0.8);
}

.question-faq-wrapper .faq-contents .faq-item .faq-title {
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  color: var(--heading-color);
  font-family: var(--heading-font);
  background: unset;
}

/* Custom Form */
.single-flex-input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 24px;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

@media only screen and (max-width: 480px) {
  .single-flex-input {
    display: block;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}

.custom-form .single-flex-input:not(:first-child) {
  margin-top: 25px;
}

.custom-form .single-flex-input .single-input,
.custom-form .single-flex-input .form-group {
  margin-top: 0 !important;
}

.custom-form .single-flex-input .single-input .js_nice_select,
.custom-form .single-flex-input .form-group .js_nice_select {
  width: 100%;
}

.custom-form .single-input,
.custom-form .form-group {
  width: 100%;
}

.custom-form .single-input:not(:first-child),
.custom-form .form-group:not(:first-child) {
  margin-top: 25px;
}

.custom-form .single-input .label-title,
.custom-form .form-group .label-title {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  display: block;
  margin-bottom: 10px;
  color: var(--heading-color);
}

@media only screen and (max-width: 480px) {

  .custom-form .single-input .label-title,
  .custom-form .form-group .label-title {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {

  .custom-form .single-input .label-title,
  .custom-form .form-group .label-title {
    font-size: 14px;
  }
}

.custom-form .single-input .input-icon,
.custom-form .form-group .input-icon {
  position: absolute;
  bottom: 7px;
  left: 7px;
}

.custom-form .single-input .iti,
.custom-form .form-group .iti {
  width: 100%;
}
.single-shop-left-inner .form-control,
.price-range-input > input,
.single-input .form-control,
.custom-form .single-input .form--control,
.custom-form .single-input .form-control,
.custom-form .form-group .form--control,
.custom-form .form-group .form-control {
  -moz-appearance: textfield;
  width: 100%;
  height: 38px;
  line-height: 38px;
  padding: 0 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--paragraph-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  font-size: 16px;
}
.single-input label,
.custom-form .single-input label,
.custom-form .form-group label {
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  color: var(--heading-color);
  margin-bottom: 8px;
}

.custom-form .single-input .form--control.input-padding-left,
.custom-form .single-input .form-control.input-padding-left,
.custom-form .form-group .form--control.input-padding-left,
.custom-form .form-group .form-control.input-padding-left {
  padding-left: 45px;
}

.custom-form .single-input .form--control#phone,
.custom-form .single-input .form-control#phone,
.custom-form .form-group .form--control#phone,
.custom-form .form-group .form-control#phone {
  width: 100%;
  padding-left: 50px;
}

.custom-form .single-input .form--control::-webkit-outer-spin-button,
.custom-form .single-input .form--control::-webkit-inner-spin-button,
.custom-form .single-input .form-control::-webkit-outer-spin-button,
.custom-form .single-input .form-control::-webkit-inner-spin-button,
.custom-form .form-group .form--control::-webkit-outer-spin-button,
.custom-form .form-group .form--control::-webkit-inner-spin-button,
.custom-form .form-group .form-control::-webkit-outer-spin-button,
.custom-form .form-group .form-control::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
  -moz-appearance: textfield;
}

.custom-form .single-input .form--control:focus,
.custom-form .single-input .form-control:focus,
.custom-form .form-group .form--control:focus,
.custom-form .form-group .form-control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

@media only screen and (max-width: 480px) {

  .custom-form .single-input .form--control,
  .custom-form .single-input .form-control,
  .custom-form .form-group .form--control,
  .custom-form .form-group .form-control {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {

  .custom-form .single-input .form--control,
  .custom-form .single-input .form-control,
  .custom-form .form-group .form--control,
  .custom-form .form-group .form-control {
    font-size: 14px;

  }
}

.single-input textarea,
.single-input textarea.form-control,
.custom-form .single-input textarea,
.custom-form .form-group textarea,
.custom-form .single-input .form-message,
.custom-form .form-group .form-message {
  width: 100%;
  line-height: 24px;
  padding: 5px 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--paragraph-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  height: unset !important;
}

.custom-form .single-input .form-message:focus,
.custom-form .form-group .form-message:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.custom-form .single-input .form-message.textarea-height,
.custom-form .form-group .form-message.textarea-height {
  height: 100px;
}

.custom-form .single-input-icon,
.custom-form .form-group-icon {
  position: relative;
}

.custom-form .single-input-icon::after,
.custom-form .form-group-icon::after {
  content: "";
  position: absolute;
  height: 38px;
  width: 2px;
  background-color: #f3f3f3;
  bottom: 0;
  left: 40px;
}

.custom-form .single-input-icon .form--control,
.custom-form .single-input-icon .form-control,
.custom-form .form-group-icon .form--control,
.custom-form .form-group-icon .form-control {
  padding-left: 50px;
  position: relative;
}

.custom-form .submit-btn {
  margin-top: 25px;
}

.submit-btn {
  border: 2px solid var(--main-color-one);
  background-color: var(--main-color-one);
  color: #fff;
  padding: 12px 20px;
  -webkit-transition: 300ms;
  transition: 300ms;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  border-radius: 5px;
}

.submit-btn:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.9);
}

.photo-uploaded {
  background-color: #f3f3f3;
  border-radius: 10px;
  border: 2px dashed #ddd;
  cursor: pointer;
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  padding: 10px;
}

.photo-uploaded-padding {
  padding: 20px;
}

.photo-uploaded.center-text .photo-uploaded-flex {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.photo-uploaded-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
}

.photo-uploaded-icon {
  font-size: 28px;
  color: var(--light-color);
}

.photo-uploaded-para {
  font-size: 16px;
  color: var(--paragraph-color);
}


.photo-uploaded-file {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  cursor: pointer;
  opacity: 0;
}

/* Toggle Password hide Show */
.single-input {
  position: relative;
}

.toggle-password {
  position: absolute;
  bottom: 7px;
  right: 20px;
  cursor: pointer;
}

.hide-icon {
  display: none;
}

.toggle-password.show-pass .hide-icon {
  display: block;
}

.toggle-password.show-pass .show-icon {
  display: none;
}

.checkbox-inline {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  cursor: pointer;
  gap: 10px;
}

.checkbox-inline .check-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 18px;
  width: 18px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dddddd;
  border-radius: 0px;
  margin-top: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.checkbox-inline .check-input::after {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 10px;
  color: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transform: scale(1.6) rotate(90deg);
  transform: scale(1.6) rotate(90deg);
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.checkbox-inline .check-input:checked {
  background: var(--main-color-one);
  border-color: var(--main-color-one);
  background: var(--main-color-one);
}

.checkbox-inline .check-input:checked::after {
  visibility: visible;
  opacity: 1;
  -webkit-transform: scale(1.2) rotate(0deg);
  transform: scale(1.2) rotate(0deg);
}

.checkbox-inline .checkbox-label {
  cursor: pointer;
  text-align: left;
  line-height: 26px;
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
  margin: 0;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media only screen and (max-width: 575.98px) {
  .checkbox-inline .checkbox-label {
    font-size: 15px;
  }
}

.checkbox-inline .checkbox-label a {
  color: var(--main-color-one);
  font-weight: 500;
}

/* Breadcrumb area */
.breadcrumb-area {
  position: relative;
}

.breadcrumb-padding {
  padding: 30px 0 0;
}

.breadcrumb-border {
  border-top: 2px solid rgba(221, 221, 221, 0.4);
}

.breadcrumb-contents {
  background: #fff;
  padding: 10px 0px 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  border-radius: 8px;
}

.breadcrumb-contents-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.breadcrumb-contents-title {
  font-size: 20px;
  line-height: 28px;
  color: var(--heading-color);
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

@media only screen and (max-width: 575.98px) {
  .breadcrumb-contents-title {
    font-size: 20px;
    line-height: 28px;
  }
}

.breadcrumb-contents-para {
  font-size: 16px;
  line-height: 24px;
  color: var(--paragraph-color);
}

@media only screen and (max-width: 375px) {
  .breadcrumb-contents-para {
    font-size: 15px;
    line-height: 22px;
  }
}

.inner-menu,
.breadcrumb-contents-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: -5px;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  margin: 0;
  padding: 0;
  list-style: none;
}

.inner-menu {
  gap: 10px;
}

.inner-menu li,
.breadcrumb-contents-list-item {
  font-size: 16px;
  font-weight: 500;
  color: var(--paragraph-color);
  position: relative;
  -webkit-transition: 200ms;
  transition: 200ms;
}

@media (min-width: 300px) and (max-width: 991.98px) {

  .inner-menu li,
  .breadcrumb-contents-list-item {
    font-size: 15px;
  }
}

.inner-menu li:hover,
.breadcrumb-contents-list-item:hover {
  color: var(--main-color-one);
}

.inner-menu li:not(:last-child),
.breadcrumb-contents-list-item:not(:last-child) {
  padding-right: 20px;
}

@media only screen and (max-width: 480px) {

  .breadcrumb-contents-list-item:not(:last-child) {
    padding-right: 15px;
  }
}

@media only screen and (max-width: 375px) {

  .breadcrumb-contents-list-item:not(:last-child) {
    padding-right: 10px;
  }
}

.inner-menu li:not(:last-child)::after,
.breadcrumb-contents-list-item:not(:last-child)::after {
  position: absolute;
  right: -8px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  content: "\f105";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

.inner-menu li:not(:last-child)::after {
  right: 1px;
}

.inner-menu li:not(:last-child),
.breadcrumb-contents-list-item:not(:first-child) {
  padding-left: 20px;
}

@media only screen and (max-width: 480px) {


  .breadcrumb-contents-list-item:not(:first-child) {
    padding-left: 15px;
  }
}

@media only screen and (max-width: 375px) {


  .breadcrumb-contents-list-item:not(:first-child) {
    padding-left: 10px;
  }
}

.banner-inner-contents {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  flex-direction: row-reverse;
}

@media only screen and (max-width: 991.98px) {
  .banner-inner-contents {
    flex-direction: column-reverse;
  }
}

.banner-inner-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  color: var(--heading-color);
}

/* Ui Range Slider */
.price-range-slider {
  padding: 0 10px;
}

.price-range-slider .ui-range-slider.noUi-target {
  position: relative;
  border-radius: 3px;
  height: 3px;
}

.price-range-slider .ui-range-slider.noUi-target .noUi-handle {
  top: -11px;
  left: -3px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  z-index: 1;
  cursor: pointer;
  background: var(--main-color-one);
}

.price-range-slider .ui-range-slider.noUi-target .noUi-handle.noUi-handle-upper::before {
  background-color: var(--main-color-one);
}

.price-range-slider .ui-range-slider.noUi-target .noUi-handle::before {
  content: "";
  position: absolute;
  background: #fff;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: inline-block;
}

.price-range-slider .ui-range-slider.noUi-target .noUi-base .noUi-origin {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.price-range-slider .ui-range-slider.noUi-target .noUi-base .noUi-connect {
  background: var(--main-color-one);
}

.price-range-slider .ui-range-slider-footer {
  display: table;
  width: 100%;
  padding-top: 20px;
}

.price-range-slider .ui-range-slider-footer .ui-range-values {
  display: inline-block;
  color: var(--heading-color);
}

.price-range-slider .ui-range-slider-footer .ui-range-values .ui-price-title {
  font-size: 18px;
  font-weight: 400;
  margin-right: 10px;
  color: var(--light-color);
}

.price-range-slider .ui-range-slider-footer .ui-range-values .ui-range-value-min,
.price-range-slider .ui-range-slider-footer .ui-range-values .ui-range-value-max {
  display: inline-block;
  font-size: 18px;
  font-weight: 500;
  color: var(--main-color-one);
}

.noUi-background {
  background: #DDDDDD;
}

[dir=rtl] .price-range-slider .ui-range-slider.noUi-target .noUi-base .noUi-origin {
  right: auto;
  left: 0;
}

[dir=rtl] .price-range-slider .ui-range-slider.noUi-target .noUi-base .noUi-connect {
  background: var(--main-color-one);
}

/* Pagination style */
.pagination-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
}

.pagination-list-item-button {
  display: inline-block;
  font-size: 16px;
  color: var(--paragraph-color);
  border: 1px solid var(--border-color);
  background-color: var(--border-color);
  line-height: 1;
  padding: 9px 15px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pagination-list-item-button:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.pagination-list-item:hover .pagination-list-item-link {
  background-color: var(--main-color-one);
  color: #fff;
}

.pagination-list-item.active .pagination-list-item-link {
  background-color: var(--main-color-one);
  color: #fff;
}

.pagination-list-item-link {
  display: inline-block;
  font-size: 16px;
  color: var(--paragraph-color);
  border: 1px solid #fff;
  background-color: #fff;
  line-height: 1;
  padding: 9px 15px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

/* Global Ratings */
@media only screen and (max-width: 375px) {
  .center-text .rating-wrap {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

.rating-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.rating-wrap .ratings {
  position: relative;
  z-index: 0;
  width: 84px;
  height: 16px;
  display: inline-block;
}

@media only screen and (max-width: 480px) {
  .rating-wrap .ratings {
    width: 84px;
  }
}

@media only screen and (max-width: 375px) {
  .rating-wrap .ratings {
    width: 84px;
  }
}

.rating-wrap .ratings span {
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  line-height: 16px;
}

.rating-wrap .ratings span.hide-rating:after {
  color: var(--light-color);
}

.rating-wrap .ratings span.show-rating:after {
  color: #FABE50;
}

.rating-wrap .ratings span.show-rating {
  width: 90% !important;
}

.rating-wrap .ratings span:after {
  position: absolute;
  left: 0;
  top: 0;
  content: "\f005" "\f005" "\f005" "\f005" "\f005";
  width: 100%;
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 15px;
}

.rating-wrap .total-ratings {
  margin-left: 5px;
}

/* Tabs Design */
.tabs,
.tab-parents,
.tabs-two,
.tabs-three,
.tabs-four {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.tabs li,
.tabs .tab-list,
.tab-parents li,
.tab-parents .tab-list,
.tabs-two li,
.tabs-two .tab-list,
.tabs-three li,
.tabs-three .tab-list,
.tabs-four li,
.tabs-four .tab-list {
  font-size: 16px;
  font-weight: 400;
  color: var(--light-color);
  line-height: 28px;
  cursor: pointer;
  -webkit-transition: 300ms;
  transition: 300ms;
  position: relative;
}

.tabs li::before,
.tabs .tab-list::before,
.tab-parents li::before,
.tab-parents .tab-list::before,
.tabs-two li::before,
.tabs-two .tab-list::before,
.tabs-three li::before,
.tabs-three .tab-list::before,
.tabs-four li::before,
.tabs-four .tab-list::before {
  content: "";
  position: absolute;
  height: 2px;
  width: 0%;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  bottom: 0;
  background-color: var(--main-color-one);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.tabs li.active,
.tabs .tab-list.active,
.tab-parents li.active,
.tab-parents .tab-list.active,
.tabs-two li.active,
.tabs-two .tab-list.active,
.tabs-three li.active,
.tabs-three .tab-list.active,
.tabs-four li.active,
.tabs-four .tab-list.active {
  color: var(--main-color-one);
}

.tabs li.active::before,
.tabs .tab-list.active::before,
.tab-parents li.active::before,
.tab-parents .tab-list.active::before,
.tabs-two li.active::before,
.tabs-two .tab-list.active::before,
.tabs-three li.active::before,
.tabs-three .tab-list.active::before,
.tabs-four li.active::before,
.tabs-four .tab-list.active::before {
  width: 100%;
}

.tabs-two li::before,
.tabs-two .tab-list::before {
  display: none;
}

/* Job Tabs Css */
.myJob-tabs .tabs,
.myJob-tabs .tabs-two,
.myJob-tabs .tabs-three,
.myJob-tabs .tabs-four {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px 30px;
}

@media only screen and (max-width: 480px) {

  .myJob-tabs .tabs,
  .myJob-tabs .tabs-two,
  .myJob-tabs .tabs-three,
  .myJob-tabs .tabs-four {
    gap: 10px 20px;
  }
}

.myJob-tabs .tabs li,
.myJob-tabs .tabs-two li,
.myJob-tabs .tabs-three li,
.myJob-tabs .tabs-four li {
  font-size: 16px;
  font-weight: 500;
  color: var(--paragraph-color);
  position: relative;
  padding-bottom: 5px;
}

@media only screen and (max-width: 480px) {

  .myJob-tabs .tabs li,
  .myJob-tabs .tabs-two li,
  .myJob-tabs .tabs-three li,
  .myJob-tabs .tabs-four li {
    padding-bottom: 2px;
  }
}

.myJob-tabs .tabs li::before,
.myJob-tabs .tabs-two li::before,
.myJob-tabs .tabs-three li::before,
.myJob-tabs .tabs-four li::before {
  content: "";
  position: absolute;
  height: 2px;
  width: 0%;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  bottom: 0;
  background-color: var(--main-color-one);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myJob-tabs .tabs li.active,
.myJob-tabs .tabs-two li.active,
.myJob-tabs .tabs-three li.active,
.myJob-tabs .tabs-four li.active {
  color: var(--main-color-one);
}

.myJob-tabs .tabs li.active::before,
.myJob-tabs .tabs-two li.active::before,
.myJob-tabs .tabs-three li.active::before,
.myJob-tabs .tabs-four li.active::before {
  width: 100%;
}

.tab-content-item,
.tab-content-item-two,
.tab-content-item-three,
.tab-content-item-four {
  display: none;
}

.tab-content-item.active,
.tab-content-item-two.active,
.tab-content-item-three.active,
.tab-content-item-four.active {
  display: block;
  -webkit-animation: 1s fade-effects;
  animation: 1s fade-effects;
}

@-webkit-keyframes fade-effects {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}

@keyframes fade-effects {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}

/* Custom Radio Css */
.custom-radio-single {
  -webkit-transition: 0.3s;
  transition: 0.3s;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

.custom-radio-single.active label {
  font-weight: 600;
  color: var(--heading-color);
}

.custom-radio-inline {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 12px 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.custom-radio-item:not(:first-child) {
  margin-top: 15px;
}

.custom-radio label {
  font-size: 16px;
  color: var(--paragraph-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
  padding: 0px 0;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

.custom-radio input[type=radio] {
  flex-shrink: 0;
  border: 2px solid #DDD;
  background: #fff;
  clear: none;
  cursor: pointer;
  display: inline-block;
  margin: 0;
  outline: 0;
  padding: 0 !important;
  text-align: center;
  vertical-align: middle;
  height: 24px;
  width: 24px;
  -webkit-appearance: none;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  border-radius: 50%;
  position: relative;
  margin-right: 3px;
}

.custom-radio input[type=radio]:checked {
  border-color: var(--main-color-one);
}

.custom-radio input[type=radio]:checked::before {
  width: calc(100% - 9px);
  height: calc(100% - 9px);
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: "";
  border-radius: 50%;
  background-color: var(--main-color-one);
  margin: 0px 0px 0;
  padding: 0px;
  line-height: 1;
}

/* Custom Switch Css */
.custom_switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}
.profile-wrapper-switch-custom {
  line-height: 1;
}
@media only screen and (max-width: 375px) {
  .custom_switch {
    width: 40px;
    height: 24px;
  }
}

.custom_switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.custom_switch input:checked+.slider {
  background-color: var(--main-color-one);
}

.custom_switch input:checked+.slider:before {
  -webkit-transform: translateX(20px);
  transform: translateX(20px);
}

@media only screen and (max-width: 375px) {
  .custom_switch input:checked+.slider:before {
    -webkit-transform: translateX(16px);
    transform: translateX(16px);
  }
}

.custom_switch input:focus+.slider {
  -webkit-box-shadow: 0 0 1px var(--main-color-one);
  box-shadow: 0 0 1px var(--main-color-one);
}

.custom_switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.custom_switch .slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

@media only screen and (max-width: 375px) {
  .custom_switch .slider:before {
    height: 16px;
    width: 16px;
  }
}

.custom_switch .slider.round {
  border-radius: 30px;
}

.custom_switch .slider.round::before {
  border-radius: 50%;
}

/* Hover Tooltip Active Css */
.hover-question {
  position: relative;
  z-index: 5;
}

.hover-question:hover .hover-active-content {
  visibility: visible;
  opacity: 1;
  bottom: 110%;
}

.hover-active-content {
  display: inline-block;
  padding: 5px 10px;
  background-color: #f3f3f3;
  color: var(--heading-color);
  position: absolute;
  bottom: 10%;
  left: 0;
  z-index: 9;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 3px;
  min-width: 135px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

/* Search Popup */
.search-open {
  font-size: 24px;
  cursor: pointer;
  position: relative;
  z-index: 9;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.search-open:hover {
  color: var(--main-color-one);
}

.search-bar {
  position: fixed;
  top: 100px;
  right: 0;
  left: 0;
  height: auto;
  z-index: 991;
  max-width: 600px;
  margin: 0 auto;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  /* IE 9 */
  -webkit-transform: scale(0.5);
  /* Chrome, Safari, Opera */
  transform: scale(0.5);
}

.search-bar.active {
  visibility: visible;
  opacity: 1;
  /* IE 9 */
  -webkit-transform: scale(1);
  /* Chrome, Safari, Opera */
  transform: scale(1);
}

.search-bar .menu-search-form {
  position: relative;
  z-index: 9;
  width: 100%;
  margin: 0 auto;
  border-radius: 0px;
}

.search-bar .menu-search-form .search-close {
  position: absolute;
  right: 0px;
  top: -40px;
  font-size: 24px;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  background: var(--border-color);
  color: #f00;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
}

.search-bar .menu-search-form .search-close:hover {
  color: #fff;
  background: #f00;
}

.search-bar .menu-search-form .item-search {
  width: 100%;
  height: 70px;
  background: none;
  border: 1px solid rgba(0, 0, 0, 0.2);
  outline: none;
  text-transform: capitalize;
  padding: 10px 20px;
  padding-right: 100px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 0px;
  color: var(--paragraph-color);
}

.search-bar .menu-search-form .item-search:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.2);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.search-bar .menu-search-form .item-search::-webkit-input-placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form .item-search::-moz-placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form .item-search:-ms-input-placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form .item-search::-ms-input-placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form .item-search::placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form button {
  position: absolute;
  right: 0;
  height: 100%;
  padding: 0 30px;
  background: var(--main-color-one);
  border: 0;
  outline: none;
  font-size: 24px;
  color: #fff;
  cursor: pointer;
}

.search-overlay {
  height: 100%;
  width: 100%;
  left: 100%;
  top: 0;
  left: 0;
  position: fixed;
  z-index: 99;
  cursor: -webkit-zoom-out;
  cursor: zoom-out;
  -webkit-transition: all 0.6s;
  transition: all 0.6s;
  visibility: hidden;
  opacity: 0;
}

.search-overlay.active {
  background: rgba(0, 0, 0, 0.7);
  visibility: visible;
  opacity: 1;
}

/* Search Suggestions */
.search-suggestions {
  padding: 20px;
  display: none;
}

.search-suggestions.active {
  display: block;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  right: 0;
  width: 100%;
  background: #fff;
  padding: 30px 20px;
  -webkit-box-shadow: 0 0 10px rgba(221, 221, 221, 0.4);
  box-shadow: 0 0 10px rgba(221, 221, 221, 0.4);
  visibility: hidden;
  opacity: 0;
  height: calc(100vh - 180px);
  overflow-y: auto;
  scrollbar-color: var(--body-color) var(--border-color);
  scrollbar-width: thin;
}

.search-suggestions::-webkit-scrollbar {
  width: 5px;
  background-color: var(--border-color);
}

.search-suggestions::-webkit-scrollbar-thumb {
  background-color: var(--body-color);
  border-radius: 10px;
}

@media (min-width: 1600px) and (max-width: 1849.98px) {
  .search-suggestions {
    height: 475px;
  }
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .search-suggestions {
    height: 475px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .search-suggestions {
    height: 475px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .search-suggestions {
    height: 475px;
    padding: 30px 10px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .search-suggestions {
    height: 485px;
  }
}

@media only screen and (max-width: 480px) {
  .search-suggestions {
    padding: 30px 10px;
  }
}

.search-suggestions.active {
  visibility: visible;
  opacity: 1;
}

.search-suggestions-title {
  font-size: 18px;
  width: 100%;
  background-color: rgba(221, 221, 221, 0.2);
  padding: 5px 10px;
}

.product-suggestion-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.product-suggestion-list-item:not(:last-child) {
  margin-bottom: 20px;
}

.product-suggestion-list-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 20px;
}

@media only screen and (max-width: 480px) {
  .product-suggestion-list-link {
    display: block;
  }
}

.product-suggestion-list-link .product-image {
  border: 1px solid rgba(221, 221, 221, 0.5);
  padding: 5px;
  height: 100px;
  width: 100px;
}

.product-suggestion-list-link .product-image img {
  height: 100%;
  width: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-suggestion-list-link .product-image {
    width: 80px;
    height: 100px;
    padding: 10px 0px;
  }
}

.product-info {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media only screen and (max-width: 480px) {
  .product-info .product-info-top {
    margin-top: 10px;
  }
}

.product-info .product-info-top .product-name {
  font-size: 18px;
  line-height: 28px;
  -webkit-transition: 300ms;
  transition: 300ms;
}

.product-info .product-info-top .product-name:hover {
  color: var(--main-color-one);
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .product-info-top .product-name {
    font-size: 17px;
  }
}

.product-info .product-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 20px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .product-price {
    gap: 7px;
  }
}

.product-info .product-price .price-update-through {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.product-info .product-price .price-update-through .flash-price {
  font-size: 18px;
  color: var(--heading-color);
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .product-price .price-update-through .flash-price {
    font-size: 16px;
  }
}

.product-info .product-price .price-update-through .flash-old-prices {
  font-size: 14px;
  text-decoration: line-through;
}

.product-info .product-price .main-price {
  font-size: 16px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .product-price .main-price {
    font-size: 17px;
  }
}

.product-info .stock-out {
  font-size: 16px;
  background: #ff0000;
  color: #fff;
  padding: 5px 10px;
  display: inline-block;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .stock-out {
    font-size: 15px;
    padding: 5px 5px;
  }
}

/*----------------------------------------------
    # Nav bar 
----------------------------------------------*/
.mobile-logo {
  display: none;
}

@media only screen and (max-width: 991.98px) {
  .mobile-logo {
    display: block;
  }

  .desktop-logo {
    display: none !important;
  }
}

.navbar-padding {
  padding: 20px 0;
}

.navbar-area {
  position: relative;
  z-index: 95;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .navbar-area {
    padding: 0;
  }
}

.navbar-area.nav-absolute {
  position: absolute;
  left: 0;
  width: 100%;
  top: 20px;
  z-index: 99;
}

.navbar-area.navbar-border {
  border-bottom: 1px solid rgba(221, 221, 221, 0.5);
}

.navbar-area.white-nav .nav-container .navbar-collapse .navbar-nav li {
  color: #fff;
}

.navbar-area.white-nav .nav-container .nav-right-content ul li {
  color: #fff;
}

.navbar-area.white-nav .nav-container .nav-right-content ul li .track-icon-list .single-icon .icon {
  color: #fff;
}

.navbar-area.white-nav .nav-container .nav-right-content ul li .track-icon-list .single-icon.hover-color-two:hover .icon:hover {
  color: var(--main-color-two);
}

.navbar-area.white-nav .nav-container .nav-right-content ul li .login-account .accounts {
  color: #fff;
}

.navbar-area.white-nav .nav-container .nav-right-content ul li .login-account .accounts.hover-color-two:hover {
  color: var(--main-color-two);
}

.navbar-area.navbar-two {
  padding: 0;
}

.navbar-area.navbar-two .nav-container {
  background-color: rgba(0, 0, 0, 0.15);
  padding: 20px;
  border-radius: 0 0 20px 20px;
}

.navbar-area.navbar-two .nav-container .navbar-collapse .navbar-nav li a:hover {
  color: var(--secondary-color);
  background: none;
}

.navbar-area.navbar-two .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu {
  border-bottom: 5px solid var(--secondary-color);
}

.navbar-area.navbar-two .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li a:hover {
  background-color: var(--secondary-color);
  color: #fff;
}

.navbar-area .nav-container {
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
  padding: 0px 15px;
  padding: 10px;
  border-radius: 7px;
}

.navbar-area .nav-container.bg-white {
  background-color: #fff;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .navbar-area .nav-container.bg-white {
    padding: 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .navbar-area .nav-container.bg-white {
    padding: 20px;
  }
}

@media only screen and (max-width: 575.98px) {
  .navbar-area .nav-container.bg-white {
    margin: 0 12px;
  }
  .navbar-area .nav-container {
    padding: 20px 0px 30px !important;
  }
}

.navbar-area .nav-container.bg-black {
  background-color: var(--heading-color);
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .navbar-area .nav-container {
    padding: 20px 0;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .navbar-area .nav-container {
    padding: 15px 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .navbar-area .nav-container .logo-wrapper {
    height: 18px;
  }
}

.navbar-area .nav-container .logo-wrapper .logo img {
  max-width: 200px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .navbar-area .nav-container .logo-wrapper .logo img {
    max-width: 160px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .navbar-area .nav-container .logo-wrapper .logo img {
    max-width: 170px;
  }
}

.navbar-area .nav-container .navbar-collapse .navbar-nav {
  display: block;
  width: 100%;
  text-align: center;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav.nav-margin-top {
  margin-top: 15px;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav.desktop-center {
  text-align: center;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li {
  display: inline-block;
  font-size: 16px;
  font-family: var(--body-font);
  text-transform: capitalize;
  color: var(--heading-color);
  font-weight: 400;
  line-height: 32px;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li+li {
  margin-left: 0px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li {
    font-size: 15px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li {
    font-size: 14px;
  }
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li a {
  padding: 10px 30px 10px 0;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  color: var(--paragraph-color);
}
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children>a {
  position: relative;
  z-index: 0;
}
@media (min-width: 1600px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children>a {
    padding: 10px 30px 10px 0;
  }
}
@media (min-width: 1400px) and (max-width: 1599.98px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li a {
    padding: 7px 10px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li a {
    padding: 5px 10px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li a {
    padding: 5px 10px;
  }
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li a:hover {
  color: var(--main-color-one);
  background: none;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.current-menu-item {
  background: none;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children {
  padding-right: 0;
  position: relative;
}



@media (min-width: 992px) and (max-width: 1199.98px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children>a {
    padding: 10px 10px;
  }
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children>a::after {
  content: "\f107";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  top: 1px;
  left: 4px;
  position: relative;
  font-size: 15px;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:hover>.sub-menu {
  visibility: visible;
  opacity: 1;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu {
  position: absolute;
  text-align: left;
  min-width: 220px;
  margin: 0;
  padding: 0;
  list-style: none;
  left: 0;
  top: 100%;
  -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  z-index: 992;
  border-bottom: 5px solid var(--main-color-one);
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu.column-count-two {
  -webkit-column-count: 2;
  -moz-column-count: 2;
  column-count: 2;
  -webkit-column-gap: 20px;
  -moz-column-gap: 20px;
  column-gap: 50px;
  -webkit-column-rule-style: solid;
  -moz-column-rule-style: solid;
  column-rule-style: solid;
  -webkit-column-rule-color: rgba(221, 221, 221, 0.3);
  -moz-column-rule-color: rgba(221, 221, 221, 0.3);
  column-rule-color: rgba(221, 221, 221, 0.3);
  -webkit-column-rule-width: 2px;
  -moz-column-rule-width: 2px;
  column-rule-width: 2px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu.column-count-two {
    -webkit-column-count: unset;
    -moz-column-count: unset;
    column-count: unset;
  }
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu.column-count-two li {
  margin-bottom: -1px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu.column-count-two li {
    margin-bottom: 0;
  }
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li {
  display: block;
  margin-left: 0;
  line-height: 24px;
  font-size: 14px;
  margin-bottom: -1px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li {
    margin-bottom: 0;
  }
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li:last-child {
  margin-bottom: -1px;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li+li {
  border-top: 1px solid #e2e2e2;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li a {
  display: block;
  padding: 10px 30px;
  background-color: #fff;
  white-space: nowrap;
  color: var(--heading-color);
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li a:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children {
  position: relative;
  z-index: 0;
  padding-right: 0px;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children>.sub-menu {
  left: 100%;
  top: 0px;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children>.sub-menu .sub-menu .sub-menu {
  left: auto;
  right: 100%;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children:hover>.sub-menu {
  visibility: visible;
  opacity: 1;
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children:hover>.sub-menu li:hover:before {
  color: #fff;
}

.navbar-area.white-nav .navbar-right-btn a {
  color: #fff;
}

.navbar-area.navbar-two .navbar-right-btn a:hover {
  color: var(--secondary-color);
}

.navbar-area.navbar-two .btn-wrapper .cmn-btn.btn-bg-1 {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.navbar-area.navbar-two .btn-wrapper .cmn-btn.btn-bg-1:hover {
  background-color: unset;
  color: var(--secondary-color);
}
.cmn-btn.hourly-rate-btn {
  line-height: 24px;
}
/* Navbar right Content */
.click-nav-right-icon {
  display: none;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .click-nav-right-icon {
    position: absolute;
    right: 0px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    font-size: 24px;
    color: rgba(0, 0, 0, 0.6);
    display: block;
  }

  .click-nav-right-icon:hover {
    color: var(--heading-color);
  }

  .click-nav-right-icon.white-color {
    color: #fff;
  }

  .click-nav-right-icon.white-color:hover {
    color: #fff;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .show-nav-content {
    visibility: hidden;
    opacity: 0;
    -webkit-transition: 300ms;
    transition: 300ms;
    margin-top: -50px;
    margin-left: auto;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .show-nav-content.show {
    visibility: visible;
    opacity: 1;
    margin-top: 30px;
  }
}

.single-right-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.navbar-right-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px 10px;
}

.navbar-right-btn a {
  display: inline-block;
  font-size: 16px;
  font-weight: 500;
  color: var(--heading-color);
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.navbar-right-btn a:hover {
  color: var(--main-color-one);
  text-decoration: underline;
}

.navbar-right-chat {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 38px;
  width: 38px;
  font-size: 16px;
  background-color: #EAECF0;
  border-radius: 50%;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.navbar-right-chat:hover {
  background-color: var(--main-color-one);
  color: #fff;
}
.bookmark-header {
  display: flex;
  align-items: center;
  gap: 12px;
}
.bookmark-header-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 16px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 50%;
  flex-shrink: 0;
}
.bookmark-header-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 24px;
}
.bookmark-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap:5px;
}

.bookmark-item:not(:last-child) {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.bookmark-item-para {
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  color: var(--paragraph-color);
  display: flex;
  align-content: flex-start;
  gap: 10px;
}
.bookmark-item-para i {

}
.bookmark-item-close {
  color: var(--danger-color);
  font-size: 18px;
  cursor: pointer;
  transition: all .3s
}

.bookmark-item-close:hover {
  transform: scale(1.1);
}

.bookmark_area,
.navbar-right-notification {
  position: relative;
}

.bookmark-wrap {
  padding: 20px;
}

.bookmark-wrap.active,
.navbar-right-notification .navbar-right-notification-wrapper.active {
  visibility: visible;
  opacity: 1;
  -webkit-transform: matrix(1, 0, 0, 1, 0, 0);
  transform: matrix(1, 0, 0, 1, 0, 0);
  top: 100%;
}

.nav-right-bookmark-icon,
.navbar-right-notification-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 18px;
  background-color: #EAECF0;
  border-radius: 50%;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.nav-right-bookmark-icon:hover,
.navbar-right-notification-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.bookmark-wrap,
.navbar-right-notification-wrapper {
  position: absolute;
  top: 120%;
  right: 0;
  z-index: 9998;
  width: 430px;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 0 10px #f3f3f3;
  box-shadow: 0 0 10px #f3f3f3;
  border-radius: 5px;
  overflow: hidden;
  border: 1px solid #fff;
  border-bottom: 4px solid var(--main-color-one);
  max-height: 600px;
  overflow-y: auto;
  scrollbar-color: var(--main-color-one) #ddd;
  scrollbar-width: thin;
}
@media screen and (max-width: 575px) {
  .bookmark-wrap.active {
    transform: translateX(25%);
  }
  .navbar-right-notification .navbar-right-notification-wrapper.active {
    transform: translateX(16%);
    max-width: 400px;
  }
}
@media screen and (max-width: 480px) {
  .bookmark-wrap.active {
    transform: translateX(32%);
    max-width: 350px;
  }
  .navbar-right-notification .navbar-right-notification-wrapper.active {
    transform: translateX(20%);
    max-width: 350px;
  }
}
@media screen and (max-width: 375px) {
  .bookmark-wrap.active {
    transform: translateX(40%);
    max-width: 300px;
  }
  .navbar-right-notification .navbar-right-notification-wrapper.active {
    transform: translateX(25%);
    max-width: 300px;
  }
}
.navbar-right-notification-wrapper::-webkit-scrollbar {
  width: 5px;
  border-radius: 10px;
  background-color: #ddd;
}

.navbar-right-notification-wrapper::-webkit-scrollbar-thumb {
  background-color: var(--main-color-one);
  border-radius: 10px;
}

.navbar-right-notification-wrapper-list {
  display: block;
  font-size: 15px;
  font-weight: 400;
  color: var(--heading-color);
  border-bottom: 1px solid #f3f3f3;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.navbar-right-notification-wrapper-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px;
  background-color: #f7f7f7;
  padding: 20px;
}

.navbar-right-notification-wrapper-list-item.active {
  background-color: unset;
}

.navbar-right-notification-wrapper-list-item:not(:first-child) {
  padding-top: 20px;
}

.navbar-right-notification-wrapper-list-item:not(:last-child) {
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.navbar-right-notification-wrapper-list-item-thumb img {
  border-radius: 50%;
}

.navbar-right-notification-wrapper-list-item-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 16px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 50%;
}

.navbar-right-notification-wrapper-list-item-icon.decline {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff0000;
}

.navbar-right-notification-wrapper-list-item-content-title {
  font-size: 15px;
  font-weight: 400;
  color: var(--heading-color);
  line-height: 24px;
}

.navbar-right-notification-wrapper-list-item-content-time {
  margin-top: 7px;
  font-size: 15px;
}

.navbar-author {
  position: relative;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.navbar-author .navbar-author-link {
  display: block;
  border: 0;
}

.navbar-author:hover .navbar-author-wrapper {
  visibility: visible;
  opacity: 1;
  top: 100%;
}

.navbar-author-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  border: 1px solid #ddd;
  border-radius: 30px;
}

.navbar-author-thumb {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.navbar-author-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.navbar-author-name {
  position: relative;
  font-size: 16px;
  font-weight: 500;
  color: var(--heading-color);
  padding-right: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  display: none;
}

.navbar-author-name::after {
  content: "\f078";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 15px;
}

.navbar-author-wrapper {
  position: absolute;
  top: 130%;
  right: 0;
  z-index: 9998;
  max-width: 200px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 0 10px #f3f3f3;
  box-shadow: 0 10px 20px #e3e3e3;
  border-radius: 5px;
  overflow: hidden;
}

.navbar-author-wrapper-list {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-color: var(--main-color-one) #ddd;
  scrollbar-width: thin;
}
.navbar-author-wrapper-list-item svg {
  max-width: 18px;
  flex-shrink: 0;
}
.navbar-author-wrapper-list-item svg path {
  fill: var(--paragraph-color);
  transition: all .3s;
}
.navbar-author-wrapper-list-item:hover svg path {
  fill: var(--white);
}
.navbar-author-wrapper-list::-webkit-scrollbar {
  width: 5px;
  background-color: #ddd;
}

.navbar-author-wrapper-list::-webkit-scrollbar-thumb {
  background-color: var(--main-color-one);
}

.navbar-author-wrapper-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 15px;
  line-height: 24px;
  font-weight: 400;
  padding: 10px 20px;
  color: var(--heading-color);
  border-bottom: 1px solid #f3f3f3;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  white-space: nowrap;
}

.navbar-author-wrapper-list-item:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 991.98px) {
  .navbar-area.white-nav .nav-container .navbar-collapse .navbar-nav {
    background-color: var(--heading-color);
  }

  .navbar-area.white-nav .nav-container .navbar-collapse .navbar-nav li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .navbar-area.white-nav .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu {
    background-color: #202020 !important;
  }

  .navbar-area.white-nav .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li {
    border-color: rgba(255, 255, 255, 0.05) !important;
  }

  .navbar-area.white-nav .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li a {
    background-color: transparent;
    color: #fff;
  }

  .navbar-area.white-nav .navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.7);
  }

  .navbar-area.white-nav .navbar-toggler.active .navbar-toggler-icon::before,
  .navbar-area.white-nav .navbar-toggler.active .navbar-toggler-icon::after {
    background: rgba(255, 255, 255, 0.7);
  }

  .navbar-area.white-nav .navbar-toggler .navbar-toggler-icon {
    background: rgba(255, 255, 255, 0.7);
  }

  .navbar-area.white-nav .navbar-toggler .navbar-toggler-icon::before,
  .navbar-area.white-nav .navbar-toggler .navbar-toggler-icon::after {
    background: rgba(255, 255, 255, 0.7);
  }

  .navbar-area.white-nav .click-nav-right-icon {
    color: rgba(255, 255, 255, 0.7);
  }

  .navbar-area .navbar-toggler {
    position: absolute;
    right: 25px;
    top: 50%;
    -webkit-box-shadow: none;
    box-shadow: none;
    /* IE 9 */
    -webkit-transform: translateY(-50%);
    /* Chrome, Safari, Opera */
    transform: translateY(-50%);
    border: 1px solid rgba(0, 0, 0, 0.7);
    outline: none;
  }

  .navbar-area .navbar-toggler.active .navbar-toggler-icon {
    background: none;
  }

  .navbar-area .navbar-toggler.active .navbar-toggler-icon::before,
  .navbar-area .navbar-toggler.active .navbar-toggler-icon::after {
    background: rgba(0, 0, 0, 0.7);
    top: 0;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }

  .navbar-area .navbar-toggler.active .navbar-toggler-icon::after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }

  .navbar-area .navbar-toggler .navbar-toggler-icon {
    background: rgba(0, 0, 0, 0.7);
    display: inline-block;
    width: 25px;
    height: 2px;
    margin: 10px -4px 10px;
    position: relative;
  }

  .navbar-area .navbar-toggler .navbar-toggler-icon::before,
  .navbar-area .navbar-toggler .navbar-toggler-icon::after {
    position: absolute;
    content: "";
    height: 2px;
    width: 25px;
    background: rgba(0, 0, 0, 0.7);
    top: -7px;
    left: 0;
    -webkit-transition: all 0.4s;
    transition: all 0.4s;
  }

  .navbar-area .navbar-toggler .navbar-toggler-icon::after {
    top: auto;
    bottom: -7px;
  }

  .navbar-area .nav-container {
    position: relative;
    z-index: 0;
    min-height: 50px;
    padding: 20px 20px 30px !important;
  }

  .navbar-area .nav-container .responsive-mobile-menu {
    display: block;
    position: relative;
    width: 100%;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav {
    display: block;
    margin-top: 30px;
    background: var(--border-color);
    -webkit-box-shadow: 0 0 30px rgba(221, 221, 221, 0.3);
    box-shadow: 0 0 30px rgba(221, 221, 221, 0.3);
    border-radius: 10px;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li {
    display: block;
    text-align: left;
    line-height: 30px;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li a {
    padding: 15px 20px;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li:last-child {
    border-bottom: none;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li+li {
    margin-left: 0;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:before {
    top: 25px;
    right: 20px;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:hover {
    padding-bottom: 0;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:hover>.sub-menu {
    visibility: visible;
    height: auto;
    opacity: 1;
    background-color: transparent;
    border-bottom: none;
    padding-top: 10px;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children>a {
    padding: 15px 20px;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu {
    position: initial;
    display: block;
    width: 100%;
    border-top: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin-left: 0;
    padding-bottom: 0;
    visibility: hidden;
    opacity: 0;
    height: 0;
    overflow: hidden;
    max-height: 250px;
    overflow-y: scroll;
    -webkit-transition: all 500ms linear;
    transition: all 500ms linear;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .sub-menu .menu-item-has-children:before {
    content: "\f107";
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li {
    padding: 0;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li:not(:first-child) {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li.menu-item-has-children:hover:before {
    color: #fff;
  }

  .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li+li {
    border-top: none;
  }
}

@media only screen and (max-width: 575px) {
  .navbar-area .nav-container .logo-wrapper .logo img {
    max-width: 160px;
  }
  .navbar-area .nav-container {
    padding: 12px 0px 30px !important;
  }
}

/*--------------------------------------------------------------
	04. Footer Style
--------------------------------------------------------------*/
.footer-area {
  position: relative;
  z-index: 1;
}

.footer-area-top {
  padding: 45px 50px 70px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .footer-area-top {
    padding: 45px 30px 70px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .footer-area-top {
    padding: 45px 20px 70px;
  }
}

@media only screen and (max-width: 375px) {
  .footer-area-top {
    padding: 45px 10px 70px;
  }
}

.footer-area-wrapper {
  border-radius: 20px 20px 0 0;
}

@media only screen and (max-width: 575.98px) {
  .footer-contents-logo {
    max-width: 200px;
  }
}

.footer-top {
  padding: 30px 0;
}

.footer-border-top {
  border-top: 1px solid rgba(221, 221, 221, 0.2);
}

.footer-border-top.white-color {
  border-color: rgba(221, 221, 221, 0.7);
}

.footer-border-bottom {
  border-bottom: 1px solid rgba(221, 221, 221, 0.2);
}

.footer-border-bottom.white-color {
  border-color: rgba(221, 221, 221, 0.7);
}

.footer-fluid {
  position: relative;
  -webkit-transition: all 1s;
  transition: all 1s;
  overflow: hidden;
}

.footer-fluid::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 0%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  top: 0;
  background-color: var(--footer-bg-1);
  z-index: -1;
  -webkit-transition: 1s;
  transition: 1s;
  visibility: hidden;
  opacity: 0;
}

.footer-fluid.footer-bg-add {
  -webkit-transition: all 1s;
  transition: all 1s;
}

.footer-fluid.footer-bg-add::before {
  visibility: visible;
  opacity: 1;
  width: 100%;
}

.footer-fluid .footer-wrapper {
  border-radius: 0;
}

.white-footer .footer-widget-title {
  color: #fff;
}

.white-footer .footer-widget-para {
  color: rgba(255, 255, 255, 0.7);
}
.white-footer .footer-widget-para > P {
  color: rgba(255, 255, 255, 0.7);
}

.white-footer .footer-widget-form-control {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.white-footer .footer-widget-link-list-item a {
  color: rgba(255, 255, 255, 0.7);
}

.white-footer .footer-widget-contact-item {
  color: rgba(255, 255, 255, 0.7);
}

.white-footer .footer-widget-contact-item-icon {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.footer-widget-title {
  font-size: 24px;
  line-height: 28px;
  margin: -4px 0 0;
}

@media only screen and (max-width: 767.98px) {
  .footer-widget-title {
    font-size: 22px;
  }
}

.footer-widget-para {
  color: var(--light-color);
  line-height: 28px;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.footer-widget-social-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 12px 20px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .footer-widget-social-list {
    gap: 12px 15px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .footer-widget-social-list {
    gap: 10px;
  }
}

.footer-widget-social-list-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 35px;
  width: 35px;
  font-size: 16px;
  border-radius: 5px;
  background: #fff;
  color: var(--heading-color);
  -webkit-transition: 300ms;
  transition: 300ms;
}

@media only screen and (max-width: 575.98px) {
  .footer-widget-social-list-link {
    font-size: 16px;
    height: 35px;
    width: 35px;
  }
}

@media only screen and (max-width: 375px) {
  .footer-widget-social-list-link {
    height: 30px;
    width: 30px;
  }
}

.footer-widget-social-list-link:hover {
  color: #fff;
  background: var(--main-color-one);
}

.footer-widget-form-single {
  position: relative;
  z-index: 2;
}

.footer-widget-form-control {
  height: 55px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  padding: 0 20px;
  color: var(--paragraph-color);
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.001);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.001);
  -webkit-transition: 0.3s;
  transition: 0.3s;
  padding-right: 110px;
}

.footer-widget-form-control:focus {
  border: 1px solid rgba(var(--main-color-one-rgb), 0.4);
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .footer-widget-form-control {
    padding-right: 20px;
  }
}

.footer-widget-form button {
  position: absolute;
  right: 0px;
  top: 0px;
  bottom: 0;
  background-color: var(--main-color-one);
  color: #fff;
  padding: 0px 15px;
  border: 0;
  outline: none;
  border-radius: 0 10px 10px 0;
  height: 100%;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

.footer-widget-form button:hover {
  background-color: var(--secondary-color);
  color: #fff;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .footer-widget-form button {
    position: relative;
    width: 100%;
    margin-top: 20px;
    border-radius: 10px;
    padding: 15px 15px;
    text-align: center;
    font-size: 16px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

.footer-widget-link-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.footer-widget-link-list-item {
  position: relative;
  z-index: 2;
  -webkit-transition: 300ms;
  transition: 300ms;
}

.footer-widget-link-list-item:not(:last-child) {
  margin-bottom: 15px;
}

.footer-widget-link-list-item:hover a {
  color: var(--main-color-one);
}

.footer-widget-link-list-item a {
  font-size: 16px;
  font-weight: 400;
  color: var(--light-color);
  position: relative;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  position: relative;
  z-index: 1;
  padding-left: 20px;
}

.footer-widget-link-list-item a:hover {
  color: var(--main-color-one);
}

.footer-widget-link-list-item a::before {
  content: "\f105";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  left: 0;
  top: -1px;
}

.footer-widget-contact {
  padding: 0;
}

.footer-widget-contact-item {
  font-size: 16px;
  line-height: 26px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px;
  color: var(--light-color);
  -webkit-transition: 300ms;
  transition: 300ms;
}

.footer-widget-contact-item:not(:last-child) {
  margin-bottom: 15px;
}

.footer-widget-contact-item:hover {
  color: var(--main-color-one);
}

.footer-widget-contact-item:hover .footer-widget-contact-item-icon {
  background-color: var(--main-color-one);
  color: #fff;
}

.footer-widget-contact-item-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 20px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .footer-widget-contact-item-flex {
    gap: 12px;
  }
}

.footer-widget-contact-item-icon {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--paragraph-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 16px;
  border-radius: 50%;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.footer-widget-contact-item-details {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.copyright-area {
  padding: 30px 0;
  text-align: center;
}

.copyright-area.copyright-border {
  border-top: 1px solid rgba(221, 221, 221, 0.2);
}

.copyright-area.copyright-border.white-color {
  border-color: rgba(221, 221, 221, 0.7);
}

.copyright-contents-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
}

@media only screen and (max-width: 767.98px) {
  .copyright-contents-flex {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

.copyright-contents-items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px 50px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.copyright-contents-main {
  font-size: 16px;
  color: var(--light-color);
  text-align: center;
}

@media only screen and (max-width: 767.98px) {
  .copyright-contents-main {
    text-align: center;
    margin-top: 10px;
  }
}

.copyright-contents-main a {
  color: var(--main-color-one);
  font-weight: 500;
}

.footer-area-two .footer-widget-contents-para {
  color: rgba(255, 255, 255, 0.8);
}

.footer-area-two .footer-widget-form-control {
  background-color: #fff;
  border: 1px solid #fff;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.001);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.001);
  padding-right: 130px;
}

.footer-area-two .footer-widget-form-control:focus {
  border: 1px solid rgba(var(--secondary-color-rgb), 0.8);
}

.footer-area-two .footer-widget-form button {
  background-color: var(--secondary-color);
}

.footer-area-two .footer-widget-form button:hover {
  background-color: rgba(var(--secondary-color-rgb), 0.7);
  color: #fff;
}

.footer-area-two .footer-widget-para {
  color: rgba(255, 255, 255, 0.8);
}

.footer-area-two .footer-widget-nav-list-link {
  color: rgba(255, 255, 255, 0.8);
}

.footer-area-two .footer-widget-nav-list-link:hover {
  color: var(--secondary-color);
}

.footer-area-two .footer-widget-social-list-link:hover {
  background-color: var(--secondary-color);
}

.footer-area-two .copyright-contents-single:hover .copyright-contents-single-icon {
  color: #fff;
  background-color: var(--secondary-color);
}

.footer-area-two .copyright-contents-single-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  font-size: 30px;
  background-color: #fff;
  color: var(--secondary-color);
  border-radius: 50%;
}

.footer-area-two .copyright-contents-single-details-title {
  color: rgba(255, 255, 255, 0.8);
}

.footer-area-two .copyright-contents-single-details-link:hover {
  color: var(--secondary-color);
}

.footer-area-two .copyright-contents-main {
  color: rgba(255, 255, 255, 0.8);
}

.footer-area-two .copyright-contents-main a {
  color: var(--secondary-color);
}

/* Banner  */
.banner-area {
  position: relative;
  z-index: 1;
  padding: 0;
  overflow: hidden;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-area {
    padding-top: 80px;
  }
}

.banner-area-padding {
  padding: 100px 0 100px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-area-padding {
    padding: 70px 0 70px;
  }
}

@media only screen and (max-width: 480px) {
  .banner-area-padding {
    padding: 70px 0 70px;
  }
}

.banner-area .container {
  position: relative;
}

.banner-single {
  position: relative;
  z-index: 2;
}

.banner-single-content {
  position: relative;
  z-index: 5;
}

.banner-single-content.text-white .banner-single-content-title {
  color: #fff;
}

.banner-single-content.text-white .banner-single-content-para {
  color: #fff;
}

.banner-single-content.text-white .banner-single-content-reviews-para {
  color: #fff;
}

.banner-single-content.text-white .banner-single-content-reviews-para span {
  color: #fff;
}

.banner-single-content-title {
  font-size: 64px;
  line-height: 72px;
  margin: -12px 0 0;
  position: relative;
  font-family: var(--Otomanopee-font);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.banner-single-content-title::before {
  content: "";
  position: absolute;
  height: 80px;
  width: 80px;
  top: -40px;
  left: -40px;
  background-color: var(--main-color-one);
  border-radius: 50%;
  z-index: -1;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

@media only screen and (max-width: 575.98px) {
  .banner-single-content-title::before {
    display: none;
  }
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .banner-single-content-title {
    font-size: 55px;
    line-height: 70px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .banner-single-content-title {
    font-size: 48px;
    line-height: 65px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .banner-single-content-title {
    font-size: 42px;
    line-height: 55px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-single-content-title {
    font-size: 32px;
    line-height: 1.2;
  }
}

@media only screen and (max-width: 575.98px) {
  .banner-single-content-title {
    font-size: 28px;
  }
}

@media only screen and (max-width: 375px) {
  .banner-single-content-title {
    font-size: 26px;
  }
}

.banner-single-content-title:hover::before {
  background-color: var(--secondary-color);
  border-radius: 10px;
}

.banner-single-content-para {
  font-size: 18px;
  color: var(--paragraph-color);
  font-weight: 500;
  line-height: 28px;
  margin-top: 30px;
}

@media only screen and (max-width: 480px) {
  .banner-single-content-para {
    font-size: 16px;
    line-height: 26px;
  }
}

@media only screen and (max-width: 375px) {
  .banner-single-content-para {
    font-size: 15px;
  }
}

.banner-single-content-logo-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 20px 10px;
}

.banner-right-content {
  position: relative;
}

.banner-right-content-thumb {
  position: relative;
  padding: 100px 50px 0;
  border-radius: 30px;
  overflow: hidden;
  text-align: center;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-right-content-thumb {
    border-radius: 10px;
  }
}

@media only screen and (max-width: 375px) {
  .banner-right-content-thumb {
    padding: 70px 20px 0;
  }
}

.banner-right-content-thumb::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0px;
  bottom: 0;
  background-color: var(--secondary-color);
  z-index: -1;
}

@media only screen and (max-width: 575.98px) {
  .banner-right-content-shape {
    display: none;
  }
}

.banner-right-content-shape img {
  position: absolute;
}

.banner-right-content-shape img:nth-child(1) {
  top: -30px;
  left: -40px;
}

.banner-right-content-shape img:nth-child(2) {
  bottom: 50px;
  right: -40px;
}

.banner-right-content-top {
  position: absolute;
  right: -60px;
  top: 100px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .banner-right-content-top {
    right: -40px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .banner-right-content-top {
    right: 0px;
    top: 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-right-content-top {
    right: 0px;
    top: 0;
  }
}

@media only screen and (max-width: 375px) {
  .banner-right-content-top {
    display: none;
  }
}

.banner-right-content-bottom {
  position: absolute;
  left: -100px;
  bottom: 100px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-right-content-bottom {
    left: 10px;
    bottom: 10px;
  }
}

@media only screen and (max-width: 375px) {
  .banner-right-content-bottom {
    display: none;
  }
}

.banner-right-content-profile {
  background-color: #fff;
  padding: 20px;
  -webkit-box-shadow: -10px 4px 20px #ddd;
  box-shadow: -10px 4px 20px #ddd;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 300px;
  border-radius: 10px;
}

.banner-right-content-profile-thumb {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--border-color);
}

.banner-right-content-profile-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.banner-right-content-profile-content-name {
  font-size: 16px;
  font-weight: 700;
}

.banner-right-content-profile-content-para {
  font-size: 14px;
  margin-top: 5px;
  color: var(--paragraph-color);
}

.banner-right-content-rating {
  background-color: #000;
  padding: 20px;
  -webkit-box-shadow: 0 0 50px transparent;
  box-shadow: 0 0 50px transparent;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 300px;
  border-radius: 10px;
  text-align: center;
}

.banner-right-content-rating-icon {
  font-size: 20px;
  color: #ffff00;
}

.banner-right-content-rating-para {
  font-size: 16px;
  margin-top: 5px;
  color: #fff;
}

.banner-right-content-growth {
  background-color: #fff;
  padding: 20px;
  -webkit-box-shadow: 0 0 50px transparent;
  box-shadow: 0 0 50px transparent;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 300px;
}

.banner-right-content-growth-thumb {
  max-width: 50px;
}

.banner-right-content-growth-title {
  font-size: 24px;
  font-weight: 500;
  line-height: 28px;
  margin: 10px 0 0;
}

.banner-right-content-pushing {
  background-color: #fff;
  padding: 20px;
  -webkit-box-shadow: 0 0 50px transparent;
  box-shadow: 0 0 50px transparent;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 300px;
}

.banner-right-content-pushing-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 20px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.banner-right-content-pushing-title {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.banner-right-new {
  height: 670px;
  width: 180%;
}

/* Banner Two Css */
.banner-shapes-bg img {
  position: absolute;
  margin: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-shapes-bg img {
    display: none;
  }
}

.banner-area-two {
  padding-top: 170px;
}

.banner-area-two .banner-single-content-title::before {
  display: none;
}

.banner-area-two .banner-right-content-thumb {
  position: relative;
  padding: 30px 50px 0;
}

@media only screen and (max-width: 375px) {
  .banner-area-two .banner-right-content-thumb {
    padding: 30px 20px 0;
  }
}

.banner-area-two .banner-right-content-thumb::before {
  content: "";
  position: absolute;
  height: 400px;
  width: 400px;
  left: 0px;
  right: 0;
  margin-inline: auto;
  top: 0;
  background-color: var(--secondary-color);
  z-index: -1;
  border-radius: 50%;
}

@media only screen and (max-width: 480px) {
  .banner-area-two .banner-right-content-thumb::before {
    height: 350px;
    width: 350px;
  }
}

@media only screen and (max-width: 375px) {
  .banner-area-two .banner-right-content-thumb::before {
    height: 300px;
    width: 300px;
  }
}

.banner-area-two .banner-right-content-top {
  right: -150px;
  top: 150px;
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .banner-area-two .banner-right-content-top {
    right: 0;
    top: -30px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .banner-area-two .banner-right-content-top {
    right: 0;
    top: auto;
    bottom: 10px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .banner-area-two .banner-right-content-top {
    right: 0;
    top: auto;
    bottom: 10px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-area-two .banner-right-content-top {
    right: 0;
    top: auto;
    bottom: 10px;
  }
}

@media only screen and (max-width: 375px) {
  .banner-area-two .banner-right-content-top {
    display: none;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .banner-area-two .banner-right-content-bottom {
    left: auto;
    right: 0;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .banner-area-two .banner-right-content-bottom {
    left: auto;
    right: 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-area-two .banner-right-content-bottom {
    left: 0;
    bottom: 100px;
  }
}

@media only screen and (max-width: 375px) {
  .banner-area-two .banner-right-content-bottom {
    display: none;
  }
}

.banner-area-two .banner-right-content-shape img:nth-child(1) {
  top: 10px;
  left: 10px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .banner-area-two .banner-right-content-shape img:nth-child(1) {
    top: 0;
    left: 0;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .banner-area-two .banner-right-content-shape img:nth-child(1) {
    top: -20px;
    left: -20px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-area-two .banner-right-content-shape img:nth-child(1) {
    display: none;
  }
}

.banner-area-three {
  padding: 200px 0 100px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-area-three {
    padding: 170px 0 80px;
  }
}

@media only screen and (max-width: 480px) {
  .banner-area-three {
    padding: 140px 0 70px;
  }
}

.banner-area-three .banner-single-content-title::before {
  display: none;
}

@media only screen and (max-width: 375px) {
  .banner-wrapper {
    text-align: center;
  }
}

.banner-wrapper-left {
  display: inline-block;
  position: relative;
}

.banner-wrapper-thumb {
  position: relative;
  display: inline-block;
  border-bottom-left-radius: 50px;
  border-bottom-right-radius: 50px;
  overflow: hidden;
}

.banner-wrapper-thumb::before {
  background-color: var(--main-color-one);
  content: "";
  position: absolute;
  height: 70%;
  width: 100%;
  border-radius: 50px;
  left: 0;
  bottom: 0;
  z-index: -1;
}

.banner-wrapper-right {
  position: absolute;
  right: 0;
  bottom: 130px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .banner-wrapper-right {
    bottom: 70px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-wrapper-right {
    bottom: 0;
  }
}

@media only screen and (max-width: 575.98px) {
  .banner-wrapper-right {
    position: relative;
    margin-top: 20px;
    display: table;
    margin-left: auto;
  }
}

.banner-wrapper-right .banner-wrapper-thumb {
  border-radius: 0;
}

.banner-wrapper-right .banner-wrapper-thumb::before {
  background-color: var(--secondary-color);
  border-radius: 0;
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
}

.banner-wrapper-right .banner-wrapper-project {
  display: inline-block;
}

@media (min-width: 992px) and (max-width: 1799.98px) {
  .banner-wrapper-right .banner-wrapper-project {
    right: unset;
    left: -150px;
    bottom: 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-wrapper-right .banner-wrapper-project {
    right: unset;
    left: -150px;
    bottom: auto;
    top: 0;
  }
}

@media only screen and (max-width: 575.98px) {
  .banner-wrapper-right .banner-wrapper-project {
    left: -150px;
    right: unset;
  }
}

@media only screen and (max-width: 375px) {
  .banner-wrapper-right .banner-wrapper-project {
    position: relative;
    left: 0;
  }
}

.banner-wrapper-project {
  display: inline-block;
  background-color: #fff;
  padding: 10px 20px;
  max-width: 300px;
  border-radius: 30px;
  position: absolute;
  right: -150px;
  bottom: 20px;
  -webkit-box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
}

@media only screen and (max-width: 375px) {
  .banner-wrapper-project {
    position: relative;
    left: 0;
  }
}

.banner-wrapper-project-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.banner-wrapper-project-icon img {
  max-width: 50px;
}

.banner-wrapper-project-content {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.banner-wrapper-project-content-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--paragraph-color);
}

.banner-wrapper-line {
  position: relative;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .banner-wrapper-line {
    display: none;
  }
}

.banner-wrapper-line-shape {
  position: absolute;
  bottom: 0px;
  left: 140px;
  z-index: -2;
}

.banner-wrapper-line-fav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 1px solid var(--main-color-one);
  height: 50px;
  width: 50px;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  bottom: 50px;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
}

/* Work css */
.single-work {
  padding: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .single-work {
    padding: 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .single-work {
    padding: 20px;
  }
}

.single-work:hover {
  border-color: var(--main-color-one);
  -webkit-box-shadow: 0 0 70px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 70px rgba(var(--main-color-one-rgb), 0.1);
  /* IE 9 */
  -webkit-transform: translateY(-5px);
  /* Chrome, Safari, Opera */
  transform: translateY(-5px);
  background-color: rgba(var(--main-color-one-rgb), 0.05);
}

.single-work:hover .single-work-icon {
  background-color: #fff;
  color: var(--main-color-one);
}

.single-work-border {
  border: 2px solid var(--border-color);
}

.single-work-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 60px;
  width: 60px;
  border-radius: 50%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.single-work-icon img {
  max-width: 40px;
}

.single-work-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px;
}

.single-work-contents-title {
  font-size: 20px;
  line-height: 28px;
  margin: -4px 0 0;
  font-weight: 400;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  font-family: var(--Otomanopee-font);
}

.single-work-contents-title:hover {
  color: var(--main-color-one);
}

.single-work-contents-para {
  font-size: 16px;
  line-height: 26px;
  color: var(--paragraph-color);
}

.single-work.style-two {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  text-align: center;
  padding: 30px;
}

@media only screen and (max-width: 767.98px) {
  .single-work.style-two {
    padding: 20px;
  }
}

.single-work.style-two:hover .single-work-contents-title:hover {
  color: #fff;
}

.single-work.style-two .single-work-icon {
  background-color: #fff;
  color: var(--main-color-one);
  margin-inline: auto;
}

.single-work.style-two .single-work-contents-title {
  font-family: var(--heading-font);
  color: #fff;
  font-weight: 600;
}

div[class*=col]:nth-child(4n+1) .single-work.style-two {
  background-color: var(--main-color-one) !important;
  border-color: var(--main-color-one) !important;
}

div[class*=col]:nth-child(4n+1) .single-work.style-two .single-work-icon {
  background-color: #fff;
  color: var(--main-color-one);
}

div[class*=col]:nth-child(4n+2) .single-work:hover {
  border-color: #A760FF;
  -webkit-box-shadow: 0 0 70px rgba(167, 96, 255, 0.1);
  box-shadow: 0 0 70px rgba(167, 96, 255, 0.1);
  background-color: rgba(167, 96, 255, 0.05);
}

div[class*=col]:nth-child(4n+2) .single-work:hover .single-work-icon {
  background-color: #fff;
  color: #A760FF;
}

div[class*=col]:nth-child(4n+2) .single-work-icon {
  background-color: rgba(167, 96, 255, 0.1);
  color: #A760FF;
}

div[class*=col]:nth-child(4n+2) .single-work-contents-title:hover {
  color: #A760FF;
}

div[class*=col]:nth-child(4n+2) .single-work.style-two {
  background-color: #A760FF;
  border-color: #A760FF;
}

div[class*=col]:nth-child(4n+2) .single-work.style-two .single-work-icon {
  background-color: #fff;
  color: #A760FF;
}

div[class*=col]:nth-child(4n+3) .single-work:hover {
  border-color: #FF8D29;
  -webkit-box-shadow: 0 0 70px rgba(255, 141, 41, 0.1);
  box-shadow: 0 0 70px rgba(255, 141, 41, 0.1);
  background-color: rgba(255, 141, 41, 0.05);
}

div[class*=col]:nth-child(4n+3) .single-work:hover .single-work-icon {
  background-color: #fff;
  color: #FF8D29;
}

div[class*=col]:nth-child(4n+3) .single-work-icon {
  background-color: rgba(255, 141, 41, 0.1);
  color: #FF8D29;
}

div[class*=col]:nth-child(4n+3) .single-work-contents-title:hover {
  color: #FF8D29;
}

div[class*=col]:nth-child(4n+3) .single-work.style-two {
  background-color: #FF8D29;
  border-color: #FF8D29;
}

div[class*=col]:nth-child(4n+3) .single-work.style-two .single-work-icon {
  background-color: #fff;
  color: #FF8D29;
}

div[class*=col]:nth-child(4n+4) .single-work:hover {
  border-color: #6BCB77;
  -webkit-box-shadow: 0 0 70px rgba(107, 203, 119, 0.1);
  box-shadow: 0 0 70px rgba(107, 203, 119, 0.1);
  background-color: rgba(107, 203, 119, 0.05);
}

div[class*=col]:nth-child(4n+4) .single-work:hover .single-work-icon {
  background-color: #fff;
  color: #6BCB77;
}

div[class*=col]:nth-child(4n+4) .single-work-icon {
  background-color: rgba(107, 203, 119, 0.1);
  color: #6BCB77;
}

div[class*=col]:nth-child(4n+4) .single-work-contents-title:hover {
  color: #6BCB77;
}

div[class*=col]:nth-child(4n+4) .single-work.style-two {
  background-color: #6BCB77;
  border-color: #6BCB77;
}

div[class*=col]:nth-child(4n+4) .single-work.style-two .single-work-icon {
  background-color: #fff;
  color: #6BCB77;
}

.work-wrapper-inner {
  padding: 25px;
  border-radius: 10px;
}

.work-waves {
  position: absolute;
  top: 80%;
  right: 0;
}

@media only screen and (max-width: 480px) {
  .work-waves {
    display: none;
  }
}

.work-wrapper-contents-wave {
  position: relative;
  right: -24px;
  bottom: 0;
}

.work-wrapper-contents {
  position: relative;
}

.work-wrapper-contents-single-thumb {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 110px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  margin-inline: auto;
  cursor: pointer;
  position: absolute;
  left: 0;
  top: 0;
}

.work-wrapper-contents-single-thumb::after {
  position: absolute;
  content: "";
  height: calc(100% + 10px);
  width: calc(100% + 10px);
  top: -5px;
  left: -5px;
  background-color: transparent;
  border: 1px dashed var(--main-color-one);
  border-radius: 50%;
}

.work-wrapper-contents-single-thumb.tooltip-inner {
  background-color: var(--main-color-one);
}

.work-wrapper-contents-single-thumb:nth-child(2) {
  left: 41%;
  top: 50px;
  background-color: #A760FF;
}

.work-wrapper-contents-single-thumb:nth-child(2)::after {
  border-color: #A760FF;
}

.work-wrapper-contents-single-thumb:nth-child(3) {
  top: auto;
  left: auto;
  right: 0%;
  bottom: 0;
  background-color: #6BCB77;
}

.work-wrapper-contents-single-thumb:nth-child(3)::after {
  border-color: #6BCB77;
}

.shapes-one {
  height: 120px;
  width: 120px;
  position: absolute;
  bottom: 10px;
  left: 0px;
  z-index: 3;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .shapes-one {
    height: 80px;
    width: 80px;
  }
}

@media only screen and (max-width: 575.98px) {
  .shapes-one {
    height: 80px;
    width: 80px;
  }
}

@media only screen and (max-width: 480px) {
  .shapes-one {
    display: none;
  }
}

.shapes-one::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0%;
  bottom: 0%;
  background-color: var(--main-color-one);
  z-index: -1;
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 50%;
}

.shapes-one::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: -50%;
  left: 0;
  background-color: #fff;
  z-index: 1;
}

.shapes-one span {
  display: inline-block;
  height: 85px;
  width: 85px;
  border-radius: 50%;
  background-color: var(--secondary-color);
  margin: auto;
  position: absolute;
  top: -25px;
  left: 0;
  right: 0;
  z-index: 4;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .shapes-one span {
    height: 65px;
    width: 65px;
  }
}

@media only screen and (max-width: 575.98px) {
  .shapes-one span {
    height: 65px;
    width: 65px;
  }
}

.shapes-dot,
.shapes-dot-two {
  display: inline-block;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  position: absolute;
  bottom: 40px;
  left: 50%;
  z-index: 4;
}

.shapes-dot-two {
  background-color: var(--secondary-color);
  bottom: 45%;
  left: 80%;
  height: 15px;
  width: 15px;
}

/* jobs css */
.single-jobs {
  border: 1px solid rgba(221, 221, 221, 0.7);
  padding: 20px;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.single-jobs-border {
  border: 2px solid rgba(221, 221, 221, 0.7);
}

.single-jobs:hover {
  -webkit-box-shadow: 0 0 15px #ececec;
  box-shadow: 0 0 15px #ececec;
}

.single-jobs-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
  margin: -5px 0 0;
  -webkit-transition: .3s;
  transition: .3s;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.single-jobs-title:hover {
  color: var(--main-color-one);
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .single-jobs-title {
    font-size: 20px;
    line-height: 28px;
  }
}

.single-jobs-date {
  font-size: 16px;
  margin-top: 10px;
  color: var(--paragraph-color);
}
.single-jobs-date > span {
  color: var(--main-color-one);
  font-weight: 500;
}
.single-jobs-price {
  font-size: 20px;
  color: var(--main-color-one);
  margin-top: 20px;
  font-family: var(--heading-font);
  font-weight: 600;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px 7px;
  font-size: 20px;
  color: var(--main-color-one);
  font-weight: 600;
}

.single-jobs-price-fixed {
  font-size: 16px;
  font-weight: 400;
  bottom: 0;
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  border-radius: 30px;
  padding: 2px 12px 3px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.single-jobs-para {
  font-size: 15px;
  line-height: 26px;
  color: var(--paragraph-color);
  margin-top: 20px;
  word-break: break-all;
}

.single-jobs-tag {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 0;
  padding-left: 0;
}

.single-jobs-tag-link {
  display: inline-block;
  padding: 4px 15px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  background-color: unset;
  color: var(--heading-color);
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 30px;
  border: 1px solid var(--border-color);
}

.single-jobs-tag-link:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

/* testimonial Css */
.testimonial-area {
  position: relative;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .testimonial-shapes {
    display: none;
  }
}

.testimonial-shapes img {
  position: absolute;
  z-index: 0;
}

.testimonial-shapes img:nth-child(1) {
  left: 5%;
  top: 20%;
}

.testimonial-shapes img:nth-child(2) {
  left: 25%;
  top: 12%;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .testimonial-shapes img:nth-child(2) {
    top: 5%;
  }
}

.testimonial-shapes img:nth-child(3) {
  right: 10%;
  top: 15%;
}

.testimonial-shapes img:nth-child(4) {
  left: 12%;
  bottom: 20%;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .testimonial-shapes img:nth-child(4) {
    bottom: 15%;
  }
}

.testimonial-shapes img:nth-child(5) {
  right: 12%;
  bottom: 20%;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .testimonial-shapes img:nth-child(5) {
    bottom: 10%;
  }
}

.testimonial-tabs-wrap {
  display: table;
  margin-inline: auto;
  background: #fff;
  border-radius: 7px;
  padding: 5px;
  border: 1px solid #f3f3f3;
}

.testimonial-tabs {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.testimonial-tabs li {
  display: inline-block;
  padding: 7px 15px;
  border-radius: 5px;
}

.testimonial-tabs li.active {
  background-color: var(--main-color-one);
  color: #fff;
}

.single-testimonial {
  padding: 20px;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.single-testimonial:hover {
  -webkit-box-shadow: 0 0 15px #ececec;
  box-shadow: 0 0 15px #ececec;
}

.single-testimonial-author-flex {
  align-items: center;
}

.single-testimonial-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 20px 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.single-testimonial-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  margin: -5px 0 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -ms-flex-preferred-size: 60%;
  flex: 1;
  word-break: break-all;
}

.single-testimonial-title:hover {
  color: var(--main-color-one);
}
.single-testimonial-author-contents-title {
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
}
.single-testimonial-author-contents-para {
  font-size: 15px;
  font-weight: 400;
  line-height: 24px;
  color: var(--paragraph-color);
}
.single-testimonial-date {
  font-size: 15px;
  margin-top: 10px;
  display: block;
}
.single-testimonial-top .single-testimonial-date {
  margin-top: 0;
}
.single-testimonial-price {
  font-size: 24px;
  color: var(--main-color-one);
  margin-top: 20px;
  font-family: var(--heading-font);
  font-weight: 600;
}

.single-testimonial-price sub {
  font-size: 16px;
  font-weight: 400;
}

.single-testimonial-para {
  font-size: 15px;
  line-height: 26px;
  color: var(--paragraph-color);
  margin-top: 20px;
  word-break: break-word;
}

@media only screen and (max-width: 480px) {
  .single-testimonial-para {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
  }
}

.single-testimonial-author {
  margin-top: 20px;
  border-top: 1px solid #f3f3f3;
  padding-top: 20px;
}

.single-testimonial-author-thumb {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  background: var(--border-color);
}

.single-testimonial-author-thumb img {
  height: 100%;
  width: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.single-testimonial-author-contents-para {
  font-size: 14px;
  margin-top: 5px;
}

/* Pricing css */
.single-pricing {
  padding: 20px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  background-color: var(--white)
}

.single-pricing:hover {
  /* IE 9 */
  -webkit-transform: translateY(-5px);
  /* Chrome, Safari, Opera */
  transform: translateY(-5px);
  -webkit-box-shadow: 0 0 50px #e9e9e9;
  box-shadow: 0 0 50px #e9e9e9;
  border: transparent;
}

div[class*=col]:nth-child(2) .single-pricing {
  -webkit-box-shadow: 0 0 50px #e9e9e9;
  box-shadow: 0 0 50px #e9e9e9;
  border: 0;
}

div[class*=col]:nth-child(2) .single-pricing .btn-wrapper .cmn-btn {
  background-color: var(--main-color-one);
  color: var(--white);
}

.single-pricing:hover .btn-wrapper .cmn-btn {
  background-color: var(--main-color-one);
  color: var(--white);
}

.single-pricing.featured {
  -webkit-box-shadow: 0 0 50px #f3f3f3;
  box-shadow: 0 0 50px #f3f3f3;
  border: 0;
}

.single-pricing.featured .cmn-btn {
  background-color: var(--main-color-one);
  color: #fff;
}

.single-pricing.featured .cmn-btn:hover {
  background-color: var(--secondary-color);
}

.single-pricing-border {
  border: 1px solid #f3f3f3;
}

.single-pricing-title {
  font-size: 20px;
  line-height: 28px;
  font-weight: 700;
  margin-bottom: 0;
}
.single-pricing-para {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .single-pricing-title {
    font-size: 18px;
  }
}

@media only screen and (max-width: 375px) {
  .single-pricing-title {
    font-size: 18px;
  }
}

.single-pricing-top {
  border-bottom: 1px solid #f3f3f3;
  padding-bottom: 20px;
}

.single-pricing-list {
  margin-top: 10px;
}

.single-pricing-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  line-height: 30px;
  padding: 10px 0px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .single-pricing-list-item {
    font-size: 15px;
  }
}

.single-pricing-list-item-icon {
  background-color: rgba(var(--success-color-rgb), .2);
  color: var(--success-color);
  border: 1px solid var(--success-color);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 12px;
  height: 20px;
  width: 20px;
  flex-shrink: 0;
}

.single-pricing-list-item-icon.cross-icon {
  color: var(--danger-color);
  background-color: rgba(var(--danger-color-rgb), .2);
  border: 1px solid var(--danger-color);
}

.single-pricing-price {
  font-size: 28px;
  line-height: 1.2;
  font-weight: 700;
  margin-top: 10px;
}

.single-pricing-price sub {
  font-size: 16px;
  font-weight: 400;
  bottom: 0;
}

.tab-monthly.active+.input-switch:after {
  left: calc(100% - 5px);
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}

.tab-yearly.active+.input-switch:after {
  left: 5px;
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
}

.pricing-tabs {
  position: relative;
  display: inline-block;
  margin-inline: auto;
  text-align: center;
}

.pricing-tabs img {
  position: absolute;
  top: -30px;
  left: 57%;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-tabs img {
    left: 60%;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-tabs img {
    left: 63%;
  }
}

@media only screen and (max-width: 480px) {
  .pricing-tabs img {
    display: none;
  }
}

.pricing-tabs ul,
.pricing-tabs .tab-parents {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: relative;
}

.pricing-tabs ul li,
.pricing-tabs ul .tab-list,
.pricing-tabs .tab-parents li,
.pricing-tabs .tab-parents .tab-list {
  font-size: 18px;
  font-weight: 500;
  color: var(--heading-color);
  position: relative;
}

.pricing-tabs ul li.active,
.pricing-tabs ul .tab-list.active,
.pricing-tabs .tab-parents li.active,
.pricing-tabs .tab-parents .tab-list.active {
  color: var(--main-color-one);
}

.pricing-tabs-switch .input-switch {
  height: 0;
  width: 0;
  visibility: hidden;
}

.pricing-tabs-switch label {
  cursor: pointer;
  text-indent: -9999px;
  width: 60px;
  height: 30px;
  background: rgba(var(--main-color-one-rgb), 0.15);
  display: block;
  border-radius: 100px;
  position: relative;
}

.pricing-tabs-switch label:after {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  width: 20px;
  height: 20px;
  background: var(--main-color-one);
  border-radius: 50%;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing-tabs-switch .input-switch:checked+label {
  background: rgba(var(--main-color-one-rgb), 0.15);
}

.pricing-tabs-switch .input-switch:checked+label:after {
  left: calc(100% - 5px);
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}

/* Newsletter Css */
.newsletter-wrapper {
  position: relative;
  z-index: 2;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.newsletter-wrapper-padding {
  padding: 50px;
}

@media only screen and (max-width: 480px) {
  .newsletter-wrapper-padding {
    padding: 30px;
  }
}

@media only screen and (max-width: 375px) {
  .newsletter-wrapper-padding {
    padding: 30px 20px;
  }
}

.newsletter-wrapper-shapes img {
  position: absolute;
  z-index: -1;
  left: 0;
  top: 0;
  right: 0;
  margin-inline: auto;
}

.newsletter-bg {
  background-color: var(--main-color-one);
}

.newsletter-contents {
  max-width: 600px;
  margin: auto;
}

.newsletter-contents-title {
  font-size: 36px;
  line-height: 42px;
  color: #fff;
  font-weight: 500;
  margin: -7px 0 0;
  font-family: var(--Otomanopee-font);
}

@media only screen and (max-width: 767.98px) {
  .newsletter-contents-title {
    font-size: 30px;
    line-height: 36px;
  }
}

@media only screen and (max-width: 575.98px) {
  .newsletter-contents-title {
    font-size: 28px;
  }
}

@media only screen and (max-width: 480px) {
  .newsletter-contents-title {
    font-size: 26px;
  }
}

@media only screen and (max-width: 375px) {
  .newsletter-contents-title {
    font-size: 24px;
  }
}

.newsletter-contents-para {
  color: #fff;
}

.newsletter-contents-form .single-input {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
}

.newsletter-contents-form .single-input .form--control {
  height: 55px;
  border: 1px solid transparent;
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--light-color);
  color: rgba(255, 255, 255, 0.8);
  outline: none;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.01);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.01);
  -webkit-transition: 0.3s;
  transition: 0.3s;
  padding: 20px;
  width: 100%;
  padding-right: 100px;
}

.newsletter-contents-form .single-input .form--control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.newsletter-contents-form .single-input .form--control::-moz-placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.newsletter-contents-form .single-input .form--control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.newsletter-contents-form .single-input .form--control::-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.newsletter-contents-form .single-input .form--control::placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.newsletter-contents-form .single-input .form--control:focus {
  -webkit-box-shadow: 0 0 10px rgba(var(--secondary-color-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--secondary-color-rgb), 0.1);
  border: rgba(var(--secondary-color-rgb), 0.3);
}

.newsletter-contents-form .single-input button {
  position: absolute;
  right: 0;
  bottom: 0;
  height: 100%;
  border: 0;
  outline: none;
  padding: 5px 20px;
  background-color: var(--secondary-color);
  color: var(--heading-color);
}

/* Brand Logo Css */
.single-brand-thumb img {
  margin: auto;
}

/* Project css */
.single-project {
  border: 1px solid #f1f1f1;
  padding: 20px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.project-catalogue-flex {
  display: flex;
  gap: 20px;
}

.project-catalogue-contents .flex-between {
  gap: 10px 20px;
}

.profile-wrapper-item .single-project.project-catalogue {
  border: 1px solid var(--border-color);
  padding: 20px;
  border-radius: 10px;
}

.project-catalogue-thumb {
  max-width: 300px;
  box-shadow: 10px 0px 10px 0px #f6f6f6;
}

@media screen and (max-width: 767px) {
  .project-catalogue-flex {
    flex-wrap: wrap;
  }
}

@media screen and (max-width: 480px) {
  .single-project-orderCompleted {
    padding: 5px 15px;
    font-size: 14px;
  }

  .single-project-content-title {
    font-size: 20px;
  }

  .single-project-content-price {
    font-size: 20px;
  }

  .single-project-content-price s {
    font-size: 18px;
  }

  .single-project-delivery-icon {
    font-size: 14px;
  }

  .single-project-delivery-days {
    font-size: 16px;
  }
}

.project-catalogue-contents {
  flex: 1;
  margin-top: 0;
}

.single-project:not(:first-child) {
  margin-top: 24px;
}

.single-project:hover {
  -webkit-box-shadow: 0 0 15px #ececec;
  box-shadow: 0 0 15px #ececec;
  border: transparent;
  -webkit-transform: translateY(-5px);
  transform: translateY(-5px);
}

.single-project-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
}

.single-project-orderCompleted {
  display: inline-block;
  padding: 5px 12px;
  font-size: 15px;
  line-height: 20px;
  color: #65C18C;
  background: #E3FBE4;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  cursor: unset;
}
.single-project-orderCompleted:focus {
  background-color: #E3FBE4;
  color: #65C18C;
}
.single-project-orderCompleted:hover {
  color: #65C18C;
}

.single-project-content {
  margin-top: 25px;
}

.single-project-content-review {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
  padding: 5px 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 5px;
  gap: 5px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 15px;
  line-height: 20px;
}

.single-project-content-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 26px;
  margin: 15px 0 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.single-project-content-title>a {
  color: inherit;
}

.single-project-content-title:hover {
  color: var(--main-color-one);
}

.single-project-content-para {
  font-size: 15px;
  line-height: 26px;
  color: var(--paragraph-color);
  margin-top: 20px;
}

.single-project-content-price {
  font-size: 20px;
  color: var(--main-color-one);
  font-family: var(--heading-font);
  font-weight: 600;
}
.single-project-thumb img {
  border-radius: 7px;
}
.single-project-content-price s {
  font-size: 18px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.single-project-delivery {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.single-project-delivery-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 15px;
}

.single-project-delivery-days {
  font-size: 15px;
  font-weight: 600;
  color: var(--heading-color);
}

.single-project-bottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  border-top: 1px solid #f3f3f3;
  padding-top: 20px;
  margin-top: 20px;
}

.single-project-feedback:not(:first-child) {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.single-project-feedback-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 15px;
}

.single-project-feedback-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
}

.single-project-feedback-author-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px;
}

.single-project-feedback-author-thumb {
  max-width: 70px;
}

.single-project-feedback-author-thumb img {
  border-radius: 50%;
}

.single-project-feedback-author-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
}

.single-project-feedback-author-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

.single-project-feedback-author-para img {
  max-width: 40px;
  border-radius: 50%;
}

.single-project-feedback-contents-para {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
}

/* Freelancer Css */
.freelancer-child:nth-child(4n+2) .single-freelancer-badge-count,
.freelancer-area .slick-slide:nth-child(4n+2) .single-freelancer-badge-count {
  background-color: #FF5D5D;
}

.freelancer-child:nth-child(4n+2) .single-freelancer-author-thumb,
.freelancer-area .slick-slide:nth-child(4n+2) .single-freelancer-author-thumb {
  background-color: #FF5D5D;
}

.freelancer-child:nth-child(4n+3) .single-freelancer-badge-count,
.freelancer-area .slick-slide:nth-child(4n+3) .single-freelancer-badge-count {
  background-color: #34B3F1;
}

.freelancer-child:nth-child(4n+3) .single-freelancer-author-thumb,
.freelancer-area .slick-slide:nth-child(4n+3) .single-freelancer-author-thumb {
  background-color: #34B3F1;
}

.freelancer-child:nth-child(4n+4) .single-freelancer-badge-count,
.freelancer-area .slick-slide:nth-child(4n+4) .single-freelancer-badge-count {
  background-color: #53BF9D;
}

.freelancer-child:nth-child(4n+4) .single-freelancer-author-thumb,
.freelancer-area .slick-slide:nth-child(4n+4) .single-freelancer-author-thumb {
  background-color: #53BF9D;
}

.single-freelancer {
  position: relative;
  border: 1px solid #f3f3f3;
  padding: 20px;
}

.single-freelancer-badge {
  position: absolute;
  right: 20px;
  top: 20px;
}

.single-freelancer-badge-count {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  color: #fff;
  font-size: 10px;
  font-weight: 500;
  position: absolute;
  bottom: 4px;
  left: 5px;
}
.single-freelancer-author-thumb {
  display: -webkit-box;
  display: -ms-flexbox;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  width: 110px;
  height: 110px;
  border-radius: 50%;
  margin-inline: auto;
  position: relative;
}
.single-freelancer-author-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.single-freelancer-author-status {
  display: inline-block;
  padding: 1px 10px 3px 20px;
  border: 1px solid var(--active-color);
  color: var(--active-color);
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  position: relative;
  border-radius: 30px;
}

 .single-freelancer-author-status-ofline {
   display: inline-block;
   padding: 2px 10px 4px 20px;
   border: 1px solid #d3d3d3;
   color: var(--body-color);
   font-size: 14px;
   line-height: 18px;
   font-weight: 400;
   position: relative;
   border-radius: 30px;
 }

.single-freelancer-author-status-ofline::before {
  content: "";
  position: absolute;
  height: 6px;
  width: 6px;
  background-color: var(--body-color);
  border-radius: 50%;
  left: 9px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
}

.single-freelancer-author-status::before {
  content: "";
  position: absolute;
  height: 6px;
  width: 6px;
  background-color: var(--active-color);
  border-radius: 50%;
  left: 10px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
}

.single-freelancer-author-status.offline {
  border: 1px solid var(--light-color);
  color: var(--light-color);
  display: inline-block;
  padding: 3px 15px 3px 30px;
  border: 1px solid #ddd;
  color: #ddd;
  line-height: 1;
  font-size: 16px;
  font-weight: 500;
  position: relative;

}

.single-freelancer-author-status.offline::before {
  background-color: var(--light-color);
}

.single-freelancer-author-name {
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.single-freelancer-author-para {
  font-size: 15px;
  font-weight: 400;
  display: block;
}

.single-freelancer-bottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  border-top: 1px solid #f3f3f3;
  padding-top: 20px;
  margin-top: 20px;
}

/* hiring Css */
.hiring-wrapper {
  padding: 0 40px;
  position: relative;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .hiring-wrapper {
    padding: 0 30px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .hiring-wrapper {
    padding: 30px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .hiring-wrapper {
    padding: 0 30px 30px;
  }
}

@media only screen and (max-width: 575.98px) {
  .hiring-wrapper {
    padding: 0 20px 20px;
  }
}

@media only screen and (max-width: 375px) {
  .hiring-wrapper {
    padding: 0 10px 10px;
  }
}

.hiring-wrapper::before {
  content: "";
  position: absolute;
  height: calc(100% - 80px);
  width: 100%;
  left: 0;
  top: 40px;
  background-color: var(--section-bg-1);
  z-index: -1;
  border-radius: 20px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .hiring-wrapper::before {
    height: calc(100% - 40px);
    top: 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .hiring-wrapper::before {
    height: 100%;
    top: 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .hiring-wrapper::before {
    height: calc(100% - 80px);
    top: auto;
    bottom: 0;
  }
}

.hiring-wrapper-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 30px 20px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .hiring-wrapper-flex {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
  }
}

.hiring-wrapper-contents {
  max-width: 570px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .hiring-wrapper-contents {
    max-width: 470px;
  }
}

.hiring-wrapper-contents-title {
  margin: -12px 0 0;
  font-family: var(--Otomanopee-font);
  font-size: 42px;
  font-weight: 400;
  line-height: 48px;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

@media only screen and (max-width: 767.98px) {
  .hiring-wrapper-contents-title {
    font-size: 36px;
    line-height: 42px;
  }
}

@media only screen and (max-width: 575.98px) {
  .hiring-wrapper-contents-title {
    font-size: 32px;
    line-height: 36px;
  }
}

@media only screen and (max-width: 480px) {
  .hiring-wrapper-contents-title {
    font-size: 28px;
    line-height: 32px;
  }
}

@media only screen and (max-width: 375px) {
  .hiring-wrapper-contents-title {
    font-size: 26px;
  }
}

.hiring-wrapper-contents-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 26px;
  font-weight: 400;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Category Css */
.single-category {
  padding: 20px 30px;
  height: 100%;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  position: relative;
}
.category-slider-item .single-category {
  min-height: 168px;
}
.single-category:hover {
  -webkit-box-shadow: 0 0 40px rgba(29, 41, 57, 0.12);
  box-shadow: 0 0 40px rgba(29, 41, 57, 0.12);
  /* IE 9 */
  -webkit-transform: translateY(-5px);
  /* Chrome, Safari, Opera */
  transform: translateY(-5px);
}

.single-category:hover::before {
  /* IE 9 */
  -webkit-transform: rotate(45deg);
  /* Chrome, Safari, Opera */
  transform: rotate(45deg);
}
.center-text .single-category-icon,
.text-center .single-category-icon {
  text-align: center;
}
.center-text .single-category-icon img,
.text-center .single-category-icon img {
  margin-inline: auto;
}

.single-category::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  z-index: -1;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 10px;
}

.single-category-contents {
  margin-top: 15px;
}

.single-category-contents-title {
  font-size: 20px;
  font-weight: 600;
  -webkit-transition: 0.2s;
  transition: 0.2s;
  color: var(--heading-color);
}

.single-category-contents-subtitle {
  margin-top: 5px;
}
.slick-slide:nth-child(6n+2) .single-category::before,
div[class*=col]:nth-child(6n+2) .single-category::before {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
}
.slick-slide:nth-child(6n+3) .single-category::before,
div[class*=col]:nth-child(6n+3) .single-category::before {
  background-color: rgba(167, 96, 255, 0.1);
}
.slick-slide:nth-child(6n+4) .single-category::before,
div[class*=col]:nth-child(6n+4) .single-category::before {
  background-color: rgba(3, 83, 151, 0.1);
}
.slick-slide:nth-child(6n+5) .single-category::before,
div[class*=col]:nth-child(6n+5) .single-category::before {
  background-color: rgba(6, 154, 142, 0.1);
}
.slick-slide:nth-child(6n+6) .single-category::before,
div[class*=col]:nth-child(6n+6) .single-category::before {
  background-color: rgba(125, 30, 106, 0.1);
}

/* Choose Css */
.choose-contents-list {
  margin: 0;
  padding: 0;
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px 25px;
}

.choose-contents-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 8px;
  font-size: 16px;
  color: var(--paragraph-color);
  font-weight: 500;
  -webkit-transition: 0.2s;
  transition: 0.2s;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.choose-contents-list-item:hover {
  color: var(--main-color-one);
}

.choose-contents-list-item:hover::before {
  background-color: var(--main-color-one);
}

.choose-contents-list-item::before {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  background-color: #00C897;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  font-size: 10px;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
  color: #fff;
  margin-top: 5px;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.choose-wrapper {
  margin-left: 120px;
  position: relative;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .choose-wrapper {
    margin-left: 60px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .choose-wrapper {
    margin-left: 40px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .choose-wrapper {
    margin-left: auto;
    margin-right: 120px;
  }
}

@media only screen and (max-width: 767.98px) {
  .choose-wrapper {
    margin-left: 0;
    margin-right: 0;
  }
}

.choose-wrapper-thumb {
  margin-inline: auto;
  text-align: center;
  margin-bottom: -160px;
}

@media only screen and (max-width: 375px) {
  .choose-wrapper-thumb {
    margin-bottom: -100px;
  }
}

.choose-wrapper-thumb-shapes img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2;
  margin-inline: auto;
}

.choose-wrapper-shapes {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  border-radius: 20px;
  padding-top: 70px;
}

/* appStore Css */
.single-appStore {
  position: relative;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  overflow: hidden;
}

.single-appStore:hover .single-appStore-flex::before {
  background-color: rgba(var(--main-color-one-rgb), 0.7);
}

.single-appStore:hover .single-appStore-contents-title {
  color: var(--heading-color);
}

.single-appStore-shapes {
  position: absolute;
  right: -60%;
  top: 0;
  z-index: -1;
}

@media only screen and (max-width: 767.98px) {
  .single-appStore-shapes {
    right: -10%;
  }
}

.single-appStore-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 25px 35px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  position: relative;
  padding: 0 40px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .single-appStore-flex {
    gap: 20px 10px;
    padding: 0 20px;
  }
}

@media only screen and (max-width: 767.98px) {
  .single-appStore-flex {
    display: -ms-grid;
    display: grid;
    gap: 30px 0;
    text-align: center;
  }
}

@media only screen and (max-width: 480px) {
  .single-appStore-flex {
    padding: 0 20px;
  }
}

.single-appStore-flex::before {
  content: "";
  position: absolute;
  height: calc(100% - 86px);
  width: 100%;
  left: 0px;
  bottom: 0;
  background-color: var(--main-color-one);
  z-index: -1;
  border-radius: 20px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .single-appStore-flex::before {
    height: calc(100% - 80px);
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .single-appStore-flex::before {
    height: calc(100% - 134px);
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .single-appStore-flex::before {
    height: calc(100% - 134px);
  }
}

@media only screen and (max-width: 767.98px) {
  .single-appStore-flex::before {
    height: 100%;
  }
}

.single-appStore-contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 30px 0;
  position: relative;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .single-appStore-contents {
    padding: 20px 0;
  }
}

@media only screen and (max-width: 767.98px) {
  .single-appStore-contents {
    padding: 30px 0 0;
  }
}

.single-appStore-contents-title {
  font-size: 32px;
  line-height: 40px;
  font-family: var(--Otomanopee-font);
  font-weight: 400;
  color: #fff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .single-appStore-contents-title {
    font-size: 26px;
    line-height: 32px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .single-appStore-contents-title {
    font-size: 28px;
    line-height: 36px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .single-appStore-contents-title {
    font-size: 28px;
    line-height: 36px;
  }
}

@media only screen and (max-width: 480px) {
  .single-appStore-contents-title {
    font-size: 26px;
    line-height: 32px;
  }
}

@media only screen and (max-width: 767.98px) {
  .single-appStore-btn {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

.single-appStore-btn img {
  border-radius: 5px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .single-appStore-thumb {
    max-width: 180px;
  }
}

div[class*=col]:nth-child(2n+2) .single-appStore:hover .single-appStore-flex::before {
  background-color: rgba(var(--secondary-color-rgb), 0.7);
}

div[class*=col]:nth-child(2n+2) .single-appStore-shapes {
  right: -100%;
}

@media only screen and (max-width: 767.98px) {
  div[class*=col]:nth-child(2n+2) .single-appStore-shapes {
    right: -15%;
  }
}

div[class*=col]:nth-child(2n+2) .single-appStore-flex::before {
  background-color: var(--secondary-color);
}

div[class*=col]:nth-child(2n+2) .single-appStore-contents-title {
  color: var(--heading-color);
}

/* Choose Account Css */
.choose-account-area {
  overflow: hidden;
}
.choose-account {
  max-width: 600px;
  margin-inline: auto;
}

.choose-account.center-text .choose-account-flex {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.choose-account-title {
  font-size: 32px;
  line-height: 36px;
  font-weight: 600;
  margin: -5px 0 0;
  color: var(--heading-color);
}

.choose-account-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.choose-account-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.choose-account-single {
  border: 1px solid var(--border-color);
  padding: 20px;
  border-radius: 10px;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  background-color: #fff;
}

.choose-account-single.selected {
  border-color: var(--main-color-one);
  background: rgba(var(--main-color-one-rgb), 0.1);
}

.choose-account-single.selected .choose-account-single-thumb svg path {
  fill: var(--main-color-one);
}

.choose-account-single.selected .choose-account-single-contents-title {
  color: var(--main-color-one);
}

.choose-account-single-thumb svg path {
  fill: var(--paragraph-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.choose-account-single-contents-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--paragraph-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

/* Sign In Sign Up */
.login-area {
  overflow: hidden;
}
.login-padding {
  padding: 30px;
}

@media only screen and (max-width: 480px) {
  .login-padding {
    padding: 20px;
  }
}

@media only screen and (max-width: 375px) {
  .login-padding {
    padding: 20px 10px;
  }
}

.single-title {
  font-size: 28px;
  line-height: 36px;
  margin: -6px 0 0;
}

.single-para {
  font-size: 16px;
  line-height: 24px;
  margin: 5px 0 0;
  color: var(--paragraph-color);
}

.login-shadow {
  -webkit-box-shadow: 0 0 10px var(--border-color);
  box-shadow: 0 0 10px var(--border-color);
}

.login-wrapper-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 25px 50px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .login-wrapper-flex {
    gap: 25px;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .login-wrapper-flex {
    gap: 25px;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
  }
}

.login-wrapper-thumb {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .login-wrapper-thumb {
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto;
  }
}

.login-wrapper-contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  max-width: 550px;
}
.login-wrapper-contents.margin-inline {
  margin-inline: auto;
}
@media (min-width: 1200px) and (max-width: 1399.98px) {
  .login-wrapper-contents {
    max-width: 480px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .login-wrapper-contents {
    max-width: 480px;
  }
}

.login-wrapper-contents-title {
  font-size: 32px;
  line-height: 36px;
  margin-bottom: 20px;
}

.login-wrapper-contents-form .submit-btn {
  border-radius: 7px;
}

.login-wrapper .checkbox-inline .check-input {
  border-radius: 3px !important;
}

.login-right {
  position: relative;
  z-index: 2;
  padding: 100px 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .login-right {
    padding: 70px 0 55px;
  }
}

@media only screen and (max-width: 480px) {
  .login-right {
    padding: 70px 0 50px;
  }
}

.login-right::before {
  content: "";
  position: absolute;
  left: -50px;
  top: 0;
  height: 100%;
  width: 100vw;
  background-color: var(--main-color-one);
  z-index: -1;
  border-radius: 20px 0 0 20px;
}

.login-right-item {
  padding: 50px 50px 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .login-right-item {
    text-align: center;
  }
  .login-right::before {
    left: 0;
  }
}

@media only screen and (max-width: 480px) {
  .login-right-item {
    padding: 0;
  }
}

.login-right-shapes {
  position: relative;
  z-index: 1;
  display: inline-block;
}

.login-right-shapes-item img {
  position: absolute;
  z-index: -1;
}

@media only screen and (max-width: 480px) {
  .login-right-shapes-item img {
    display: none;
  }
}

.login-right-shapes-item img:nth-child(1) {
  left: -40px;
  top: 20px;
}

.login-right-shapes-item img:nth-child(2) {
  right: -55px;
  top: -60px;
}

.login-right-shapes-item img:nth-child(3) {
  left: -20px;
  bottom: -20px;
}

.login-right-shapes-item img:nth-child(4) {
  right: -60px;
  bottom: 40px;
}

.login-right-thumb img {
  border-radius: 20px;
}

.login-right-contents {
  margin-top: 40px;
}

.login-right-contents.text-white .login-right-contents-title {
  color: #fff;
}

.login-right-contents.text-white .login-right-contents-para {
  color: rgba(255, 255, 255, 0.8);
}

.login-right-contents-title {
  font-size: 32px;
  line-height: 36px;
}

@media only screen and (max-width: 767.98px) {
  .login-right-contents-title {
    font-size: 28px;
  }
}

@media only screen and (max-width: 575.98px) {
  .login-right-contents-title {
    font-size: 24px;
    line-height: 32px;
  }
}

.login-right-contents-para {
  font-size: 16px;
  line-height: 26px;
  margin-top: 20px;
}

.input-flex-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media only screen and (max-width: 480px) {
  .input-flex-item {
    display: block;
  }
}

.input-flex-item .single-input {
  width: 100%;
}

.input-flex-item .single-input:not(:first-child) {
  margin-left: 24px;
}

@media only screen and (max-width: 480px) {
  .input-flex-item .single-input:not(:first-child) {
    margin-left: 0;
  }
}

.input-flex-item .single-input .form--control {
  border: 1px solid rgba(221, 221, 221, 0.4);
}

.single-input {
  display: inline-block;
  width: 100%;
}

.single-input .form--control {
  border: 1px solid rgba(221, 221, 221, 0.4);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  -webkit-box-shadow: 0 0 10px transparent;
  box-shadow: 0 0 10px transparent;
  border-radius: 7px;
  height: 48px;
  padding-inline: 12px;
}

.single-input .form--control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.single-input .form--message {
  padding: 15px 0 0 15px;
}

.label-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--heading-color);
  margin-bottom: 10px;
  display: block;
}

@media only screen and (max-width: 480px) {
  .label-title {
    font-size: 15px;
  }
}

.single-checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

.or-contents {
  text-align: center;
  position: relative;
  z-index: 2;
}

.or-contents::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  background: rgba(221, 221, 221, 0.4);
  width: 100%;
  height: 1px;
  z-index: -1;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.or-contents-para {
  background: #fff;
  padding: 10px 30px;
  color: var(--paragraph-color);
}

.login-others-single:not(:last-child) {
  margin-bottom: 20px;
}

.login-others-single-btn {
  padding: 13px 10px;
  display: inline-block;
  border: 1px solid rgba(221, 221, 221, 0.4);
  color: var(--heading-color);
  font-weight: 500;
  text-align: center;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border-radius: 7px;
}

.login-others-single-btn:hover {
  border-color: var(--main-color-one);
  color: var(--heading-color);
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.login-others-single-btn img {
  margin-right: 5px;
}

/* Setup Account Css */
.setup-right-border {
  border-right: 1px solid var(--border-color);
}

.setup-top-border {
  border-top: 1px solid var(--border-color);
}

.setup-bottom-border {
  border-bottom: 1px solid var(--border-color);
}

.setup-header {
  padding: 25px 0;
}

.setup-header-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

.setup-header-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
  margin: -4px 0 0;
}

.setup-header-skip {
  font-size: 16px;
  font-weight: 500;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.setup-header-skip:hover {
  color: var(--main-color-one);
}

.setup-footer {
  padding-top: 30px;
}

.setup-footer-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 25px 10px;
}

.setup-footer-back {
  background-color: #f3f3f3;
  padding: 10px 30px;
  color: var(--heading-color);
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  font-size: 16px;
  font-weight: 500;
}

.setup-footer-back:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.setup-footer-next {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  background-color: var(--main-color-one);
  color: #fff;
  border-radius: 50%;
  font-size: 16px;
  position: relative;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  overflow: hidden;
}

.setup-footer-next:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
  color: #fff;
}

.setup-footer-next:focus {
  color: #fff;
}

@-webkit-keyframes setupForm {
  from {
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
    opacity: 0;
  }

  to {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes setupForm {
  from {
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
    opacity: 0;
  }

  to {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}

.setup-wrapper {
  padding: 50px 0;
}

.setup-wrapper-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 50px 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .setup-wrapper-flex {
    display: -ms-grid;
    display: grid;
  }
}

.setup-wrapper-left {
  max-width: 350px;
  padding-right: 50px;
  margin-right: 50px;
  position: relative;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .setup-wrapper-left {
    margin-right: 30px;
    padding-right: 30px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .setup-wrapper-left {
    padding-right: 0;
    margin-right: 0;
    max-width: 100%;
  }
}

.setup-wrapper-left::before {
  content: "";
  position: absolute;
  height: calc(100% + 100px);
  width: 1px;
  right: 0;
  top: -50px;
  background-color: #f3f3f3;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .setup-wrapper-left::before {
    display: none;
  }
}

.setup-wrapper-contents {
  max-width: 550px;
  display: none;
}

.setup-wrapper-contents.active {
  display: block;
  -webkit-animation: setupForm 0.3s linear;
  animation: setupForm 0.3s linear;
}

.setup-wrapper-contents-item:not(:last-child) {
  margin-bottom: 40px;
}

.setup-wrapper-contents-item-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.setup-wrapper-contents-title {
  font-size: 24px;
  line-height: 28px;
  font-weight: 600;
  margin: -3px 0 24px 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .setup-wrapper-contents-title {
    font-size: 24px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 480px) {
  .setup-wrapper-contents-title {
    font-size: 24px;
    line-height: 28px;
  }
}

.setup-wrapper-contents-title-two {
  font-size: 24px;
  line-height: 28px;
  margin: -3px 0 25px;
}

.setup-wrapper-contents-form-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.setup-wrapper-contents-form-item .form--control {
  height: 38px;
  border: 1px solid var(--border-color);
  border-radius: 7px;
  width: 100%;
  padding: 20px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: transparent;
  box-shadow: transparent;
  font-size: 16px;
  color: var(--paragraph-color);
}

.setup-wrapper-contents-form-item .form--control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.4);
  -webkit-box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
}

.setup-wrapper-contents-form-item .form--message {
  height: 150px;
}

.setup-wrapper-contents-btn {
  padding: 10px 30px;
  display: inline-block;
  font-size: 16px;
  background-color: var(--main-color-one);
  border-radius: 10px;
  color: #fff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.setup-wrapper-contents-btn:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
  color: #fff;
}

.setup-wrapper-experience-add {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 5px;
  border: 1px solid var(--border-color);
  padding: 10px;
}

.setup-wrapper-experience-add-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
}

.setup-wrapper-experience-add-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  color: #fff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.setup-wrapper-experience-add-icon:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
  color: #fff;
}

.setup-wrapper-experience-details {
  padding: 20px;
  border: 1px solid #f3f3f3;
  border-radius: 10px;
}

.setup-wrapper-experience-details:not(:last-child) {
  margin-bottom: 25px;
}

.setup-wrapper-experience-details-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 20px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.setup-wrapper-experience-details-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.setup-wrapper-experience-details-title {
  font-size: 20px;
  font-weight: 600;
}

.setup-wrapper-experience-details-subtitle {
  font-size: 16px;
  line-height: 24px;
  margin-top: 8px;
}

.setup-wrapper-experience-details-edit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border: 1px solid #f3f3f3;
  font-size: 16px;
  border-radius: 4px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: var(--body-color);
}

.setup-wrapper-experience-details-edit:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.setup-wrapper-experience-details-list {
  margin: 0;
  padding: 0;
  list-style: none;
  border-top: 1px solid #f3f3f3;
  padding-top: 20px;
  margin-top: 20px;
}

.setup-wrapper-experience-details-list-item {
  font-size: 16px;
  font-weight: 400;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px 10px;
}

.setup-wrapper-experience-details-list-item:not(:first-child) {
  margin-top: 15px;
}

.setup-wrapper-experience-details-list-item .list-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.setup-wrapper-experience-details-list-item .list-inner-para a {
  color: var(--main-color-one);
  font-weight: 500;
  margin-left: 2px;
}

.setup-wrapper-work-single {
  padding: 20px;
  border: 1px solid #f3f3f3;
  border-radius: 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 15px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  background-color: #fff;
}

.setup-wrapper-work-single.active {
  border-color: var(--main-color-one);
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.setup-wrapper-work-single:hover {
  border-color: var(--main-color-one);
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.setup-wrapper-work-single-title {
  font-size: 16px;
  line-height: 22px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.setup-wrapper-work-single-title:hover {
  color: var(--main-color-one);
}

.setup-wrapper-work-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0;
  margin: 0;
  list-style: none;
}

.setup-wrapper-work-list-item {
  display: inline-block;
  padding: 8px 20px;
  border: 1px solid #f3f3f3;
  font-size: 16px;
  line-height: 20px;
  border-radius: 5px;
  cursor: pointer;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .setup-wrapper-work-list-item {
    padding: 5px 12px;
  }
}

.setup-wrapper-work-list-item.active,
.setup-wrapper-work-list-item:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-color: var(--main-color-one);
}

.setup-wrapper-skill-para {
  font-size: 16px;
  line-height: 24px;
  color: var(--paragraph-color);
}

.setup-wrapper-finish-profile-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 20px;
}

.setup-wrapper-finish-profile-thumb {
  width: 120px;
  height: 120px;
  border-radius: 50%;
}

.setup-wrapper-finish-profile-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.setup-wrapper-finish-profile-content {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.setup-wrapper-finish-profile-content .cmn-btn {
  position: relative;
}

.setup-wrapper-finish-profile-content .cmn-btn input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  opacity: 0;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.setup-wrapper-finish-profile-content-para {
  font-size: 14px;
  margin-top: 10px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .single-setup-request-list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 0 30px;
  }
}

.single-setup-request-list-item {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  gap: 20px;
  background-color: #fff;
  padding-bottom: 35px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .single-setup-request-list-item {
    display: -ms-grid;
    display: grid;
    -ms-flex-preferred-size: 45%;
    flex-basis: 45%;
  }
}

@media only screen and (max-width: 480px) {
  .single-setup-request-list-item {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

@media only screen and (max-width: 375px) {
  .single-setup-request-list-item {
    gap: 10px;
  }
}

.single-setup-request-list-item.completed::before {
  background-color: #65C18C;
}

.single-setup-request-list-item::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 2px;
  top: 35px;
  left: 17px;
  background: #f3f3f3;
  z-index: 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .single-setup-request-list-item::before {
    height: 2px;
    width: 100%;
    top: 18px;
    left: 0px;
  }
}

.single-setup-request-list-item:last-child {
  padding-bottom: 0;
}

.single-setup-request-list-item:last-child::before {
  display: none;
}

.single-setup-request-list-item.completed .single-setup-request-list-item-number {
  border-color: #65C18C;
  color: #fff;
}

.single-setup-request-list-item.completed .single-setup-request-list-item-number::before {
  background-color: #65C18C;
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  z-index: 9;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 12px;
}

.single-setup-request-list-item.running .single-setup-request-list-item-number {
  border-color: var(--main-color-one);
  color: #fff;
}

.single-setup-request-list-item.running .single-setup-request-list-item-number::before {
  background-color: var(--main-color-one);
}

.single-setup-request-list-item.running .single-setup-request-list-item-contents-title {
  color: var(--main-color-one);
}

.single-setup-request-list-item-number {
  display: inline-block;
  position: relative;
  z-index: 2;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 35px;
  width: 35px;
  color: #222;
  font-size: 16px;
  font-weight: 500;
  border: 1px solid #f3f3f3;
  background-color: unset;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .single-setup-request-list-item-number {
    background-color: #fff;
  }
}

.single-setup-request-list-item-number::before {
  content: "";
  position: absolute;
  height: calc(100% - 10px);
  width: calc(100% - 10px);
  top: 5px;
  left: 5px;
  background-color: #fff;
  border-radius: 50%;
  z-index: -1;
}

.single-setup-request-list-item-contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.single-setup-request-list-item-contents-title {
  font-size: 20px;
  font-weight: 600;
}

.single-setup-request-list-item-contents-para {
  font-size: 15px;
  font-weight: 400;
  line-height: 24px;
  margin-top: 10px;
}

.single-setup-account .single-setup-request-list {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

@media only screen and (max-width: 767.98px) {
  .single-setup-account .single-setup-request-list {
    gap: 10px;
  }
}

.single-setup-account .single-setup-request-list-item {
  padding-bottom: 0;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.single-setup-account .single-setup-request-list-item:not(:last-child) {
  padding-right: 100px;
}

@media only screen and (max-width: 767.98px) {
  .single-setup-account .single-setup-request-list-item:not(:last-child) {
    padding-right: 50px;
  }
}

@media only screen and (max-width: 480px) {
  .single-setup-account .single-setup-request-list-item:not(:last-child) {
    padding: 0;
  }
}

.single-setup-account .single-setup-request-list-item:not(:last-child)::before {
  content: "";
  position: absolute;
  height: 2px;
  width: 90px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  left: auto;
  right: 0px;
  background: #f3f3f3;
  z-index: 0;
}

@media only screen and (max-width: 767.98px) {
  .single-setup-account .single-setup-request-list-item:not(:last-child)::before {
    width: 50px;
  }
}

@media only screen and (max-width: 480px) {
  .single-setup-account .single-setup-request-list-item:not(:last-child)::before {
    display: none;
  }
}

.account-setup-wrapper {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.account-setup-wrapper .setup-wrapper-contents {
  max-width: 100%;
}

/* Tags Input Css */
.tags-input-wrapper {
  background: transparent;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #f3f3f3;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  min-width: 100%;
}

.tags-input-wrapper input {
  border: none;
  background: transparent;
  outline: none;
  max-width: 100px;
  padding: 0 10px;
}

.tags-input-wrapper input:focus {
  border: 1px solid rgba(var(--main-color-one-rgb), 0.2);
}

.tags-input-wrapper .tag {
  display: inline-block;
  background-color: var(--border-color);
  color: var(--heading-color);
  border-radius: 4px;
  padding: 0px 7px;
  -webkit-box-shadow: 0 5px 35px -2px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 5px 35px -2px rgba(var(--main-color-one-rgb), 0.1);
}

.tags-input-wrapper .tag a {
  margin: 0px 0px 0px 4px;
  display: inline-block;
  cursor: pointer;
}

/* Popup Modal */
.popup-fixed {
  position: fixed;
  top: 50%;
  left: 0%;
  right: 0;
  margin-inline: auto;
  padding: 0 5px;
  -webkit-transform: translateY(-50%) scale(0.6);
  transform: translateY(-50%) scale(0.6);
  z-index: 9992;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  max-width: -webkit-fit-content;
  max-width: -moz-fit-content;
  max-width: fit-content;
}

.popup-fixed.popup-active {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateY(-50%) scale(1);
  transform: translateY(-50%) scale(1);
}

.popup-overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9991;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.popup-overlay.popup-active {
  visibility: visible;
  opacity: 1;
}

.popup-contents {
  max-width: 600px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  background-color: #fff;
  padding: 30px;
  margin: auto;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  max-height: calc(100vh - 50px);
  overflow-y: auto;
  scrollbar-color: var(--main-color-one) #e6e6e6;
  scrollbar-width: thin;
}

.popup-contents::-webkit-scrollbar {
  width: 5px;
  height: 8px;
  background-color: #d3d3d3;
  border-radius: 10px;
}

.popup-contents::-webkit-scrollbar-thumb {
  background-color: var(--main-color-one);
  border-radius: 10px;
}

.popup-contents-close {
  position: absolute;
  right: 0;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  background-color: #f3f3f3;
  color: #ff0000;
  font-size: 18px;
  -webkit-box-shadow: 0 0 10px #f3f3f3;
  box-shadow: 0 0 10px #f3f3f3;
  cursor: pointer;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.popup-contents-close:hover {
  background-color: #ff0000;
  color: #fff;
}

.popup-contents-changePhoto {
  max-width: 550px;
}

.popup-contents-changePhoto img {
  border-radius: 10px;
}

.popup-contents-portfolio-thumb img {
  border-radius: 10px;
}

.popup-contents-title {
  font-size: 24px;
  line-height: 28px;
  font-weight: 600;
  color: var(--heading-color);
  margin: -4px 0 10px;
}

.popup-contents-para {
  color: var(--paragraph-color);
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 30px;
}

.popup-contents-btn {
  margin-top: 25px;
}

.popup-contents-withdraw-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.popup-contents-withdraw-item:not(:last-child) {
  border-bottom: 1px solid #EAECF0;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.popup-contents-withdraw-title {
  font-size: 16px;
  color: var(--heading-color);
  font-weight: 500;
  line-height: 24px;
}

.popup-contents-withdraw-price {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
}

.popup-contents-withdraw-exchange {
  background-color: #EAECF0;
  padding: 20px;
  border-radius: 5px;
}

.popup-contents-withdraw-exchange-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 26px;
}

.popup-contents-withdraw-exchange-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
}

.popup-contents-withdraw-exchange-para-link {
  color: var(--main-color-one);
  padding: 0 5px;
  display: inline-block;
  font-weight: 500;
}

.popup-contents-encrypt-para {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 7px;
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.popup-contents-encrypt-para i {
  margin-top: 3px;
}

.popup-contents-transaction-link {
  display: block;
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.popup-contents-transaction-link:not(:first-child) {
  padding-top: 10px;
  margin-top: 10px;
  border-top: 1px solid #EAECF0;
}

.popup-contents-transaction-link:hover {
  color: var(--main-color-one);
}

.popup-contents-milestone-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.popup-contents-milestone-list-item {
  display: block;
}

.popup-contents-milestone-list-item:not(:first-child) {
  margin-top: 10px;
}

.popup-contents-milestone-list-item a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  padding: 10px 20px;
  background-color: var(--border-color);
  border-radius: 5px;
  color: var(--heading-color);
  font-size: 14px;
  font-weight: 500;
}

/* Send offer css */
.sendOffer-para {
  background-color: #F2F4F7;
  padding: 20px;
  color: var(--paragraph-color);
  border-radius: 10px;
}

.proposal-input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
}

.proposal-input-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.proposal-input-left-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.proposal-input-item {
  border: 1px solid #ddd;
  border-right: 0;
  padding: 5px 10px;
  height: 55px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 7px 0 0 7px;
}

.proposal-input-budget {
  white-space: nowrap;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  background-color: #F2F4F7;
  padding: 5px 15px;
  border-radius: 5px;
  color: var(--heading-color);
}

.proposal-input-budget .price {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
}

.proposal-input .single-input {
  margin-top: 0;
}

.proposal-input .single-input .form--control {
  border-radius: 0 7px 7px 0;
}

.proposal-input-right {
  -ms-flex-preferred-size: 180px;
  flex-basis: 180px;
}

.proposal-input-right-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  height: 55px;
  border: 1px solid #ddd;
  padding: 0 15px;
  background-color: #F2F4F7;
  border-radius: 7px;
}

.proposal-input-right-price {
  font-size: 20px;
  font-weight: 600;
  color: var(--main-color-one);
}

.proposal-input-right-question {
  cursor: pointer;
  font-size: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 22px;
  width: 22px;
  border: 1px solid var(--paragraph-color);
  color: var(--paragraph-color);
  border-radius: 50%;
}

/* Congratulation Css */
.congratulation-wrapper {
  max-width: 550px;
  margin-inline: auto;
  -webkit-box-shadow: 0 0 20px #f3f3f3;
  box-shadow: 0 0 20px #f3f3f3;
  padding: 30px 20px;
  background-color: #fff;
  border-radius: 10px;
}

.congratulation-contents.center-text .congratulation-contents-icon {
  margin-inline: auto;
}

.congratulation-contents-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100px;
  width: 100px;
  background-color: var(--success-color);
  color: #fff;
  font-size: 50px;
  border-radius: 50%;
  margin-bottom: 30px;
}

.congratulation-contents-title {
  font-size: 28px;
  line-height: 36px;
  margin: -6px 0 0;
}

.congratulation-contents-para {
  font-size: 16px;
  line-height: 24px;
  color: var(--paragraph-color);
  margin-top: 15px;
}

/* Profile Details Css */
.profile-border-top {
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
  margin-top: 20px;
  width: 100%;
}

.profile-border-bottom {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.profile-border-bottom:last-child {
  border-bottom: 0px;
  padding-bottom: 0px;
  margin-bottom: 0px;
}
.profile-wrapper-wishlist {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 18px;
  border-radius: 5px;
  border: 1px solid var(--border-color);
  color: var(--paragraph-color);
}

.profile-wrapper-wishlist.active {
  color: #f00;
  background-color: var(--border-color);
}

.profile-wrapper-item {
  background-color: #fff;
  padding: 24px;
}

.profile-wrapper-item:not(:first-child) {
  margin-top: 25px;
}

.profile-wrapper-item-browse-btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.profile-wrapper-item-browse-btn:hover {
  color: var(--main-color-one);
}

.profile-wrapper-item-viewAll {
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.profile-wrapper-item-viewAll:hover {
  color: var(--main-color-one);
}

.profile-wrapper-item-title {
  font-size: 20px;
  line-height: 28px;
  margin: -3px 0 0;
}

.profile-wrapper-item-plus {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  border: 1px solid #e3e3e3;
  font-size: 18px;
  cursor: pointer;
}

.profile-wrapper-item .single-project {
  padding: 0;
  border: unset;
  -webkit-box-shadow: unset;
  box-shadow: unset;
}

.profile-wrapper-item .single-project:hover {
  -webkit-box-shadow: unset;
  box-shadow: unset;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

.profile-wrapper-item-tab .tabs-two {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  background-color: var(--border-color);
  border-radius: 10px;
  padding: 5px;
}

.profile-wrapper-item-tab .tabs-two li {
  white-space: nowrap;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: var(--heading-color);
  padding: 10px 10px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-align: center;
  border-radius: 10px;
  font-size: 16px;
  line-height: 24px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .profile-wrapper-item-tab .tabs-two li {
    font-size: 15px;
  }
}

.profile-wrapper-item-tab .tabs-two li.active {
  background-color: #fff;
}

.profile-wrapper-author-thumb {
  width: 80px;
  height: 80px;
  border-radius: 50%;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .profile-wrapper-author-thumb {
    max-width: 50px;
  }
}

.profile-wrapper-author-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.profile-wrapper-switch-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--heading-color);
}

.profile-wrapper-details {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px 0;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.profile-wrapper-details-edit {
  font-size: 20px;
  color: var(--main-color-one);
  margin-left: 18px;
  display: inline-block;
  cursor: pointer;
}

.profile-wrapper-details-para {
  font-size: 14px;
  color: var(--paragraph-color);
}

.profile-wrapper-details-single {
  display: inline-block;
}

.profile-wrapper-details-single:not(:first-child) {
  border-left: 1px solid #e3e3e3;
  padding-left: 20px;
}

.profile-wrapper-details-single:not(:last-child) {
  padding-right: 20px;
}

.profile-wrapper-details-single-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
}

.profile-wrapper-details-single-icon {
  font-size: 15px;
}

.profile-wrapper-details-single-title {
  font-size: 16px;
  font-weight: 700;
}

.profile-wrapper-details-single-price {
  font-size: 24px;
  line-height: 32px;
  color: var(--main-color-one);
}

.profile-wrapper-details-single-price sub {
  font-size: 14px;
  margin: 0;
  bottom: 0;
  padding: 0px 10px 2px;
  border: 1px solid var(--secondary-color);
  color: var(--secondary-color);
  line-height: 16px;
  border-radius: 5px;
}

.profile-wrapper-details-single-flag {
  max-width: 35px;
}

.profile-wrapper-details-single-flag img {
  border-radius: 50%;
}

.profile-wrapper-details-single-thumb {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  border-radius: 50%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.profile-wrapper-details-single-contents-title {
  font-size: 20px;
  font-weight: 600;
}

.profile-wrapper-details-single-contents-para {
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
  margin-top: 7px;
}

.profile-wrapper-details-single:nth-child(3n+2) .profile-wrapper-details-single-thumb {
  background-color: rgba(0, 200, 151, 0.1);
}

.profile-wrapper-details-single:nth-child(3n+3) .profile-wrapper-details-single-thumb {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
}

.profile-wrapper-about-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  margin: -4px 0 0;
}

.profile-wrapper-about-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
}

/* Profile Details Widget */
.profile-details-widget-single {
  background-color: #fff;
  padding: 20px;
}

.profile-details-widget-single:not(:last-child) {
  margin-bottom: 25px;
}

.profile-details-widget-single-bottom-para {
  font-size: 14px;
  color: var(--paragraph-color);
}

.profile-details-widget-inner:not(:last-child) {
  border-bottom: 1px solid #EAECF0;
  padding-bottom: 20px;
  margin-bottom: 20px;
}
.rating_profile_details-para {
  font-weight: 700;
  font-size: 16px;
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
  padding: 2px 10px;
  line-height: 20px;
  border-radius: 3px;
}
.profile-details-widget-review-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 26px;
}

.profile-details-widget-review-images {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.profile-details-widget-review-images-item {
  max-width: 130px;
  overflow: hidden;
}

.profile-details-widget-review-images-item:hover img {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.profile-details-widget-review-images-item img {
  border-radius: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.profile-details-widget-portfolio-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.profile-details-widget-portfolio-col {
  width: calc(50% - 12px);
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .profile-details-widget-portfolio-col {
    width: calc(33.3333333333% - 16px);
  }
}

@media only screen and (max-width: 375px) {
  .profile-details-widget-portfolio-col {
    width: 100%;
  }
}

.profile-details-portfolio:hover .profile-details-portfolio-thumb img {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.profile-details-portfolio-thumb {
  overflow: hidden;
  max-width: 230px;
}

@media only screen and (max-width: 375px) {
  .profile-details-portfolio-thumb {
    max-width: 100%;
  }
}

.profile-details-portfolio-thumb img {
  border-radius: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

@media only screen and (max-width: 375px) {
  .profile-details-portfolio-thumb img {
    width: 100%;
  }
}

.profile-details-portfolio-content-title {
  font-size: 18px;
  font-weight: 700;
  margin: -2px 0 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.profile-details-portfolio-content-title:hover {
  color: var(--main-color-one);
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .profile-details-portfolio-content-title {
    font-size: 17px;
  }
}

.profile-details-portfolio-content-para {
  font-size: 14px;
  margin-top: 5px;
}

/* Create Project Css */
.create-project-wrap {
  padding: 0;
}

.create-project {
  padding-right: 0;
  padding: 30px;
  background: #fff;
  border-radius: 10px;
  max-width: 320px;
  flex-shrink: 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .create-project {
    max-width: 100%;
  }
}

.create-project-wrapper {
  background-color: #fff;
  padding: 24px;
  border-radius: 10px;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  overflow-x: auto;
}

.create-project-wrapper .setup-wrapper-contents {
  max-width: 100%;
}

.create-project-wrapper-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
  margin: -3px 0 0;
}

@media only screen and (max-width: 480px) {
  .create-project-wrapper-title {
    font-size: 24px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 375px) {
  .create-project-wrapper-title {
    font-size: 20px;
    line-height: 26px;
  }
}

.create-project-wrapper-skip {
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
  cursor: pointer;
}

.create-project-wrapper-skip:hover {
  color: var(--main-color-one);
}

.create-project-wrapper-item-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

.create-project-wrapper-upload-browse {
  position: relative;
  padding: 40px;
  border: 2px dashed #98A2B3;
  background-color: #F2F4F7;
}

.create-project-wrapper-upload-browse-icon {
  font-size: 40px;
  display: block;
}

.create-project-wrapper-upload-browse-para {
  font-size: 18px;
  font-weight: 500;
  color: var(--paragraph-color);
  margin-top: 20px;
}

.create-project-wrapper-upload-browse .upload-gallery {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  opacity: 0;
}

/* package */
.package-table {
  overflow-x: auto;
  scrollbar-color: #f3f3f3 var(--border-color);
  scrollbar-width: thin;
}

.package-table::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  border-radius: 10px;
  background-color: var(--border-color);
}

.package-table::-webkit-scrollbar-thumb {
  background-color: #f3f3f3;
  border-radius: 10px;
}

.package-table table {
  border: 1px solid #EAECF0;
}

.package-table table thead tr {
  vertical-align: middle;
  text-align: center;
}

.package-table table thead tr .package-head {
  padding: 10px;
  min-width: 190px;
}

.package-table table thead tr .package-head-flex {
  background-color: var(--section-bg-2);
  padding: 10px;
  border-radius: 7px;
}

.package-table table thead tr .package-head-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 28px;
}

@media only screen and (max-width: 480px) {
  .package-table table thead tr .package-head-title {
    font-size: 18px;
  }
}

.package-table table thead tr .package-head-edit {
  cursor: pointer;
  font-size: 20px;
}

.package-table table tbody {
  border-top: 0 !important;
}

.package-table table tbody tr {
  vertical-align: middle;
  text-align: center;
}

.package-table table tbody tr th {
  padding: 10px;
}

.package-table table tbody tr td {
  padding: 10px;
  position: relative;
}

.main_file_included,
.no_of_screen {
  height: 90px;
}

.package-head-title {
  font-size: 22px;
  font-weight: 600;
  line-height: 28px;
}

@media only screen and (max-width: 480px) {
  .package-head-title {
    font-size: 20px;
  }
}

.package-head-left-flex {
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 15px;
}

.package-head-left-edit {
  cursor: pointer;
  line-height: 1;
}

.package-head-left-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--heading-color);
}

.package-field {
  position: relative;
}

.package-button-wrapper {
  position: absolute;
  bottom: -15px;
  right: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.package-field-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 30px;
  width: 30px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  color: #fff;
  cursor: pointer;
  z-index: 5;
}

.package-field-icon.remove-icon {
  background-color: #fff;
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}

.package-field-icon.remove-mainFile {
  bottom: -15px;
}

.package-field .js_nice_select {
  height: 40px;
  line-height: 40px;
}

.package-field .js_nice_select::after {
  margin-top: -4px;
}

.package-field .js_nice_select .list .option {
  font-size: 14px;
}

.package-field-input .form--control {
  height: 40px;
  width: 100%;
  border: 1px solid #EAECF0;
  padding: 20px;
}

.package-field-checkbox .checkbox-inline {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.package-field-price-main-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--main-color-one);
}

.package-field-price-main-title s {
  font-size: 16px;
  color: var(--body-color);
  font-weight: 400;
}

.package-field-price-edit {
  cursor: pointer;
}

/* Project Preview */
.project-preview {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.project-preview-head-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
  margin: -3px 0 0;
}

.new_style .project-preview-head-title {
  font-size: 20px;
  line-height: 24px;
}

.project-preview-thumb img {
  border-radius: 10px;
}

.new_style .project-preview-thumb {
  box-shadow: 0px 10px 10px 0px #f6f6f6;
}

.project-preview-contents > p {
  word-break: break-word;
}

.project-preview-contents-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  margin: -3px 0 0;
}
@media screen and (max-width: 375px) {
  .project-preview-contents-title {
    font-size: 20px;
  }
}
.new_style .project-preview-contents-title {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
}

.project-preview-contents-para {
  font-size: 14px;
  line-height: 24px;
  margin-top: 10px;
  color: var(--paragraph-color);
}

/* Project Preview Pricing */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper {
    gap: 25px 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper {
    gap: 25px 0;
  }
}

.pricing-wrapper-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-left {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-left .pricing-wrapper-card-top {
    display: none;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-left .pricing-wrapper-card-top {
    display: none;
  }
}

.pricing-wrapper-left .pricing-wrapper-card::before {
  display: none;
}

.pricing-wrapper-left .pricing-wrapper-card-bottom {
  position: relative;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.pricing-wrapper-left .pricing-wrapper-card-bottom::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  height: 100%;
  width: 0%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing-wrapper-left .pricing-wrapper-card:hover .pricing-wrapper-card-bottom {
  border-radius: 0px;
  overflow: hidden;
}

.pricing-wrapper-left .pricing-wrapper-card:hover .pricing-wrapper-card-bottom::before {
  visibility: visible;
  opacity: 1;
  width: 100%;
}

.pricing-wrapper-right {
  -webkit-box-flex: 3;
  -ms-flex: 3;
  flex: 3;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-right {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    gap: 25px 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-right {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    gap: 25px 0;
  }
}

.pricing-wrapper-right .pricing-wrapper-card {
  border: 1px solid rgba(221, 221, 221, 0.4);
  border-left: 0;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-right .pricing-wrapper-card:nth-child(1) {
    border-left: 1px solid rgba(221, 221, 221, 0.4);
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-right .pricing-wrapper-card {
    border-left: 1px solid rgba(221, 221, 221, 0.4);
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .pricing-wrapper-right .pricing-wrapper-card:nth-child(3n+1) {
    border-right: unset;
  }
}

.pricing-wrapper-right .pricing-wrapper-card:nth-child(3n+1) .pricing-wrapper-card-top {
  border-left: 1px solid rgba(221, 221, 221, 0.4);
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-right .pricing-wrapper-card:nth-child(3n+1) .pricing-wrapper-card-top {
    border-left: unset;
  }
}

.pricing-wrapper-right .pricing-wrapper-card-bottom {
  border: 0;
  border-top: 1px solid rgba(221, 221, 221, 0.4);
  border-left: 0;
}

.pricing-wrapper-card {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  position: relative;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-card {
    gap: 25px 0;
  }
}

@media only screen and (max-width: 575.98px) {
  .pricing-wrapper-card {
    -ms-flex-preferred-size: 50%;
    flex-basis: 50%;
  }
}

@media only screen and (max-width: 480px) {
  .pricing-wrapper-card {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

.pricing-wrapper-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  height: 100%;
  width: 0%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing-wrapper-card:hover {
  color: #fff;
  border-radius: 20px;
  overflow: hidden;
  /* IE 9 */
  -webkit-transform: scaleY(1);
  /* Chrome, Safari, Opera */
  transform: scaleY(1);
}

.pricing-wrapper-card:hover::before {
  visibility: visible;
  opacity: 1;
  width: 100%;
}

.pricing-wrapper-card.active .pricing-wrapper-card-top {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.pricing-wrapper-card.active .pricing-wrapper-card-bottom {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.pricing-wrapper-card-top {
  padding: 20px;
  min-height: 70px;
}

.pricing-wrapper-card-top-prices {
  color: var(--heading-color);
  font-size: 16px;
  line-height: 24px;
  margin: -2px 0 0;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.pricing-wrapper-card-bottom {
  border: 1px solid rgba(221, 221, 221, 0.4);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}


.pricing-wrapper-card-bottom-list ul li {
  color: #585858;
  font-size: 16px;
  font-weight: 400;
  border-bottom: 1px solid rgba(221, 221, 221, 0.4);
  padding: 0px 10px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  min-height: 50px;
  display: flex;
  align-items: center;
  overflow-y: auto;
}
.text-center .pricing-wrapper-card-bottom-list ul li {
  justify-content: center;
}

.pricing-wrapper-card-bottom-list ul li:last-child {
  border: 0;
}

.pricing-wrapper-card-bottom-list ul li .price-main {
  font-size: 18px;
  font-weight: 600;
  color: var(--main-color-one);
  display: inline-block;
}

.pricing-wrapper-card-bottom-list ul li .price-old {
  font-size: 13px;
  color: var(--paragraph-color);
  line-height: 1;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 15px;
    padding: 0 10px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 15px;
  }
}

@media only screen and (max-width: 480px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 16px;
  }
}

@media only screen and (max-width: 375px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 15px;
  }
}

.pricing-wrapper-card-bottom-list ul li .check-icon {
  color: var(--main-color-one) !important;
}

/* Job Filter Css */
.sticky_top {
  position: sticky;
  top: 0;
  z-index: 8;
}

@media screen and (min-width: 992px) {
  .sticky_top_lg {
    position: sticky;
    top: 0;
    z-index: 8;
  }
}

.shop-contents-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .shop-contents-wrapper {
    display: block;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .shop-contents-wrapper.responsive-lg .shop-sidebar-content {
    width: 280px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .shop-contents-wrapper.responsive-lg {
    display: block;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .shop-contents-wrapper.responsive-lg .shop-sidebar-content {
    position: unset;
  }
}

.shop-contents-wrapper-right {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.shop-contents-wrapper .shop-sidebar-content {
  width: 330px;
  margin-right: 24px;
  position: sticky;
  top: 0;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .shop-contents-wrapper .shop-sidebar-content {
    width: 300px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .shop-contents-wrapper .shop-sidebar-content {
    width: 250px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .shop-contents-wrapper .shop-sidebar-content {
    width: 375px;
    position: unset;
  }
}

.single-shop-left {
  padding: 20px 20px;
  box-shadow: 0px 8px 10px #efefef;
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .single-shop-left {
    padding: 22px 20px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .single-shop-left {
    padding: 15px 20px 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .single-shop-left {
    padding: 15px 20px 20px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .single-shop-left {
    padding: 15px 20px 20px;
  }
}

.single-shop-left-inner {
  margin-top: 10px;
}

.single-shop-left-title.open .title::after {
  -webkit-transform: rotate(0);
  transform: rotate(0);
}

.single-shop-left-title .title {
  cursor: pointer;
  position: relative;
  font-size: 16px;
  line-height: 24px;
  font-weight: 700;
  margin: -2px 0 0;
}

.single-shop-left-title .title::after {
  content: "\f106";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 16px;
  position: absolute;
  right: 0;
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
  -webkit-transition: all 300ms;
  transition: all 300ms;
}

.single-shop-left-select {
  display: -ms-grid;
  display: grid;
}

.single-shop-left-list .item-search {
  position: relative;
  margin: 15px 0;
}

.single-shop-left-list .item-search .form--control {
  width: 100%;
  height: 50px;
  line-height: 50px;
  padding: 0 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--light-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  padding-right: 40px;
}

.single-shop-left-list .item-search .form--control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

@media only screen and (max-width: 480px) {
  .single-shop-left-list .item-search .form--control {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .single-shop-left-list .item-search .form--control {
    font-size: 14px;
  }
}

.single-shop-left-list .item-search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  font-size: 20px;
  cursor: pointer;
}

.single-shop-left-list .more-amenities {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  text-decoration: underline;
  margin-top: 15px;
  font-family: var(--heading-font);
}

.single-shop-left-list .item {
  font-size: 16px;
  line-height: 30px;
  position: relative;
  text-align: left;
  z-index: 2;
  padding: 5px 30px 5px 10px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border-radius: 5px;
  border: 1px solid var(--border-color);
}

.single-shop-left-list .item:not(:last-child) {
  margin-bottom: 10px;
}

.single-shop-left-list .item::before {
  content: "";
  position: absolute;
  height: 22px;
  width: 22px;
  border: 1px solid #ddd;
  right: 10px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  background: none;
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border-radius: 50%;
}

.single-shop-left-list .item a {
  display: block;
  padding: 7px 0;
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .single-shop-left-list .item a {
    padding: 5px 0;
  }
}

.single-shop-left-list .item.show>.submenu {
  display: block;
}

.single-shop-left-list .item.active {
  border-color: var(--main-color-one);
}

.single-shop-left-list .item.active::before {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  background: var(--main-color-one);
  border-color: var(--main-color-one);
}

.single-shop-left-list .item.active>a {
  color: var(--heading-color);
  font-weight: 600;
}

.single-shop-left-list .item:hover>a {
  color: var(--main-color-one);
}

.single-shop-left-list .item:hover::before {
  border-color: var(--main-color-one);
}

.single-shop-left-filter-title .title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
  font-size: 16px;
  font-weight: 700;
  color: var(--heading-color);
}

.single-shop-left-filter-reset {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  text-decoration: underline;
}

.shop-icon {
  display: none;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .shop-icon {
    display: block;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .shop-icon-sidebar {
    font-size: 24px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    width: 40px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: var(--main-color-one);
    -webkit-box-shadow: 0 0 10px rgba(221, 221, 221, 0.5);
    box-shadow: 0 0 10px rgba(221, 221, 221, 0.5);
    z-index: 95;
    color: #fff;
    margin-bottom: 20px;
  }
}

/* Responsive lg Device */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .responsive-overlay-lg {
    position: fixed;
    height: 100vh;
    width: 100%;
    top: 0;
    left: 100%;
    z-index: -1;
    visibility: hidden;
    opacity: 0;
    background: rgba(0, 0, 0, 0.5);
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
  }

  .responsive-overlay-lg.active {
    visibility: visible;
    opacity: 1;
    z-index: 99;
    left: 0;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .responsive-lg .shop-icon {
    display: block;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .responsive-lg .shop-icon-sidebar {
    font-size: 24px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    width: 40px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: var(--main-color-one);
    -webkit-box-shadow: 0 0 10px rgba(221, 221, 221, 0.5);
    box-shadow: 0 0 10px rgba(221, 221, 221, 0.5);
    z-index: 95;
    color: #fff;
    margin-bottom: 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .responsive-lg .shop-close-content {
    z-index: 1;
    position: absolute;
    left: -100%;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
    z-index: 99;
    background: #fff;
    width: 375px;
    display: block;
    padding: 20px;
    transition: all 0.5s;
  }

  .responsive-lg .shop-close-content.active {
    visibility: visible;
    opacity: 1;
    left: 0;
  }
}

.responsive-lg .shop-close-content-icon {
  visibility: hidden;
  opacity: 0;
  height: 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .responsive-lg .shop-close-content-icon {
    height: 40px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .responsive-lg .shop-close-content-icon {
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute;
    right: -40px;
    top: 0;
    font-size: 24px;
    color: #fff;
    background: var(--main-color-one);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    width: 40px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .shop-close-content {
    z-index: 1;
    position: absolute;
    left: -100%;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
    z-index: 99;
    background: #fff;
    width: 375px;
    display: block;
    padding: 20px;
    transition: all 0.5s;
  }

  .shop-close-content.active {
    visibility: visible;
    opacity: 1;
    left: 0;
  }
}

@media only screen and (min-width: 300px) and (max-width: 991.98px) and (max-width: 480px) {
  .shop-close-content {
    max-width: 320px;
  }
}

@media only screen and (min-width: 300px) and (max-width: 991.98px) and (max-width: 375px) {
  .shop-close-content {
    max-width: 270px;
  }
}

.shop-close-content-icon {
  visibility: hidden;
  opacity: 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .shop-close-content-icon {
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute;
    right: -40px;
    top: 0;
    font-size: 24px;
    color: #fff;
    background: var(--main-color-one);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    width: 40px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
  }
}

@media only screen and (min-width: 300px) and (max-width: 991.98px) and (max-width: 375px) {
  .shop-close-content-icon {
    height: 28px;
    width: 28px;
    font-size: 20px;
    right: -28px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .responsive-overlay {
    position: fixed;
    height: 100vh;
    width: 100%;
    top: 0;
    left: 100%;
    z-index: -1;
    visibility: hidden;
    opacity: 0;
    background: rgba(0, 0, 0, 0.5);
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
  }

  .responsive-overlay.active {
    visibility: visible;
    opacity: 1;
    z-index: 99;
    left: 0;
  }
}

/* Job Filter Right */
.jobFilter-wrapper-search {
  background-color: #fff;
  position: relative;
  width: 100%;
  border-radius: 7px;
  margin-bottom: 24px;
}

.jobFilter-wrapper-search .form--control {
  height: 60px;
  width: 100%;
  border: 1px solid #e9e9e9;
  padding: 0 100px 0 20px;
  border-radius: 7px;
}

.jobFilter-wrapper-search-btn {
  position: absolute;
  right: 5px;
  top: 5px;
  bottom: 0;
  height: calc(100% - 10px);
  padding: 0px 20px;
  font-size: 16px;
  background-color: var(--main-color-one);
  color: #fff;
  outline: none;
  border: 0;
  border-radius: 7px;
}

.jobFilter-wrapper-item {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 10px 10px #eee;
  overflow: hidden;
}

.jobFilter-wrapper-item.jobDetails-padding {
  padding: 0;
}
 .jobFilter-wrapper-item-para > span {
   font-weight: 500;
   color: var(--main-color-one);
 }
.jobFilter-wrapper-item.jobDetails-padding .jobFilter-wrapper-item-inner {
  padding: 20px;
}

.jobFilter-wrapper-item.jobDetails-padding .jobFilter-wrapper-item-bottom {
  background-color: #F9F9FB;
  padding: 20px;
}

.jobFilter-wrapper-item:not(:last-child) {
  margin-bottom: 24px;
}

.jobFilter-wrapper-item-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 20px 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.jobFilter-wrapper-item-top-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.jobFilter-wrapper-item-top-right-image {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  background-color: #f3f3f3;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.jobFilter-wrapper-item-top-right-image svg path {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.jobFilter-wrapper-item-top-right-image:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.jobFilter-wrapper-item-top-right-image:hover svg {
  fill: #fff;
}

.jobFilter-wrapper-item-top-right-image:hover svg path {
  fill: #fff;
}

.jobFilter-wrapper-item-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
  margin: -3px 0 0;
}

@media only screen and (max-width: 480px) {
  .jobFilter-wrapper-item-title {
    font-size: 22px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 375px) {
  .jobFilter-wrapper-item-title {
    font-size: 20px;
  }
}

.jobFilter-wrapper-item-para {
  font-size: 16px;
  margin-top: 10px;
  color: var(--paragraph-color);
}

.jobFilter-wrapper-item-contents-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  color: var(--main-color-one);
  font-weight: 600;
}

.jobFilter-wrapper-item-contents-fixed {
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  border-radius: 30px;
  padding: 2px 12px 3px 12px;
}

.jobFilter-wrapper-item-contents-review {
  font-size: 16px;
  color: var(--paragraph-color);
}

.jobFilter-wrapper-item-contents-review i {
  font-size: 18px;
  color: #ffac16;
  margin-right: 5px;
}

.jobFilter-wrapper-item-contents-tag {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin-bottom: unset;
}

.jobFilter-wrapper-item-contents-tag-list {
  font-size: 16px;
  color: var(--heading-color);
  padding: 5px 15px;
  border-radius: 30px;
  background-color: #eeeeee;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.jobFilter-wrapper-item-contents-tag-list:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.jobFilter-wrapper-item-bottom-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px 30px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
}

.jobFilter-wrapper-item-bottom-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
  font-size: 16px;
  color: var(--paragraph-color);
}

.jobFilter-wrapper-item-bottom-list-item .item-icon {
  font-size: 18px;
  color: var(--paragraph-color);
}

.jobFilter-wrapper-item-bottom-list-item .item-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 25px;
}

.jobFilter-wrapper-item-bottom-list-item .item-para strong {
  color: var(--heading-color);
}

.jobFilter-wrapper-item-tickets-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 20px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.jobFilter-wrapper-item-tickets-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 65px;
  width: 65px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 50%;
}

.jobFilter-wrapper-item-tickets-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.jobFilter-wrapper-item-tickets-icon:hover svg path {
  fill: #fff;
}

.jobFilter-wrapper-item-tickets-icon svg path {
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: var(--main-color-one);
}

.jobFilter-wrapper-item-tickets-contents-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--main-color-one);
}

.jobFilter-wrapper-item-tickets-contents-title sub {
  font-size: 16px;
  bottom: 0;
}

.jobFilter-wrapper-item-completed {
  font-size: 16px;
  font-weight: 700;
}

.jobFilter-wrapper-list {
  padding: 10px 15px;
  background-color: var(--border-color);
  margin: 0;
  list-style: none;
  border-radius: 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.jobFilter-wrapper-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
  font-weight: 400;
  position: relative;
}

.jobFilter-wrapper-list-item:not(:last-child) {
  margin-right: 20px;
  padding-right: 20px;
}

.jobFilter-wrapper-list-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  height: 70%;
  width: 1px;
  background-color: var(--paragraph-color);
}

.jobFilter-wrapper-list-item-icon {
  font-size: 18px;
  color: var(--paragraph-color);
}

.jobFilter-wrapper-list-item-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
  font-weight: 400;
}

.jobFilter-wrapper-list-item-para strong {
  font-weight: 600;
  color: var(--heading-color);
}

.jobFilter-about-clients:not(:last-child) {
  border-bottom: 1px solid #EAECF0;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.jobFilter-about-clients-single {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 15px 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.jobFilter-about-clients-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.jobFilter-about-clients-icon {
  font-size: 20px;
  color: var(--paragraph-color);
}

.jobFilter-about-clients-icon svg,
.jobFilter-about-clients-icon img {
  max-width: 18px;
  margin-bottom: 2px;
}
.jobFilter-about-clients-icon svg path {
  fill: var(--main-color-one);
}

.jobFilter-about-clients-thumb {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  background: var(--border-color);
}

.jobFilter-about-clients-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}
 .jobFilter-about-clients-thumb-title {
   font-size: 16px;
   font-weight: 700;
   line-height: 26px;
   color: var(--heading-color);
 }
.jobFilter-about-clients-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
}

/* Left Right List */
.leftRight-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.leftRight-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.leftRight-list-item:not(:first-child) {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.leftRight-list-item.price .leftRight-list-item-left {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

.leftRight-list-item.price .leftRight-list-item-right {
  font-size: 24px;
  font-weight: 600;
  color: var(--main-color-one);
}

.leftRight-list-item.price .leftRight-list-item-right .old-price {
  color: var(--body-color);
  font-weight: 400;
  font-size: 20px;
}

.leftRight-list-item.not-included .leftRight-list-item-left {
  color: var(--body-color);
}

.leftRight-list-item.not-included .leftRight-list-item-left.check-icon::before {
  content: "\f00d";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  color: var(--body-color);
}

.leftRight-list-item.not-included .leftRight-list-item-right {
  color: var(--body-color);
}

.leftRight-list-item-left {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

.leftRight-list-item-left.check-icon::before {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 16px;
  color: var(--success-color);
}

.leftRight-list-item-right {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--heading-color);
}

/* Proposals css */
.jobFilter-proposal-author-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 20px 15px;
}

.jobFilter-proposal-author-thumb {
  width: 70px;
  height: 70px;
  border-radius: 50%;
}

.jobFilter-proposal-author-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.jobFilter-proposal-author-contents-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.jobFilter-proposal-author-contents-subtitle {
  font-size: 14px;
  line-height: 24px;
  margin-top: 7px;
}

.jobFilter-proposal-author-contents-subtitle span {
  color: var(--heading-color);
}

.jobFilter-proposal-author-contents-review {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
}

.jobFilter-proposal-author-contents-review-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  background-color: rgba(255, 172, 22, 0.1);
  color: #ffac16;
  padding: 3px 7px;
  border-radius: 5px;
}

.jobFilter-proposal-author-contents-review-para {
  color: #ffac16;
}

.jobFilter-proposal-author-contents-jobs {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 3px 10px;
  border: 1px solid var(--border-color);
  color: var(--paragraph-color);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  border-radius: 3px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.jobFilter-proposal-author-contents-jobs:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.jobFilter-proposal-offered-single {
  display: inline-block;
  position: relative;
}

.jobFilter-proposal-offered-single:not(:last-child) {
  padding-right: 15px;
  margin-right: 15px;
}

.jobFilter-proposal-offered-single:not(:last-child)::after {
  content: "";
  position: absolute;
  height: 70%;
  width: 1px;
  background-color: #ddd;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
}

.jobFilter-proposal-offered-single .offered {
  font-size: 15px;
  color: var(--paragraph-color);
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

.jobFilter-proposal-offered-single .offered-price {
  font-size: 18px;
  font-weight: 600;
  color: var(--main-color-one);
}

.jobFilter-proposal-offered-single .offered-days {
  font-size: 15px;
  font-weight: 600;
  color: var(--heading-color);
}

/* Activities */
.jobFilter-activities-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.jobFilter-activities-list-item {
  padding-left: 40px;
  position: relative;
}

.jobFilter-activities-list-item::before {
  content: "";
  position: absolute;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: #ddd;
  top: 5px;
  left: 0;
  z-index: 1;
}

.jobFilter-activities-list-item:first-child::before {
  background-color: var(--main-color-one);
}

.jobFilter-activities-list-item:not(:last-child) {
  padding-bottom: 30px;
}

.jobFilter-activities-list-item:not(:last-child)::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 2px;
  top: 7px;
  left: 4px;
  background-color: #ddd;
  z-index: 0;
}

.jobFilter-activities-list-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--paragraph-color);
  display: flex;
  align-items: center;
  gap: 7px;
}

.jobFilter-activities-list-title strong {
  color: var(--heading-color);
  font-weight: 700;
}

.jobFilter-activities-list-para {
  font-size: 14px;
  font-weight: 400;
  color: var(--light-color);
  line-height: 24px;
  margin-top: 7px;
}

/* My Jobs Css */
.job-progress {
  display: inline-block;
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
  border: 1px solid transparent;
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  padding: 4px 15px;
  border-radius: 20px;
  font-size: 14px;
  line-height: 20px;
}

.job-progress:hover {
  background-color: var(--success-color);
  color: #fff;
}

.job-progress.cancel {
  background-color: rgba(var(--danger-color-rgb), 0.1);
  color: var(--danger-color);
}

.job-progress.active {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  border-color: transparent;
  color: var(--main-color-one);
}

.job-progress.active:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.job-progress.active-outline {
  background-color: unset;
  border-color: var(--main-color-one);
  color: var(--main-color-one);
}

.job-progress.active-outline:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.milestone-approved {
  display: inline-block;
  padding: 3px 15px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.milestone-approved:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.milestone-approved.completed {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.milestone-approved.completed:hover {
  background-color: var(--success-color);
  color: #fff;
}

.myJob-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 24px;
  position: relative;
}

.myJob-wrapper-single {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  position: relative;
}

.myJob-wrapper-single-hired {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 4px 10px;
  border: 1px solid var(--border-color);
  border-radius: 30px;
  font-size: 14px;
  font-weight: 400;
  color: var(--paragraph-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
  margin-bottom: 10px;
}

.myJob-wrapper-single-hired:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.myJob-wrapper-single-hired:hover.hired {
  color: #fff;
}

.myJob-wrapper-single-hired:hover.hired::before {
  background-color: #fff;
}

.myJob-wrapper-single-hired.hired {
  border-color: var(--main-color-one);
  color: var(--main-color-one);
  position: relative;
  padding-left: 25px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myJob-wrapper-single-hired.hired::before {
  content: "";
  position: absolute;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  left: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.shortlisted-item {
  display: inline-block;
  padding: 1px 10px 3px 10px;
  border: 1px solid #d3d3d3;
  color: var(--body-color);
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  position: relative;
  border-radius: 30px;
  color: var(--main-color-one);
  border: 1px solid var(--main-color-one);
}

.shortlisted-item.hired {
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
}
.shortlisted-item.seen {
  color: var(--success-color);
  border: 1px solid var(--success-color);
}
.shortlisted-item.not_seen {
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}
.job-proposal-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
.myJob-wrapper-single-match {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 4px 20px;
  background-color: var(--main-color-one);
  color: #fff;
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  border-radius: 5px;
  position: relative;
  top: -35px;
}

.myJob-wrapper-single-match::after {
  content: "";
  position: absolute;
  right: -20px;
  top: 0px;
  right: -16px;
  width: 0;
  height: 0;
  border-top: 15px solid transparent;
  border-left: 20px solid rgba(var(--main-color-one-rgb), 0.9);
  border-bottom-right-radius: 3px;
}

.myJob-wrapper-single-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 24px;
}

@media only screen and (max-width: 575.98px) {
  .myJob-wrapper-single-title {
    font-size: 20px;
  }
}

@media only screen and (max-width: 375px) {
  .myJob-wrapper-single-title {
    font-size: 18px;
  }
}

.myJob-wrapper-single-contents {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
}

@media only screen and (max-width: 375px) {
  .myJob-wrapper-single-contents {
    -ms-flex-preferred-size: unset;
    flex-basis: unset;
  }
}

.myJob-wrapper-single-contents-item:not(:first-child) {
  margin-top: 15px;
}

.myJob-wrapper-single-contents-para {
  color: var(--paragraph-color);
  font-size: 16px;
  line-height: 24px;
}

.myJob-wrapper-single-id {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
}

.myJob-wrapper-single-fixed {
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  border-radius: 30px;
  border-radius: 30px;
  padding: 3px 10px;
  display: inline-block;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myJob-wrapper-single-fixed:hover {
  color: var(--secondary-color);
}

.myJob-wrapper-single-fixed.completed {
  border-color: var(--success-color);
  color: var(--success-color);
}

.myJob-wrapper-single-fixed.completed:hover {
  color: var(--success-color);
}

.myJob-wrapper-single-fixed.active {
  border-color: var(--main-color-one);
  color: var(--main-color-one);
}

.myJob-wrapper-single-fixed.active:hover {
  color: var(--main-color-one);
}

.myJob-wrapper-single-fixed.not-started {
  border-color: var(--border-color);
  color: var(--paragraph-color);
}

.myJob-wrapper-single-fixed.not-started:hover {
  color: var(--paragraph-color);
}
.myJob-wrapper-single-fixed.closed {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.myJob-wrapper-single-fixed.closed:hover {
  color: var(--danger-color);
}

.myJob-wrapper-single-date {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.myJob-wrapper-single-date strong {
  font-weight: 600;
  color: var(--heading-color);
}

.myJob-wrapper-single-arrow-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myJob-wrapper-single-arrow-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

.myJob-wrapper-single-balance {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  padding: 10px;
  border-radius: 10px;
}

.myJob-wrapper-single-balance-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.myJob-wrapper-single-balance-price {
  font-size: 24px;
  line-height: 24px;
  font-weight: 600;
}

.myJob-wrapper-single-balance-para {
  color: var(--paragraph-color);
  font-size: 16px;
  margin-top: 7px;
}

.myJob-wrapper-single-balance-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 25px;
  width: 25px;
  font-size: 15px;
  border: 1px solid #bdbdbd;
  border-radius: 50%;
  font-size: 12px;
}

.myJob-wrapper-single-right-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 20px 10px;
}

.myJob-wrapper-single-right-elipsis {
  font-size: 18px;
  padding: 5px;
  cursor: pointer;
}

.myJob-wrapper-single-item {
  display: inline-block;
  position: relative;
}

.myJob-wrapper-single-item:not(:last-child) {
  padding-right: 30px;
  margin-right: 30px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .myJob-wrapper-single-item:not(:last-child) {
    padding-right: 20px;
    margin-right: 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .myJob-wrapper-single-item:not(:last-child) {
    padding-right: 20px;
    margin-right: 20px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .myJob-wrapper-single-item:not(:last-child) {
    padding-right: 20px;
    margin-right: 20px;
  }
}

@media only screen and (max-width: 480px) {
  .myJob-wrapper-single-item:not(:last-child) {
    padding-right: 15px;
    margin-right: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .myJob-wrapper-single-item:not(:last-child) {
    padding-right: 10px;
    margin-right: 10px;
  }
}

.myJob-wrapper-single-item:not(:last-child)::after {
  content: "";
  position: absolute;
  height: 70%;
  width: 1px;
  right: 0;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  background-color: #ddd;
}

.myJob-wrapper-single-item-para {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .myJob-wrapper-single-item-para {
    font-size: 16px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .myJob-wrapper-single-item-para {
    font-size: 16px;
  }
}

@media only screen and (max-width: 375px) {
  .myJob-wrapper-single-item-para {
    font-size: 15px;
  }
}

.myJob-wrapper-single-item-small {
  font-size: 14px;
  font-weight: 500;
  color: var(--paragraph-color);
  margin-right: 5px;
}

.myJob-wrapper-single-elipsis {
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 16px;
  color: var(--paragraph-color);
  cursor: pointer;
}

.myJob-wrapper-single-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.myJob-wrapper-single-list .js_nice_select {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: auto;
  height: 30px;
  line-height: 30px;
  border: 1px solid var(--border-color);
  border-radius: 30px;
}

.myJob-wrapper-single-list .js_nice_select::after {
  margin-top: -4px;
}

.myJob-wrapper-single-list-para {
  font-size: 16px;
  color: var(--paragraph-color);
  font-weight: 400;
}

.myJob-wrapper-single-list-para a {
  color: var(--main-color-one);
  font-weight: 500;
}

.myJob-wrapper-single-milestone-item {
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 15px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myJob-wrapper-single-milestone-item:not(:first-child) {
  margin-top: 24px;
}

.myJob-wrapper-single-milestone-item.remove .remove-milestone-contractor {
  visibility: visible;
  opacity: 1;
  display: block;
}

.myJob-wrapper-single-milestone-item .remove-milestone-contractor {
  visibility: hidden;
  opacity: 0;
  display: none;
}
div[class*=col]:nth-child(4n+1) .myJob-wrapper-single-balance {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

div[class*=col]:nth-child(4n+2) .myJob-wrapper-single-balance {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
}

div[class*=col]:nth-child(4n+3) .myJob-wrapper-single-balance {
  background-color: rgba(var(--success-color-rgb), 0.1);
}

div[class*=col]:nth-child(4n+4) .myJob-wrapper-single-balance {
  background-color: rgba(185, 131, 255, 0.1);
}

/* Chat Inbox Css */
.chat-wrapper {
  background-color: #fff;
  border-radius: 10px;
}

.chat-wrapper-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .chat-wrapper-flex {
    display: -ms-grid;
    display: grid;
  }
}

.chat-wrapper-contact {
  padding: 20px;
  max-height: calc(100vh - 10px);
  overflow-y: auto;
  scrollbar-color: var(--main-color-one) #ddd;
  scrollbar-width: thin;
  position: relative;
  flex-shrink: 0;
  width: 500px;
}

@media screen and (min-width: 992px) and (max-width: 1199.98px) {
  .chat-wrapper-contact {
    width: 400px;
  }
}

.chat-wrapper-contact::-webkit-scrollbar {
  width: 5px;
  background-color: #ddd;
  border-radius: 20px;
}

.chat-wrapper-contact::-webkit-scrollbar-thumb {
  background: var(--main-color-one);
  border-radius: 20px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .chat-wrapper-contact {
    padding: 0;
  }
}
@media (min-width: 300px) and (max-width: 575px) {
  .chat-wrapper-contact {
    max-width: 100%;
    width: auto;
  }
}

.chat-wrapper-contact-close.active {
  visibility: visible;
  opacity: 1;
  left: 0;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .chat-wrapper-contact-close {
    position: fixed;
    left: -100%;
    top: 0;
    background: #fff;
    z-index: 99;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    height: 100vh;
    width: 400px;
  }
}

@media only screen and (max-width: 480px) {
  .chat-wrapper-contact-close {
    width: 300px;
  }
}

@media only screen and (max-width: 375px) {
  .chat-wrapper-contact-close {
    width: 275px;
  }
}

.chat-wrapper-contact-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .chat-wrapper-contact-list {
    padding: 20px;
    height: calc(100vh - 0px);
    overflow-y: auto;
    scrollbar-width: thin;
    position: relative;
  }

  .chat-wrapper-contact-list::-webkit-scrollbar {
    width: 5px;
    background-color: #ddd;
    border-radius: 20px;
  }

  .chat-wrapper-contact-list::-webkit-scrollbar-thumb {
    background: var(--main-color-one);
    border-radius: 20px;
  }
}

.chat-wrapper-contact-list-item {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.chat-wrapper-contact-list-item:not(:first-child) {
  padding-top: 20px;
}

.chat-wrapper-contact-list-item:not(:last-child) {
  border-bottom: 1px solid #F5F6F9;
  padding-bottom: 20px;
}

.chat-wrapper-contact-list-item.active {
  background-color: #F5F6F9;
  padding: 10px;
  border-radius: 10px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .chat-wrapper-contact-list-item.active {
    padding: 10px;
  }
}

.chat-wrapper-contact-list-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.chat-wrapper-details-header {
  width: 100%;
}
.chat-wrapper-details-header.profile-border-bottom {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}
.chat-wrapper-details-header.flex-between {
  gap: 15px 10px;
}
.chat-wrapper-details-header-left {
  max-width: 320px;
}

.chat-wrapper-details-header-left-author {}

.chat-wrapper-contact-list-thumb {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  position: relative;
}
.chat-wrapper-contact-list-thumb-main.chat-wrapper-contact-list-thumb {
  height: 40px;
  width: 40px;
}
.chat-wrapper-contact-list-thumb img {
  height: 100%;
  width: 100%;
  border-radius: 50%;
  object-fit: cover;
}

@media only screen and (max-width: 480px) {
  .chat-wrapper-contact-list-thumb {
     width: 40px;
     height: 40px;
  }
}

.chat-wrapper-contact-list-thumb .notification-dots {
  position: absolute;
  height: 12px;
  width: 12px;
  background-color: #ddd;
  border-radius: 50%;
  bottom: 5px;
  right: 0;
  z-index: 2;
  border: 2px solid #fff;
  -webkit-box-shadow: 0 0 10px #cacaca;
  box-shadow: 0 0 10px #cacaca;
}

.chat-wrapper-contact-list-thumb .notification-dots.active {
  background-color: var(--success-color);
}

.chat-wrapper-contact-list-thumb img {
  border-radius: 50%;
}

.chat-wrapper-contact-list-contents {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.chat-wrapper-contact-list-contents-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
  margin: -2px 0 0;
}

.chat-wrapper-contact-list-contents-para {
  font-size: 14px;
  line-height: 24px;
  color: var(--paragraph-color);
}

.chat-wrapper-contact-list-contents-link {
  color: var(--main-color-one);
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.chat-wrapper-contact-list-contents-link:hover {
  color: var(--main-color-one);
  text-decoration: underline;
}

.chat-wrapper-contact-list-time {
  font-size: 14px;
  line-height: 24px;
  color: var(--paragraph-color);
}

.chat-wrapper-details {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  padding: 10px 10px;
  border-left: 1px solid #F5F6F9;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  /*max-height: calc(100vh - 100px);*/
}
@media screen and (max-width: 991.98px) {
  .chat-wrapper-details {
    height: unset;
    /*max-height: calc(100vh - 140px);*/
  }
}
.chat-wrapper-details-header-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--main-color-one);
}

.chat-wrapper-details-header-subtitle {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  margin-top: 5px;
}

.chat-wrapper-details-header-right .btn-report {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border: 1px solid #eee;
  font-size: 15px;
  color: var(--paragraph-color);
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.chat-wrapper-details-header-right .btn-report:hover {
  background-color: red;
  color: #fff;
}

.chat-wrapper-details-inner {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  min-height: 470px;
  max-height: 470px;
  overflow-y: auto;
  scrollbar-color: var(--body-color) var(--border-color);
  scrollbar-width: thin;
  scrollbar-width: thin;
  padding: 10px;
  min-width: 400px;
}
@media only screen and (max-width: 991.98px) {
  .chat-wrapper-details-inner {
    padding: 10px;
    max-width: 100%;
    min-width: auto;
    padding: 10px 5px;
    max-height: 470px;
  }
}
@media only screen and (max-width: 575px) {
  .chat-wrapper-details-inner {
    padding: 10px;
    max-width: 100%;
    min-width: auto;
    padding: 10px 5px;
    max-height: 470px;
  }
}
.chat-wrapper-details-inner::-webkit-scrollbar {
  width: 5px;
  background-color: #ddd;
  border-radius: 20px;
}

.chat-wrapper-details-inner::-webkit-scrollbar-thumb {
  background: var(--main-color-one);
  border-radius: 20px;
}

.chat-wrapper-details-inner-chat {
  padding: 0 5px;
}

.chat-wrapper-details-inner-chat:not(:last-child) {
  margin-bottom: 20px;
}
.chat-wrapper-details-footer.profile-border-top {
  margin-top: 10px;
  padding-top: 10px;
}
.chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-flex {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
}

.chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-contents {
  text-align: right;
}
.chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-contents-para {
  display: inline-block;
}
 .chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-contents-para-span {
  border-radius: 10px 0px 10px 10px;
  background-color: #F5F6F9;
  color: var(--heading-color);
}


.chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-contents-time {
  text-align: right;
}

.chat-wrapper-details-inner-chat-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px 10px;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.chat-wrapper-details-inner-chat-thumb {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.chat-wrapper-details-inner-chat-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.chat-wrapper-details-inner-chat-contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.chat-wrapper-details-inner-chat-contents-para {
  display: inline-block;
}
 .chat-wrapper-details-inner-chat-contents-para-span {
   background-color: var(--main-color-one);
   padding: 7px 15px;
   border-radius: 0 10px 10px 10px;
   color: #fff;
   font-size: 16px;
   line-height: 22px;
   font-weight: 500;
   display: inline-block;
   word-break: break-all;
 }

@media only screen and (max-width: 575.98px) {
  .chat-wrapper-details-inner-chat-contents-para {
    font-size: 15px;
    line-height: 24px;
    font-weight: 400;
  }
}

@media only screen and (max-width: 480px) {
  .chat-wrapper-details-inner-chat-contents-para {
    padding: 10px;
    font-size: 14px;
    line-height: 24px;
    font-weight: 400;
  }
}

.chat-wrapper-details-inner-chat-contents-time {
  font-size: 16px;
  color: var(--paragraph-color);
  font-weight: 500;
  display: block;
}

@media only screen and (max-width: 575.98px) {
  .chat-wrapper-details-inner-chat-contents-time {
    font-size: 15px;
    line-height: 24px;
    font-weight: 400;
  }
}

@media only screen and (max-width: 480px) {
  .chat-wrapper-details-inner-chat-contents-time {
    padding: 10px;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
  }
}

.chat-wrapper-details-inner-chat-offer {
  border: 1px solid var(--border-color);
  padding: 20px;
  border-radius: 5px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media only screen and (max-width: 375px) {
  .chat-wrapper-details-inner-chat-offer {
    padding: 10px;
  }
}

.chat-wrapper-details-inner-chat-offer-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 28px;
}

@media only screen and (max-width: 480px) {
  .chat-wrapper-details-inner-chat-offer-title {
    font-size: 22px;
  }
}

.chat-wrapper-details-inner-chat-offer-para {
  font-size: 16px;
  line-height: 24px;
  color: var(--paragraph-color);
}

@media only screen and (max-width: 480px) {
  .chat-wrapper-details-inner-chat-offer-para {
    font-size: 15px;
  }
}

.chat-wrapper-details-inner-chat-offer-contents-list {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  gap: 15px 0;
}

.chat-wrapper-details-inner-chat-offer-contents-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--paragraph-color);
  position: relative;
  word-break: break-word;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media only screen and (max-width: 575.98px) {
  .chat-wrapper-details-inner-chat-offer-contents-list-item {
    font-size: 15px;
    font-weight: 400;
  }
}

@media only screen and (max-width: 375px) {
  .chat-wrapper-details-inner-chat-offer-contents-list-item {
    font-size: 14px;
  }
}

.chat-wrapper-details-inner-chat-offer-contents-list-item:not(:last-child) {
  margin-right: 24px;
  padding-right: 24px;
}

.chat-wrapper-details-inner-chat-offer-contents-list-item:not(:last-child)::after {
  content: "";
  position: absolute;
  height: 70%;
  width: 2px;
  background-color: var(--border-color);
  right: -1px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
}

.chat-wrapper-details-inner-chat-offer-contents-list-item strong {
  font-weight: 600;
  font-size: 20px;
  color: var(--heading-color);
}

@media only screen and (max-width: 480px) {
  .chat-wrapper-details-inner-chat-offer-contents-list-item strong {
    font-size: 18px;
  }
}

@media only screen and (max-width: 375px) {
  .chat-wrapper-details-inner-chat-offer-contents-list-item strong {
    font-size: 16px;
  }
}

.chat-wrapper-details-inner-chat-offer-contents-list-item strong.color-one {
  color: var(--main-color-one);
}
 .chat-wrapper-details-footer-btn-right {
   text-align: right;
   margin-top: 3px;
 }
.milestone-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.milestone-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  background-color: var(--border-color);
  padding: 10px;
  border-radius: 5px;
}

.milestone-list-item:not(:first-child) {
  margin-top: 10px;
}

.milestone-list-item-para {
  font-size: 15px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.milestone-list-item-price {
  font-size: 20px;
  font-weight: 600;
  color: var(--main-color-one);
}

.milestone-list-item-btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 3px 15px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 30px;
  font-size: 15px;
  font-weight: 500;
}

.milestone-list-item-btn.funded {
  background-color: var(--main-color-one);
  color: #fff;
}

.milestone-list-item.funded {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.milestone-list-item.funded .milestone-list-item-btn {
  background-color: var(--main-color-one);
  color: #fff;
}

/* responsive sidebar */
@media (min-width: 300px) and (max-width: 991.98px) {
  .chat-sidebar {
    font-size: 24px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    width: 40px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: var(--main-color-one);
    -webkit-box-shadow: 0 0 10px rgba(221, 221, 221, 0.5);
    box-shadow: 0 0 10px rgba(221, 221, 221, 0.5);
    z-index: 95;
    color: #fff;
    margin: 20px 0 0 20px;
    border-radius: 7px;
  }
  .chat-sidebar.chatText {
    width: unset;
    height: unset;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    padding: 5px 15px;
    margin: 20px;
  }
}

.close-chat {
  position: absolute;
  right: -40px;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  background: #fff;
  color: #dd0000;
  font-size: 20px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.close-chat:hover {
  background-color: #dd0000;
  color: #fff;
}

.offer-remove-icon {
  display: none;
}

.offer-remove .offer-remove-icon {
  display: block;
}

/* Refund Request css */
.padding-20 {
  padding: 20px;
}

.single-refundRequest {
  background-color: #fff;
}

.single-refundRequest-header-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.single-refundRequest-header-para strong {
  color: var(--heading-color);
}

.single-refundRequest-item:not(:last-child) {
  border-bottom: 1px solid #ddd;
  padding-bottom: 25px;
  margin-bottom: 25px;
}

.single-refundRequest-item-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

.single-refundRequest-item-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--heading-color);
}

.single-refundRequest-item-price {
  font-size: 24px;
  font-weight: 600;
  color: var(--main-color-one);
}

.single-refundRequest-item-form-single:not(:last-child) {
  margin-bottom: 20px;
}

.single-refundRequest-item-form-single label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.single-refundRequest-item-form-single .form--control {
  height: 55px;
  width: 100%;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 7px;
  font-size: 15px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: var(--paragraph-color);
}

.single-refundRequest-item-form-single .form--control:focus {
  -webkit-box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
  border-color: rgba(var(--main-color-one-rgb), 0.4);
}

.single-refundRequest-item-form-single textarea {
  width: 100%;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 7px;
  font-size: 15px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: var(--paragraph-color);
}

.single-refundRequest-item-form-single textarea:focus {
  -webkit-box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
  border-color: rgba(var(--main-color-one-rgb), 0.4);
}

.single-refundRequest-item-uploads {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  text-decoration: underline;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  display: block;
}

.single-refundRequest-item-uploads:hover {
  color: var(--main-color-one);
}

.single-refundRequest-item-uploads:not(:first-child) {
  margin-top: 15px;
}

/* Time Tracker Css */
.timeTracker-wrapper {
  max-width: 630px;
  margin-inline: auto;
  border: 1px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
}

.timeTracker-item-contents {
  padding: 30px 20px;
}

.timeTracker-item-contents-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 15px 25px;
}

.timeTracker-item-contents-para {
  font-size: 15px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.timeTracker-item-contents-para strong {
  font-weight: 600;
  color: var(--heading-color);
}

.timeTracker-box {
  background-color: var(--main-color-one);
  padding: 30px 20px;
}

.timeTracker-box-title {
  font-size: 48px;
  font-weight: 600;
  color: #fff;
  display: block;
  font-family: var(--heading-font);
}

@media only screen and (max-width: 480px) {
  .timeTracker-box-title {
    font-size: 42px;
  }
}

@media only screen and (max-width: 375px) {
  .timeTracker-box-title {
    font-size: 36px;
  }
}

.timeTracker-box-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: 15px 20px;
}

.timeTracker-box-single {
  position: relative;
}

.timeTracker-box-stop {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 90px;
  width: 90px;
  border: 1px solid #fff;
  position: relative;
  border-radius: 50%;
  cursor: pointer;
}

@media only screen and (max-width: 480px) {
  .timeTracker-box-stop {
    height: 70px;
    width: 70px;
  }
}

.timeTracker-box-stop::before {
  content: "";
  position: absolute;
  height: 40px;
  width: 40px;
  left: 25px;
  top: 25px;
  background-color: #fff;
  border-radius: 10px;
}

@media only screen and (max-width: 480px) {
  .timeTracker-box-stop::before {
    height: 30px;
    width: 30px;
    left: 20px;
    top: 20px;
  }
}

.timeTracker-box-stop {
  display: none;
}

.timeTracker-box-stop.active {
  display: block;
}

.timeTracker-box-pause {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 90px;
  width: 90px;
  background-color: #fff;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
}

@media only screen and (max-width: 480px) {
  .timeTracker-box-pause {
    height: 70px;
    width: 70px;
  }
}

.timeTracker-box-pause::before,
.timeTracker-box-pause::after {
  content: "";
  position: absolute;
  left: 35%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  height: 42px;
  width: 7px;
  background-color: var(--main-color-one);
  border-radius: 10px;
}

@media only screen and (max-width: 480px) {

  .timeTracker-box-pause::before,
  .timeTracker-box-pause::after {
    height: 32px;
    width: 4px;
    left: 36%;
  }
}

.timeTracker-box-pause:after {
  left: auto;
  right: 35%;
}

@media only screen and (max-width: 480px) {
  .timeTracker-box-pause:after {
    right: 36%;
  }
}

.timeTracker-box-pause,
.timeTracker-box-play {
  position: absolute;
  left: 0%;
  top: 0;
}

.timeTracker-box-play {
  position: relative;
  z-index: 9;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 90px;
  width: 90px;
  background-color: #fff;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
}

@media only screen and (max-width: 480px) {
  .timeTracker-box-play {
    height: 70px;
    width: 70px;
  }
}

.timeTracker-box-play::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-40%, -50%);
  transform: translate(-40%, -50%);
  width: 0;
  height: 0;
  border-top: 20px solid transparent;
  border-left: 40px solid var(--main-color-one);
  border-bottom: 20px solid transparent;
  border-radius: 5px;
  background-color: unset;
}

@media only screen and (max-width: 480px) {
  .timeTracker-box-play::before {
    border-top: 15px solid transparent;
    border-left: 30px solid var(--main-color-one);
    border-bottom: 15px solid transparent;
  }
}

.timeTracker-box-play.active {
  visibility: hidden;
  opacity: 0;
}

.timeTracker-box-pause.active {
  visibility: visible;
  opacity: 1;
}

/* My Order Css */
.myOrder-wrapper-tabs .tabs li {
  position: relative;
  padding-bottom: 5px;
}

.myOrder-wrapper-tabs .tabs li::before {
  content: "";
  position: absolute;
  height: 2px;
  width: 0%;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  bottom: 0;
  background-color: transparent;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myOrder-wrapper-tabs .tabs li.active {
  color: var(--main-color-one);
  font-weight: 500;
}

.myOrder-wrapper-tabs .tabs li.active::before {
  background-color: var(--main-color-one);
  width: 100%;
}

.myOrder-wrapper-tabs .tabs {
  margin-bottom: 24px;
}

.myOrder-wrapper-tabs .tabs button {
  font-size: 16px;
  font-weight: 500;
  display: inline-block;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  line-height: 24px;
  padding: 7px 20px;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  border: 1px solid var(--border-color);
  color: var(--paragraph-color);
}

.myOrder-wrapper-tabs .tabs button.active,
.myOrder-wrapper-tabs .tabs button.btn-bg-1 {
  color: var(--white);
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .orderReport-crud-btn .btn-profile {
    font-size: 13px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .orderReport-crud-btn .btn-profile {
    font-size: 14px;
  }
}

.pending-approval {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: #EAECF0;
  color: var(--heading-color);
}

.pending-approval.active {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
}

.custom-order {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
}

.myOrder-single:not(:last-child) {
  margin-bottom: 25px;
}

.myOrder-single-item:not(:last-child) {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.myOrder-single-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

.myOrder-single-content {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.myOrder-single-content-id {
  color: var(--main-color-one);
  font-weight: 600;
  font-size: 16px;
}

.myOrder-single-content-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
}

.myOrder-single-content-btn .pending-approval {
  padding: 4px 15px;
  border-radius: 20px;
  font-size: 14px;
  line-height: 20px;
  background-color: #EAECF0;
  color: var(--body-color);
}

.myOrder-single-content-btn .custom-order {
  padding: 4px 15px;
  border-radius: 20px;
  font-size: 14px;
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
  line-height: 20px;
}

.myOrder-single-content-time {
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
}

.myOrder-single-content-para {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 400;
}

.myOrder-single-content-list {
  -webkit-columns: 250px 3;
  -moz-columns: 250px 3;
  columns: 250px 3;
  padding: 0;
  list-style: none;
}

.myOrder-single-content-list-item {
  padding-left: 20px;
  position: relative;
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myOrder-single-content-list-item:hover {
  color: var(--main-color-one);
}

.myOrder-single-content-list-item::before {
  content: "";
  position: absolute;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  left: 0;
  top: 8px;
}

.myOrder-single-block {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px 0;
}

.myOrder-single-block-item {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 12px;
}

.myOrder-single-block-item:not(:last-child) {
  padding-right: 30px;
  margin-right: 30px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .myOrder-single-block-item:not(:last-child) {
    padding-right: 20px;
    margin-right: 20px;
  }
}

.myOrder-single-block-item:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  right: 0;
  height: 70%;
  width: 1px;
  background-color: #ddd;
}

.myOrder-single-block-item-author {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.myOrder-single-block-item-author img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.myOrder-single-block-subtitle {
  font-size: 15px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.myOrder-single-block-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.myOrder-single-block-title .order-funded-btn {
  display: inline-block;
  padding: 4px 10px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myOrder-single-block-title .order-funded-btn:hover {
  background-color: var(--main-color-one);
  color: #fff;
}
.order-funded-btn.order-rating {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
}
.order-funded-btn.order-rating:hover {
  color: var(--secondary-color);
  background-color: rgba(var(--secondary-color-rgb), 0.1);
}
/* Proposal Css */
.myProposal-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 24px;
}

.myProposal-wrapper-single {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  position: relative;
}

@media only screen and (max-width: 375px) {
  .myProposal-wrapper-single-flex {
    display: -ms-grid;
    display: grid;
  }
}

.myProposal-wrapper-single-contents {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
}

@media only screen and (max-width: 375px) {
  .myProposal-wrapper-single-contents {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

.myProposal-wrapper-single-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 32px;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .myProposal-wrapper-single-title {
    font-size: 22px;
    line-height: 28px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .myProposal-wrapper-single-title {
    font-size: 20px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 575.98px) {
  .myProposal-wrapper-single-title {
    font-size: 20px;
    line-height: 28px;
  }
}

.myProposal-wrapper-single-id {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
}

.myProposal-wrapper-single-fixed {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  border-radius: 30px;
  border-radius: 30px;
  padding: 2px 10px;
  display: inline-block;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myProposal-wrapper-single-fixed:hover {
  background-color: var(--secondary-color);
  color: var(--heading-color);
}

.myProposal-wrapper-single-fixed.completed {
  border-color: #65C18C;
  color: #65C18C;
}

.myProposal-wrapper-single-fixed.completed:hover {
  background-color: #65C18C;
  color: #fff;
}

.myProposal-wrapper-single-fixed.active {
  border-color: var(--main-color-one);
  color: var(--main-color-one);
}

.myProposal-wrapper-single-fixed.active:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.myProposal-wrapper-single-date {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.myProposal-wrapper-single-date strong {
  font-weight: 600;
  color: var(--heading-color);
}

.myProposal-wrapper-single-arrow-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myProposal-wrapper-single-arrow-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

.myProposal-wrapper-single-balance {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  padding: 10px;
  border-radius: 10px;
}

.myProposal-wrapper-single-balance-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.myProposal-wrapper-single-balance-price {
  font-size: 24px;
  line-height: 24px;
  font-weight: 600;
}

.myProposal-wrapper-single-balance-para {
  color: var(--paragraph-color);
  font-size: 16px;
  margin-top: 7px;
}

.myProposal-wrapper-single-balance-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 30px;
  width: 30px;
  font-size: 15px;
  border: 1px solid #ddd;
  border-radius: 50%;
}

.myProposal-wrapper-single-right-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 20px;
}

.myProposal-wrapper-single-right-elipsis {
  font-size: 18px;
  padding: 5px;
  cursor: pointer;
}

.myProposal-wrapper-single-item {
  display: inline-block;
  position: relative;
}

.myProposal-wrapper-single-item:not(:last-child) {
  padding-right: 30px;
  margin-right: 30px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .myProposal-wrapper-single-item:not(:last-child) {
    padding-right: 20px;
    margin-right: 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .myProposal-wrapper-single-item:not(:last-child) {
    padding-right: 20px;
    margin-right: 20px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .myProposal-wrapper-single-item:not(:last-child) {
    padding-right: 20px;
    margin-right: 20px;
  }
}

@media only screen and (max-width: 480px) {
  .myProposal-wrapper-single-item:not(:last-child) {
    padding-right: 15px;
    margin-right: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .myProposal-wrapper-single-item:not(:last-child) {
    padding-right: 10px;
    margin-right: 10px;
  }
}

.myProposal-wrapper-single-item:not(:last-child)::after {
  content: "";
  position: absolute;
  height: 70%;
  width: 1px;
  right: 0;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  background-color: #ddd;
}

.myProposal-wrapper-single-item-para {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .myProposal-wrapper-single-item-para {
    font-size: 16px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .myProposal-wrapper-single-item-para {
    font-size: 16px;
  }
}

@media only screen and (max-width: 375px) {
  .myProposal-wrapper-single-item-para {
    font-size: 15px;
  }
}

.myProposal-wrapper-single-item-small {
  font-size: 14px;
  font-weight: 500;
  color: var(--paragraph-color);
  margin-right: 5px;
}

.proposal-amount-list {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px 0;
  padding: 0;
  margin: 0;
  list-style: none;
}

.proposal-amount-list-item {
  position: relative;
}

.proposal-amount-list-item:not(:last-child) {
  padding-right: 20px;
  margin-right: 20px;
}

.proposal-amount-list-item:not(:last-child)::before {
  content: "";
  position: absolute;
  height: 70%;
  width: 2px;
  background-color: #ddd;
  right: 1px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
}

.proposal-amount-list-item-content {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 5px;
}

.proposal-amount-list-item-content strong {
  font-weight: 700;
  color: var(--heading-color);
  font-size: 18px;
}

.myProposal-attachment-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.myProposal-attachment-list-item {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
}

.myProposal-attachment-list-item:not(:last-child) {
  margin-bottom: 10px;
}

.myProposal-attachment-list-item-para {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 7px;
}

.myProposal-attachment-list-item-para i {
  font-size: 18px;
  color: var(--success-color);
  margin-top: 3px;
}

/* Withdraw Money Css */
.withdrawal-single {
  background-color: #fff;
  padding: 20px;
}

.withdrawal-single:not(:last-child) {
  margin-bottom: 25px;
}

.withdrawal-single-header-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 15px 10px;
}

.withdrawal-single-item-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: var(--paragraph-color);
}

.withdrawal-single-item-balance {
  font-size: 32px;
  font-weight: 700;
  line-height: 36px;
  color: var(--main-color-one);
}

.withdrawal-single-item-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 15px;
}

.withdrawal-single-item-contents-para {
  font-size: 14px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
}

.withdrawal-single-item-contents-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
}

.withdrawal-single-item-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 55px;
  width: 55px;
  font-size: 24px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 50%;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.withdrawal-single-item-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

/* Setup Bank Account Css */
.setup-bank-form-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.setup-bank-form-flex .setup-bank-form-item {
  margin: 0 !important;
}

.setup-bank-form-item-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media only screen and (max-width: 480px) {
  .setup-bank-form-item-flex {
    display: -ms-grid;
    display: grid;
  }
}

.setup-bank-form-item-flex:not(:first-child) {
  margin-top: 24px;
}

.setup-bank-form-item-flex .setup-bank-form-item {
  margin: 0 !important;
}

.setup-bank-form-item {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.setup-bank-form-item:not(:first-child) {
  margin-top: 24px;
}

.setup-bank-form-item-select {
  display: -ms-grid;
  display: grid;
}

.setup-bank-form-item .iti {
  width: 100%;
}

.setup-bank-form-item .form--control {
  height: 38px;
  border: 1px solid #f3f3f3;
  border-radius: 7px;
  width: 100%;
  padding: 20px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: transparent;
  box-shadow: transparent;
}

.setup-bank-form-item .form--control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.4);
  -webkit-box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
}

.setup-bank-form-item .form--control#phone {
  width: 100%;
  padding-left: 50px;
}

.setup-bank-form-item .input-icon {
  position: absolute;
  bottom: 7px;
  left: 7px;
}

.setup-bank-form-item-icon {
  position: relative;
}

.setup-bank-form-item-icon::after {
  content: "";
  position: absolute;
  height: 42px;
  width: 2px;
  background-color: #f3f3f3;
  bottom: 0;
  left: 40px;
}

.setup-bank-form-item-icon .form--control,
.setup-bank-form-item-icon .form-control {
  padding-left: 50px;
  position: relative;
}

.setup-bank-form-item .form--message {
  border: 1px solid #f3f3f3;
  border-radius: 7px;
  width: 100%;
  padding: 20px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: transparent;
  box-shadow: transparent;
}

.setup-bank-form-item .form--message:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.4);
  -webkit-box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), 0.1);
}

.setup-bank-form-item .form--message.message-height {
  height: 150px;
}

.setup-bank-box {
  background-color: rgba(var(--main-color-one-rgb), 0.05);
  border: 1px solid var(--main-color-one);
  padding: 20px;
  border-radius: 10px;
}

.setup-bank-box-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

@media only screen and (max-width: 480px) {
  .setup-bank-box-flex {
    display: -ms-grid;
    display: grid;
  }
}

.setup-bank-box-details {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.setup-bank-box-details-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px;
}

@media only screen and (max-width: 375px) {
  .setup-bank-box-details-flex {
    display: -ms-grid;
    display: grid;
  }
}

.setup-bank-box-details-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  font-size: 20px;
  border-radius: 50%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.setup-bank-box-details-contents-title {
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
}

.setup-bank-box-details-contents-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 26px;
}

.setup-bank-box-details-contents-currency {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
  display: block;
}

.setup-bank-box-details-contents-country {
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.setup-bank-box-details-contents-country .country-short {
  display: inline-block;
  color: var(--main-color-one);
  font-weight: 600;
}

.setup-bank-box-btn-link {
  display: inline-block;
  color: var(--main-color-one);
  border-bottom: 1px solid var(--main-color-one);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

.bank-details-wrapper-item {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.bank-details-wrapper-item:not(:first-child) {
  margin-top: 24px;
}

.bank-details-wrapper-item.open .bank-details-wrapper-item-arrow {
  border-color: #EAECF0;
  color: var(--paragraph-color);
  background-color: unset;
}

.bank-details-wrapper-item.open .bank-details-wrapper-item-arrow i {
  /* IE 9 */
  -webkit-transform: rotate(180deg);
  /* Chrome, Safari, Opera */
  transform: rotate(180deg);
}

.bank-details-wrapper-item-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.bank-details-wrapper-item-inner {
  display: none;
}

.bank-details-wrapper-item-inner.show {
  display: block;
}

.bank-details-wrapper-item-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
}

.bank-details-wrapper-item-arrow {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 35px;
  width: 35px;
  font-size: 18px;
  border-radius: 5px;
  cursor: pointer;
  background-color: rgba(var(--main-color-one-rgb), 0.05);
  color: var(--main-color-one);
  border: 1px solid var(--main-color-one);
}

.bank-details-wrapper-item-arrow i {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.bank-details-wrapper-item-contents-title {
  font-size: 18px;
  font-weight: 600;
}

.bank-details-wrapper-item-contents-para {
  color: var(--paragraph-color);
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
}

.bank-details-wrapper-item-list-single {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px;
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.bank-details-wrapper-item-list-single:not(:first-child) {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #EAECF0;
}

.bank-details-wrapper-item-list-single .list-left {
  width: 130px;
}

.bank-details-wrapper-item-list-single strong {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  color: var(--heading-color);
  font-weight: 600;
}

.find-bank {
  display: inline-block;
  padding: 13px 20px;
  font-size: 16px;
  color: var(--paragraph-color);
  font-weight: 400;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  word-break: break-all;
  white-space: nowrap;
  border: 1px solid transparent;
  border-radius: 7px;
}

.find-bank.btn-bg-1 {
  background-color: var(--main-color-one);
  color: #fff;
}

.find-bank.btn-bg-1:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
  color: #fff;
}

.find-bank.btn-outline-gray {
  background-color: unset;
  color: var(--paragraph-color);
  border-color: #e2e2e2;
}

.find-bank.btn-outline-gray:hover {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: transparent;
}

/* Token Css */
.token-item {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.token-item:not(:first-child) {
  margin-top: 24px;
}

.token-item-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
}

.token-item-header-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.token-item-header-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
}

.token-item-expire-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.token-item-expire-para strong {
  font-weight: 600;
  color: var(--heading-color);
}

.token-item-details-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px;
}

.token-item-details-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  font-size: 18px;
  border-radius: 50%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.token-item-details-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.token-item-details-icon:hover path {
  fill: #fff;
}

.token-item-details-icon svg {
  max-width: 30px;
}

.token-item-details-icon svg path {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.token-item-details-contents-price {
  font-size: 32px;
  color: var(--main-color-one);
  font-weight: 700;
}

.token-item-details-contents-price sub {
  font-size: 18px;
  color: var(--paragraph-color);
  bottom: 0;
  font-weight: 400;
}

.token-item-details-contents-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.token-item-inner-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.token-item-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  border: 1px solid #e2e2e2;
  width: calc(50% - 12px);
  padding: 20px;
  border-radius: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

@media only screen and (max-width: 575.98px) {
  .token-item-list {
    width: 100%;
  }
}

.token-item-list.active {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  border-color: var(--main-color-one);
}

.token-item-list-contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.token-item-list-contents-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
}

.token-item-list-contents-price {
  font-size: 20px;
  font-weight: 600;
  color: var(--main-color-one);
  margin-top: 10px;
  display: block;
}

.token-item-list-contents-card {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
  margin-top: 10px;
}

.token-item-purchase-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.token-item-purchase-list-item:not(:first-child) {
  border-top: 1px solid #e2e2e2;
  padding-top: 15px;
  margin-top: 15px;
}

.token-item-purchase-list-item-single {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 15px 10px;
}

.token-item-purchase-list-item-single:not(:first-child) {
  margin-top: 12px;
}

.token-item-purchase-list-item-single.total strong {
  font-size: 24px;
}

.token-item-purchase-list-item-single strong {
  font-weight: 600;
  color: var(--heading-color);
}

/* Dashboard Css */
.dashboard-header-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.dashboard-header-item {
  width: calc(25% - 18px);
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .dashboard-header-item {
    width: calc(33.3333333333% - 16px);
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard-header-item {
    width: calc(50% - 12px);
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard-header-item {
    width: calc(33.3333333333% - 16px);
  }
}

@media only screen and (max-width: 767.98px) {
  .dashboard-header-item {
    width: calc(50% - 12px);
  }
}

@media only screen and (max-width: 480px) {
  .dashboard-header-item {
    width: calc(100% - 0px);
  }
}

.dashboard-header-item-subtitle {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
}

.dashboard-header-item-title {
  font-size: 36px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 42px;
}

.dashboard-item {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.dashboard-item:not(:first-child) {
  margin-top: 24px;
}

.dashboard-item-top-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
}

.dashboard-item-top-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 26px;
  color: var(--heading-color);
}

.dashboard-item-top-select .js_nice_select {
  height: 35px;
  line-height: 35px;
}

.dashboard-item-transaction:not(:first-child) {
  border-top: 1px solid #e2e2e2;
  padding-top: 20px;
  margin-top: 20px;
}

.dashboard-item-transaction-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
}

.dashboard-item-transaction-contents-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.dashboard-item-transaction-contents-price {
  font-size: 20px;
  font-weight: 600;
  color: #f00;
}

.dashboard-item-transaction-contents-price.received {
  color: var(--success-color);
}

.dashboard-item-transaction-contents-refund {
  padding: 3px 10px;
  border: 1px solid #e2e2e2;
  border-radius: 30px;
  cursor: pointer;
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  color: var(--paragraph-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
}

.dashboard-item-transaction-contents-refund.received {
  color: var(--success-color);
  border-color: var(--success-color);
}

.dashboard-item-transaction-contents-para {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 5px;
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
}

.dashboard-item-transaction-dots-icon {
  font-size: 20px;
  color: var(--heading-color);
  cursor: pointer;
}

/* Profile Settings Css */
.profile-close {
  position: relative;
  z-index: 9999;
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 35px;
  width: 35px;
  font-size: 18px;
  border-radius: 3px;
  color: #f00;
  position: absolute;
  right: -35px;
  top: 0;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.profile-close:hover {
  background-color: #f00;
  color: #fff;
}

.profile-bars {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  background-color: var(--main-color-one);
  color: #fff;
  font-size: 18px;
  border-radius: 5px;
}

.profile-settings-menu-inner {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  position: relative;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .profile-settings-menu-inner {
    position: absolute;
    left: -100%;
    top: auto;
    height: auto;
    width: 300px;
    z-index: 99;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: 0.5s;
    transition: 0.5s;
  }
}

@media only screen and (max-width: 375px) {
  .profile-settings-menu-inner {
    width: 275px;
  }
}

.profile-settings-menu-inner.active {
  visibility: visible;
  opacity: 1;
  left: 0;
}

.profile-settings-menu-list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.profile-settings-menu-item {
  transition: all .3s;
}
.profile-settings-menu-item:not(:first-child) {
  margin-top: 10px;
}

.profile-settings-menu-item:hover .profile-settings-menu-item-link,
.profile-settings-menu-item.active .profile-settings-menu-item-link {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  font-weight: 500;
}

.profile-settings-menu-item:hover .profile-settings-menu-item-link svg path,
.profile-settings-menu-item.active .profile-settings-menu-item-link svg path {
  fill: var(--main-color-one);
}

.profile-settings-menu-item.active .profile-settings-menu-item-icon {
  max-width: 25px;
  font-size: 18px;
  color: var(--main-color-one);
}

.profile-settings-menu-item.active .profile-settings-menu-item-icon svg {
  fill: var(--main-color-one);
}

.profile-settings-menu-item.active .profile-settings-menu-item-icon svg path {
  fill: var(--main-color-one);
}

.profile-settings-menu-item-icon svg {
  fill: var(--paragraph-color);
}

.profile-settings-menu-item-icon svg path {
  fill: var(--paragraph-color);
}

.profile-settings-menu-item-link {
  padding: 10px 20px;
  border-radius: 7px;
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
  transition: all .3s;
}
.profile-settings-menu-item-link svg {
  max-width: 18px;
}
.profile-settings-menu-item-link svg path {
  fill: var(--paragraph-color);
}
.single-profile-settings {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.single-profile-settings:not(:first-child) {
  margin-top: 24px;
}

.single-profile-settings-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px 20px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.single-profile-settings-thumb {
  width: 70px;
  height: 70px;
  border-radius: 50%;
}
.single-profile-settings-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.single-profile-settings-contents-upload {
  position: relative;
}

.single-profile-settings-contents-upload .upload-file {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  opacity: 0;
}

.single-profile-settings-contents-upload-btn {
  display: inline-block;
  padding: 6px 15px;
  font-size: 16px;
  background-color: var(--main-color-one);
  color: #fff;
  border-radius: 7px;
  position: relative;
}

.single-profile-settings-contents-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.single-profile-settings-header-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.single-profile-settings-header-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 26px;
  color: var(--heading-color);
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

/* Identity Verification Css */
.identity-verification.verify .identity-verification-contents-icon {
  background-color: var(--success-color);
  color: #fff;
}

.identity-verification-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.identity-verification-contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.identity-verification-contents-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px;
}

.identity-verification-contents-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  font-size: 24px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 50%;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.identity-verification-contents-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.identity-verification-contents-icon:hover svg path {
  fill: #fff;
}

.identity-verification-contents-icon svg path {
  fill: var(--main-color-one);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.identity-verification-contents-details-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 26px;
}

.identity-verification-contents-details-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
}

.identity-verifying-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
  color: var(--heading-color);
}

.identity-verifying-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.identity-verifying-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.identity-verifying-list {
  width: calc(33.3333333333% - 16px);
  padding: 20px;
  border: 1px solid #e2e2e2;
  border-radius: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
@media only screen and (max-width: 991.98px) {
  .identity-verifying-list {
    width: calc(50% - 12px);
  }
}
@media only screen and (max-width: 480px) {
  .identity-verifying-list {
    width: calc(100% - 0px);
  }
}

.identity-verifying-list.active {
  border-color: var(--main-color-one);
  background-color: rgba(var(--main-color-one-rgb), 0.05);
}

.identity-verifying-list.active .identity-verifying-list-contents-icon {
  background-color: var(--main-color-one);
  color: #fff;
}

.identity-verifying-list.active .identity-verifying-list-contents-details-title {
  color: var(--main-color-one) !important;
}

.identity-verifying-list-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
}

.identity-verifying-list-contents-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px 10px;
}

.identity-verifying-list-contents-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  font-size: 24px;
  border-radius: 50%;
  background-color: #EAECF0;
  color: var(--paragraph-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.identity-verifying-list-contents-details {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.identity-verifying-list-contents-details-title {
  font-size: 18px;
  line-height: 26px;
  font-weight: 500;
  color: var(--heading-color);
}

/* Membership Plan Css */
.membership-plan-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
}

.membership-plan-contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.membership-plan-contents-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 26px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 5px;
}

.membership-plan-contents-title-active {
  display: inline-block;
  padding: 2px 10px 4px;
  font-size: 14px;
  line-height: 20px;
  margin: 4px 0 0;
  font-weight: 400;
  border: 1px solid var(--success-color);
  color: var(--success-color);
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.membership-plan-contents-title-active:hover {
  background-color: var(--success-color);
  color: #fff;
}

.membership-plan-contents-list {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;
  gap: 15px 0;
}

.membership-plan-contents-list-item {
  font-size: 16px;
  line-height: 24px;
  color: var(--paragraph-color);
  position: relative;
}

.membership-plan-contents-list-item:not(:last-child) {
  margin-right: 20px;
  padding-right: 20px;
}

.membership-plan-contents-list-item:not(:last-child)::after {
  content: "";
  position: absolute;
  height: 70%;
  width: 2px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  right: 1px;
  background-color: #EAECF0;
}

.membership-plan-contents-feature {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  display: block;
  margin-top: 20px;
  text-decoration: underline;
}

.billing-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.billing-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 24px;
}

.billing-list-item:not(:first-child) {
  border-top: 1px solid #EAECF0;
  padding-top: 20px;
  margin-top: 20px;
}

.billing-list-item-single {
  max-width: calc(20% - 24px);
}

.billing-list-item-single-name {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--heading-color);
  font-family: var(--heading-font);
}

.billing-list-item-single-price {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  color: var(--heading-color);
  font-family: var(--heading-font);
}

.billing-list-item-single-date {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--paragraph-color);
  font-family: var(--heading-font);
}

.billing-list-item-single-card {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--heading-color);
  font-family: var(--heading-font);
}

.billing-list-item-single-paid {
  display: inline-block;
  padding: 3px 20px;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.billing-list-item-single-paid:hover {
  background-color: var(--success-color);
  color: #fff;
}

/* Notification Settings Css */
.notification-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.notification-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 15px 10px;
}

.notification-list-item:not(:first-child) {
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
  margin-top: 20px;
}

.notification-list-item-name {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--heading-color);
  font-family: var(--heading-font);
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
}

/* Step Verification Css */
.step-verification-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px 10px;
}

.step-verification-contents-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.step-verification-contents-para {
  font-size: 15px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
}

.step-verification-form-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 12px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.step-verification-form-box {
  width: calc(16.6666666667% - 10px);
}

.step-verification-form-box-input {
  height: 40px;
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  text-align: center;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
  font-size: 16px;
  font-weight: 600;
  -moz-appearance: textfield;
}

.step-verification-form-box-input::-webkit-outer-spin-button,
.step-verification-form-box-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
  -moz-appearance: textfield;
}

.step-verification-form-box-input::-webkit-input-placeholder {
  color: var(--paragraph-color);
  font-weight: 400;
}

.step-verification-form-box-input::-moz-placeholder {
  color: var(--paragraph-color);
  font-weight: 400;
}

.step-verification-form-box-input:-ms-input-placeholder {
  color: var(--paragraph-color);
  font-weight: 400;
}

.step-verification-form-box-input::-ms-input-placeholder {
  color: var(--paragraph-color);
  font-weight: 400;
}

.step-verification-form-box-input::placeholder {
  color: var(--paragraph-color);
  font-weight: 400;
}

.step-verification-form-box-input:focus {
  border-color: var(--main-color-one);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.2);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.2);
}

.step-verification-sendCode-btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 10px;
  font-size: 16px;
  font-weight: 500;
}

.step-verification-sendCode-btn:focus {
  color: var(--main-color-one);
}

.step-verification-sendCode-btn .code-send {
  text-decoration: underline;
  color: var(--main-color-one);
}

.step-verification-sendCode-btn .code-para {
  color: var(--paragraph-color);
}

/* Billing Method Css */
.billing-method:not(:first-child) {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.billing-method-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.billing-method-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.billing-method-card-content {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.billing-method-card-content-name {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
  font-weight: 400;
}

.billing-method-card-content-name strong {
  color: var(--heading-color);
  font-weight: 600;
}

.billing-method-card-content-date {
  font-size: 16px;
  color: var(--heading-color);
  font-weight: 400;
  line-height: 24px;
}

.billing-method-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.billing-method-icon-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 16px;
  border: 1px solid var(--border-color);
  color: var(--paragraph-color);
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.billing-method-icon-item:hover {
  background-color: var(--main-color-one);
  border-color: transparent;
  color: #fff;
}

.billing-method-icon-item.deleting {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.billing-method-icon-item.deleting:hover {
  background-color: var(--danger-color);
  border-color: transparent;
  color: #fff;
}

.billing-method-icon-item.editing {
  border-color: var(--main-color-one);
  color: var(--main-color-one);
}

.billing-method-icon-item.editing:hover {
  background-color: var(--main-color-one);
  border-color: transparent;
  color: #fff;
}

/* Support Tickets Css */
.supportTicket-single:not(:last-child) {
  margin-bottom: 25px;
}

.supportTicket-single-item:not(:last-child) {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.supportTicket-single-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

@media only screen and (max-width: 480px) {
  .supportTicket-single-flex {
    gap: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket-single-flex {
    gap: 10px;
  }
}

.supportTicket-single-content {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.supportTicket-single-content-id {
  color: var(--main-color-one);
  font-weight: 600;
  font-size: 16px;
}

.supportTicket-single-content-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
}

.supportTicket-single-content-btn .pending-approval {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: #EAECF0;
  color: var(--heading-color);
}

.supportTicket-single-content-btn .custom-order {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
}

.supportTicket-single-content-time {
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
}

.supportTicket-single-content-para {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 400;
}

.supportTicket-single-content-list {
  -webkit-columns: 250px 3;
  -moz-columns: 250px 3;
  columns: 250px 3;
  padding: 0;
  list-style: none;
}

.supportTicket-single-content-list-item {
  padding-left: 20px;
  position: relative;
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.supportTicket-single-content-list-item:hover {
  color: var(--main-color-one);
}

.supportTicket-single-content-list-item::before {
  content: "";
  position: absolute;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  left: 0;
  top: 8px;
}

.supportTicket-single-invoice {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.supportTicket-single-invoice-id {
  color: var(--main-color-one);
  font-weight: 500;
}

.supportTicket-single-chat:not(:first-child) {
  margin-top: 24px;
}

.supportTicket-single-chat.reply .supportTicket-single-chat-flex {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
}

.supportTicket-single-chat.reply .supportTicket-single-chat-contents {
  text-align: right;
}

.supportTicket-single-chat-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px 20px;
}

@media only screen and (max-width: 480px) {
  .supportTicket-single-chat-flex {
    gap: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket-single-chat-flex {
    gap: 10px;
  }
}

.supportTicket-single-chat-thumb {
  max-width: 70px;
}

@media only screen and (max-width: 480px) {
  .supportTicket-single-chat-thumb {
    max-width: 50px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket-single-chat-thumb {
    max-width: 40px;
  }
}

.supportTicket-single-chat-thumb img {
  border-radius: 50%;
}

.supportTicket-single-chat-contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.supportTicket-single-chat-box {
  display: inline-block;
  border: 1px solid var(--border-color);
  padding: 20px;
  border-radius: 5px;
}

@media only screen and (max-width: 480px) {
  .supportTicket-single-chat-box {
    padding: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket-single-chat-box {
    padding: 10px;
  }
}

.supportTicket-single-chat-message {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
}

@media only screen and (max-width: 480px) {
  .supportTicket-single-chat-message {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket-single-chat-message {
    font-size: 14px;
  }
}

.supportTicket-single-chat-time {
  font-size: 15px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.supportTicket-single-chat-replyForm-input {
  width: 100%;
}

.supportTicket-single-chat-replyForm-input .label-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 24px;
}

.pending-closed {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: #EAECF0;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pending-closed:hover {
  background-color: var(--heading-color);
  color: #fff;
}

.pending-progress {
  display: inline-block;
  padding: 5px 15px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border: 1px solid transparent;
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pending-progress:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.pending-progress.closed {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: #EAECF0;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pending-progress.closed:hover {
  background-color: var(--heading-color);
  color: #fff;
}

.pending-progress.completed {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.pending-progress.completed:hover {
  background-color: var(--success-color);
  color: #fff;
}

.pending-progress.active {
  background-color: rgba(var(--main-color-oen-rgb), 0.1);
  color: var(--main-color-one);
}

.pending-progress.active:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.pending-progress.cancel {
  background-color: rgba(var(--danger-color-rgb), 0.1);
  color: var(--danger-color);
}

.pending-progress.cancel:hover {
  background-color: var(--danger-color);
  color: #fff;
}

/* Contact Css */
.question-answer {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  padding: 30px;
  border-radius: 10px;
  border: 1px solid var(--main-color-one);
}

@media only screen and (max-width: 480px) {
  .question-answer {
    padding: 20px;
  }
}

@media only screen and (max-width: 375px) {
  .question-answer {
    padding: 10px;
  }
}

.question-answer-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
}

.question-answer-close {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  font-size: 24px;
  border-radius: 50%;
  background-color: #fff;
  color: #f00;
  cursor: pointer;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.question-answer-close:hover {
  background-color: #f00;
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .question-answer-close {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 40px;
    width: 40px;
    font-size: 20px;
  }
}

.question-answer-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media only screen and (max-width: 375px) {
  .question-answer-item {
    display: -ms-grid;
    display: grid;
  }
}

.question-answer-item-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 70px;
  width: 70px;
  font-size: 30px;
  background-color: var(--main-color-one);
  color: #fff;
  border-radius: 10px;
}

@media only screen and (max-width: 575.98px) {
  .question-answer-item-icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 50px;
    width: 50px;
    font-size: 24px;
  }
}

.question-answer-item-icon img,
.question-answer-item-icon svg {
  max-width: 50px;
}

@media only screen and (max-width: 575.98px) {

  .question-answer-item-icon img,
  .question-answer-item-icon svg {
    max-width: 35px;
  }
}

.question-answer-item-icon img path,
.question-answer-item-icon svg path {
  fill: #fff;
}

.question-answer-item-contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.question-answer-item-contents-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .question-answer-item-contents-title {
    font-size: 22px;
  }
}

@media only screen and (max-width: 575.98px) {
  .question-answer-item-contents-title {
    font-size: 20px;
  }
}

@media only screen and (max-width: 375px) {
  .question-answer-item-contents-title {
    font-size: 18px;
  }
}

.question-answer-item-contents-btn .see-here {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  text-decoration: underline;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.question-answer-item-contents-btn .see-here:hover {
  color: var(--main-color-one);
}

.contact-question {
  background-color: #fff;
  padding: 40px;
  border-radius: 10px;
  border: 1px solid var(--border-color);
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .contact-question {
    padding: 20px;
  }
}

.contact-question-search-padding {
  padding-right: 50px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .contact-question-search-padding {
    padding-right: 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .contact-question-search-padding {
    padding-right: 20px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .contact-question-search-padding {
    padding-right: 0;
  }
}

.contact-question .contact-question-search-padding {
  padding-right: 0;
}

.contact-question-top-title {
  font-size: 24px;
  line-height: 1.2;
  margin: -3px 0 0;
  font-weight: 600;
  color: var(--heading-color);
}

.contact-question-search-title {
  font-size: 24px;
  line-height: 1.2;
  margin: -2px 0 0;
  font-weight: 600;
  color: var(--heading-color);
}


@media only screen and (max-width: 480px) {
  .contact-question-top-title {
    font-size: 26px;
  }
}

@media only screen and (max-width: 375px) {
  .contact-question-top-title {
    font-size: 24px;
  }
}

.contact-question-search-form-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  gap: 12px 24px;
}

@media only screen and (max-width: 480px) {
  .contact-question-search-form-flex {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}

.contact-question-search-form-search {
  font-size: 16px;
  line-height: 26px;
  padding: 15px 35px;
  background-color: var(--main-color-one);
  border-radius: 5px;
  color: #fff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.contact-question-search-form-search:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
  color: #fff;
}

.contact-question-search-or {
  text-align: center;
  position: relative;
  z-index: 2;
  display: block;
  margin: 24px 0;
}

.contact-question-search-or-title {
  font-size: 15px;
  line-height: 1;
  font-weight: 400;
  color: var(--paragraph-color);
  margin-inline: auto;
  position: relative;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  background: #fff;
  padding: 10px 20px;
}

.contact-question-search-or::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  background: rgba(221, 221, 221, 0.4);
  width: 100%;
  height: 1px;
  z-index: -1;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}
.contact-info {
  background: var(--white);
  padding: 40px;
  border-radius: 10px;
  border: 1px solid var(--border-color);
  height: 100%;
}
.contact-info-item:not(:last-child) {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}
.contact-info-item-flex {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}
.contact-info-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border: 1px solid rgba(var(--main-color-one-rgb), .3);
  color: var(--main-color-one);
  background-color: rgba(var(--main-color-one-rgb), .1);
  border-radius: 50%;
  flex-shrink: 0;
}
.contact-info-item-contents {
  flex-grow: 1;
}
.contact-info-item-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 28px;
  color: var(--heading-color);
}
.contact-info-item-para {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: var(--paragraph-color);
  transition: all .3s;
  margin-top: 5px;
}
/* Most Search Topic Css */
.most-search-single {
  background-color: #fff;
  padding: 20px 15px;
  border-radius: 10px;
}

.most-search-single-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 60px;
  width: 60px;
  font-size: 24px;
  border-radius: 50%;
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
}

.most-search-single-icon svg,
.most-search-single-icon img {
  max-width: 35px;
  fill: var(--secondary-color);
}

.most-search-single-icon svg path,
.most-search-single-icon img path {
  fill: var(--secondary-color);
}

.most-search-single-contents-title {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  color: var(--heading-color);
}

.slick-slide:nth-child(6n+1) .most-search-single-icon {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
}

.slick-slide:nth-child(6n+1) .most-search-single-icon svg,
.slick-slide:nth-child(6n+1) .most-search-single-icon img {
  max-width: 35px;
  fill: var(--secondary-color);
}

.slick-slide:nth-child(6n+1) .most-search-single-icon svg path,
.slick-slide:nth-child(6n+1) .most-search-single-icon img path {
  fill: var(--secondary-color);
}

.slick-slide:nth-child(6n+2) .most-search-single-icon {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
}

.slick-slide:nth-child(6n+2) .most-search-single-icon svg,
.slick-slide:nth-child(6n+2) .most-search-single-icon img {
  max-width: 35px;
  fill: var(--main-color-one);
}

.slick-slide:nth-child(6n+2) .most-search-single-icon svg path,
.slick-slide:nth-child(6n+2) .most-search-single-icon img path {
  fill: var(--main-color-one);
}

.slick-slide:nth-child(6n+3) .most-search-single-icon {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.slick-slide:nth-child(6n+3) .most-search-single-icon svg,
.slick-slide:nth-child(6n+3) .most-search-single-icon img {
  max-width: 35px;
  fill: var(--success-color);
}

.slick-slide:nth-child(6n+3) .most-search-single-icon svg path,
.slick-slide:nth-child(6n+3) .most-search-single-icon img path {
  fill: var(--success-color);
}

.slick-slide:nth-child(6n+4) .most-search-single-icon {
  background-color: rgba(var(--danger-color-rgb), 0.1);
  color: var(--danger-color);
}

.slick-slide:nth-child(6n+4) .most-search-single-icon svg,
.slick-slide:nth-child(6n+4) .most-search-single-icon img {
  max-width: 35px;
  fill: var(--danger-color);
}

.slick-slide:nth-child(6n+4) .most-search-single-icon svg path,
.slick-slide:nth-child(6n+4) .most-search-single-icon img path {
  fill: var(--danger-color);
}

.slick-slide:nth-child(6n+5) .most-search-single-icon {
  background-color: rgba(3, 83, 151, 0.1);
  color: #035397;
}

.slick-slide:nth-child(6n+5) .most-search-single-icon svg,
.slick-slide:nth-child(6n+5) .most-search-single-icon img {
  max-width: 35px;
  fill: #035397;
}

.slick-slide:nth-child(6n+5) .most-search-single-icon svg path,
.slick-slide:nth-child(6n+5) .most-search-single-icon img path {
  fill: #035397;
}

.slick-slide:nth-child(6n+6) .most-search-single-icon {
  background-color: rgba(167, 96, 255, 0.1);
  color: #A760FF;
}

.slick-slide:nth-child(6n+6) .most-search-single-icon svg,
.slick-slide:nth-child(6n+6) .most-search-single-icon img {
  max-width: 35px;
  fill: #A760FF;
}

.slick-slide:nth-child(6n+6) .most-search-single-icon svg path,
.slick-slide:nth-child(6n+6) .most-search-single-icon img path {
  fill: #A760FF;
}

/* Ask Questions Css */
.ask-questions-header-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 28px;
}

@media only screen and (max-width: 575.98px) {
  .ask-questions-header-title {
    font-size: 22px;
  }
}

@media only screen and (max-width: 480px) {
  .ask-questions-header-title {
    font-size: 20px;
  }
}

.ask-questions-contents-author.reply {
  margin-left: 50px;
}

@media only screen and (max-width: 375px) {
  .ask-questions-contents-author.reply {
    margin-left: 30px;
  }
}

.ask-questions-contents-author:not(:first-child) {
  margin-top: 24px;
}

.ask-questions-contents-author-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px 15px;
}

@media only screen and (max-width: 375px) {
  .ask-questions-contents-author-flex {
    display: -ms-grid;
    display: grid;
  }
}

.ask-questions-contents-author-thumb {
  max-width: 50px;
}

.ask-questions-contents-author-thumb img {
  border-radius: 50%;
}

.ask-questions-contents-author-details {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.ask-questions-contents-author-details-title {
  font-size: 18px;
  line-height: 24px;
  font-weight: 600;
  color: var(--heading-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.ask-questions-contents-author-details-title .activities {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  padding: 4px 20px;
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
  border-radius: 3px;
}

.ask-questions-contents-author-details-time {
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.ask-questions-contents-author-details-para {
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.ask-questions-contents-author-form {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.ask-questions-contents-author-form textarea {
  border: 1px solid var(--border-color);
  border-radius: 7px;
  padding: 5px 20px;
}

.all-questions-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.all-questions-list-item:not(:first-child) {
  border-top: 1px solid var(--border-color);
  padding-top: 24px;
  margin-top: 24px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .all-questions-list-item:not(:first-child) {
    margin-top: 15px;
    padding-top: 15px;
  }
}

.all-questions-list-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--heading-color);
  line-height: 24px;
}

.all-questions-list-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
  margin-top: 8px;
}

.all-questions-list-para .community {
  color: var(--main-color-one);
  font-weight: 500;
}

/* End Contract Css */
.end-contract-single {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.end-contract-single:not(:first-child) {
  margin-top: 24px;
}

.end-contract-single-select {
  display: -ms-grid;
  display: grid;
}

.end-contract-feedback-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

.end-contract-feedback-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.end-contract-feedback-single {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.end-contract-feedback-single:not(:first-child) {
  margin-top: 24px;
}

.end-contract-feedback-single-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
  color: var(--heading-color);
}

.end-contract-feedback-single-title-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 15px 10px;
}

.end-contract-reaction {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

.end-contract-reaction-item {
  border: 1px solid var(--border-color);
  padding: 0px 15px;
  border-radius: 30px;
  line-height: 42px;
  cursor: pointer;
  position: relative;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.end-contract-reaction-item:hover,
.end-contract-reaction-item.active {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
  background-color: rgba(var(--secondary-color-rgb), 0.1);
}

.end-contract-reaction-item:hover .end-contract-reaction-item-tooltip,
.end-contract-reaction-item.active .end-contract-reaction-item-tooltip {
  visibility: visible;
  opacity: 1;
  z-index: 9;
}

.end-contract-reaction-item:hover .end-contract-reaction-icon,
.end-contract-reaction-item.active .end-contract-reaction-icon {
  border-color: var(--secondary-color);
}

.end-contract-reaction-item:hover .end-contract-reaction-review-star,
.end-contract-reaction-item.active .end-contract-reaction-review-star {
  color: var(--secondary-color);
}

.end-contract-reaction-item-tooltip {
  position: absolute;
  top: -30px;
  left: 0;
  text-align: center;
  background: var(--secondary-color);
  color: #fff;
  padding: 3px 12px;
  line-height: 20px;
  border-radius: 3px;
  font-size: 12px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  visibility: hidden;
  opacity: 0;
}

.end-contract-reaction-item-tooltip::before {
  content: "";
  position: absolute;
  left: auto;
  right: auto;
  bottom: -10px;
  height: 0;
  width: 0;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  border-top: 20px solid var(--secondary-color);
  border-radius: 5px;
  z-index: -1;
}

.end-contract-reaction-item-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.end-contract-reaction-icon {
  border-right: 1px solid var(--border-color);
  padding-right: 10px;
  margin-right: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.end-contract-reaction-review-star {
  font-size: 15px;
  color: var(--body-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.end-contract-reaction-review-star:not(:last-child) {
  margin-right: 2px;
}

/* End Contract widget Css */
.end-contract-widget-item {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.end-contract-widget-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.end-contract-widget-list-item {
  font-size: 16px;
  line-height: 30px;
  position: relative;
  text-align: left;
  z-index: 2;
  padding: 5px 10px 5px 40px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border-radius: 5px;
  border: 1px solid var(--border-color);
}

.end-contract-widget-list-item:not(:last-child) {
  margin-bottom: 10px;
}

.end-contract-widget-list-item.active {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
  border-color: var(--success-color);
}

.end-contract-widget-list-item.active::after {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  background: var(--success-color);
  border-color: var(--success-color);
}

.end-contract-widget-list-item::after {
  content: "";
  position: absolute;
  height: 22px;
  width: 22px;
  border: 1px solid var(--border-color);
  left: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background: none;
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border-radius: 50%;
}

.overall-score {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px;
}

.overall-score-para {
  font-size: 16px;
  line-height: 24px;
  color: var(--paragraph-color);
}

.overall-score-review {
  border: 1px solid var(--border-color);
  padding: 3px 10px;
  border-radius: 3px;
  font-size: 16px;
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

.overall-score-review-icon {
  color: var(--secondary-color);
  font-size: 15px;
}

.overall-score-review-para {
  color: var(--paragraph-color);
  font-size: 16px;
  line-height: 24px;
}

/* Pay Now Css */
.pay-now-single {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.pay-now-single:not(:first-child) {
  margin-top: 24px;
}

.pay-now-single-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

.pay-now-single-contents-work:not(:first-child) {
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
  margin-top: 20px;
}

.pay-now-single-contents-work-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

.pay-now-single-contents-work-date {
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
  line-height: 24px;
}

.pay-now-single-contents-work-viewMore {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 16px;
  line-height: 24px;
  color: var(--heading-color);
  font-weight: 400;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pay-now-single-contents-work-viewMore:hover {
  color: var(--main-color-one);
}

/* pay now popup css */
.show_bonus_form {
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
  height: 0;
  margin: 0;
}

.show_bonus_form.show {
  visibility: visible;
  opacity: 1;
  height: auto;
  margin-top: 20px;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
}

.popup-contents-payment-form .custom-radio input[type=radio] {
  height: 18px !important;
  width: 18px !important;
}

.popup-contents-payment-form .custom-radio input[type=radio]::before {
  width: calc(100% - 6px);
  height: calc(100% - 6px);
}

.popup-contents-payment-form .custom-radio label {
  margin: -5px 0 0;
}

.popup-contents-payment-item:not(:first-child) {
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
  margin-top: 20px;
}

.popup-contents-payment-item .custom-form .custom-radio input[type=radio] {
  height: 18px !important;
  width: 18px !important;
}

.popup-contents-payment-item .custom-form .custom-radio input[type=radio]::before {
  width: calc(100% - 6px);
  height: calc(100% - 6px);
}

.popup-contents-payment-item .custom-form .custom-radio label {
  margin: -5px 0 0;
}

.popup-contents-payment-item-title {
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
}

.popup-contents-payment-item-flex {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.popup-contents-payment-item-single {
  position: relative;
}

.popup-contents-payment-item-single:not(:last-child) {
  margin-right: 20px;
  padding-right: 20px;
}

.popup-contents-payment-item-single:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  height: 70%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  width: 1px;
  background-color: var(--border-color);
}

.popup-contents-payment-item-single-para {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: var(--paragraph-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

.popup-contents-payment-item-single-para strong {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
}

/* Transactions Css */
.transaction-single {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.transaction-single-header-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
  color: var(--heading-color);
}

.transaction-single-table {
  overflow-x: auto;
}

.transaction-single-table table {
  margin: 0;
}

.transaction-single-table .table> :not(:first-child) {
  border-top: 0;
}

.transaction-single-table thead {
  background-color: var(--section-bg-2);
  border-radius: 5px;
}

.transaction-single-table thead tr th {
  padding: 20px 10px;
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
  white-space: nowrap;
}

.transaction-single-table tbody {
  border-top: 0;
}

.transaction-single-table tbody tr:not(:last-child) {
  border-bottom: 0;
}

.transaction-single-table tbody tr:not(:last-child) td {
  border-bottom: 1px solid var(--border-color);
}

.transaction-single-table tbody tr:last-child td {
  border: 0;
  padding-bottom: 0;
}

.transaction-single-table tbody tr td {
  padding: 20px 10px;
  font-size: 15px;
  line-height: 20px;
  font-weight: 500;
  color: var(--paragraph-color);
}

.transaction-single-table .invoice_number {
  color: var(--main-color-one);
  font-weight: 500;
}

.transaction-single-table .amount {
  color: var(--danger-color);
  font-weight: 500;
}

.transaction-single-table .amount__added {
  color: var(--success-color);
}

.transaction-single-table .balance {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 5px 15px;
  border: 1px solid var(--border-color);
  position: relative;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
  border-radius: 30px;
  white-space: nowrap;
}

.transaction-single-table .balance::before {
  content: "$";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

.transaction-single-table .balance__added {
  color: var(--success-color);
  border-color: var(--success-color);
}

.transaction-single-table .source_balance strong {
  color: var(--heading-color);
}

/* Delete Edit Popup Css */
.elipsis {
  position: relative;
}

.elipsis__icon {
  font-size: 16px;
  color: var(--paragraph-color);
  cursor: pointer;
  padding: 0 5px;
}

.elipsis__wrap {
  background-color: #fff;
  -webkit-box-shadow: 0 0 20px #f3f3f3;
  box-shadow: 0 0 20px #f3f3f3;
  position: absolute;
  bottom: 105%;
  right: 0;
  z-index: 9;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 5px;
  /* IE 9 */
  -webkit-transform: scale(0.7);
  /* Chrome, Safari, Opera */
  transform: scale(0.7);
}

.elipsis__wrap.show {
  visibility: visible;
  opacity: 1;
  /* IE 9 */
  -webkit-transform: scale(1);
  /* Chrome, Safari, Opera */
  transform: scale(1);
}

.elipsis__wrap__contents__icon {
  display: block;
  font-size: 15px;
  line-height: 24px;
  font-weight: 400;
  padding: 10px 20px;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
}

.elipsis__wrap__contents__icon:not(:first-child) {
  border-top: 1px solid var(--border-color);
}

.elipsis__wrap__contents__icon.editing {
  color: var(--main-color-one);
}

.elipsis__wrap__contents__icon.deleting {
  color: var(--danger-color);
}

/* Send Offer Css */
.send-offer {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.send-offer .myJob-wrapper-single {
  border: 1px solid var(--border-color);
}

.select2-container {
  font-size: initial;
  display: grid;
  width: 100% !important;
}

.select2-dropdown {
  z-index: 1060 !important;
}
 .select2-container--default .select2-results__option--selected {
   background-color: #ddd;
   padding: 0 6px;
 }
 .select2-results .select2-results__option {
   padding: 0 6px;
   color: var(--heading-color);
   font-size: 15px;
 }
 .select2-container--default .select2-results__option--selected {
   background-color: unset !important;
 }

.select2-container {
  display: grid !important;
  z-index: 9992;
  display: grid;
  width: 100% !important;
}
 .select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
   background-color: #1967D2 !important;
   color: white;
 }
 .select2-container--open .select2-dropdown--below {
   border: 1px solid #aaa;
   box-shadow: 0 5px 16px #aaa;
 }
 .select2-container--default .select2-search--inline .select2-search__field {
   line-height: 1;
 }
.select2-container--default .select2-selection--single {
  border: 1px solid #ced4da !important;
  height: 38px !important;
}
 .select2-container--default .select2-search--dropdown .select2-search__field {
   border: 1px solid var(--border-color);
 }
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 38px !important;
  width: 42px !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: var(--paragraph-color) !important;
  line-height: 38px !important;
}

.note-editor.note-airframe .note-editing-area .note-editable,
.note-editor.note-frame .note-editing-area .note-editable {
  height: 250px;
}

.popup-contents-form .single-input .select2-container {
  z-index: 9992 !important;
}

/* Project Preview start */
.sticky-sidebar {
  position: sticky;
  top: 0;
  z-index: 9;
}

.project-preview {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.project-preview:not(:first-child) {
  margin-top: 24px;
}

.project-preview-head-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
  margin: -3px 0 0;
}

.project-preview-thumb img {
  border-radius: 10px;
}

.project-preview-contents-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  margin: -3px 0 0;
}

@media screen and (max-width: 375px) {
  .project-preview-contents-title {
    font-size: 20px;
  }
}

.project-preview-contents-para {
  font-size: 14px;
  line-height: 24px;
  margin-top: 10px;
  color: var(--paragraph-color);
}

.project-preview-tab .tabs {
  background-color: var(--border-color);
  padding: 10px;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  border-radius: 10px;
  box-shadow: 0px 0px 10px #f1f1f1;
}

@media only screen and (max-width: 375px) {
  .project-preview-tab .tabs {
    padding: 5px;
  }
}

.project-preview-tab .tabs li {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center;
  padding: 5px;
  border-radius: 10px;
  color: var(--body-color);
  font-weight: 600;
}

@media only screen and (max-width: 375px) {
  .project-preview-tab .tabs li {
    font-size: 15px;
  }
}

.project-preview-tab .tabs li::before {
  display: none;
}

.project-preview-tab .tabs li.active {
  background-color: #fff;
}

.project-preview-tab-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  background-color: var(--border-color);
  padding: 10px;
  border-radius: 5px;
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-header {
    padding: 5px;
  }
}

.project-preview-tab-header-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 12px 10px;
  position: relative;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .project-preview-tab-header-item {
    gap: 12px 12px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item {
    gap: 10px 10px;
  }
}

.project-preview-tab-header-item:not(:last-child) {
  margin-right: 20px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .project-preview-tab-header-item:not(:last-child) {
    margin-right: 10px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item:not(:last-child) {
    margin-right: 5px;
  }
}

.project-preview-tab-header-item .left {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item .left {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-header-item .left {
    font-size: 15px;
  }
}

.project-preview-tab-header-item .left i {
  font-size: 15px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item .left i {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-header-item .left i {
    font-size: 15px;
  }
}

.project-preview-tab-header-item .right {
  font-weight: 700;
  color: var(--heading-color);
  font-size: 15px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item .right {
    font-size: 16px;
  }
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-header-item .right {
    font-size: 16px;
  }
}

.project-preview-tab-inner-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 12px 20px;
}

.project-preview-tab-inner-item:not(:last-child) {
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
}

.project-preview-tab-inner-item .check-icon {
  color: var(--success-color);
}

.project-preview-tab-inner-item.included .left {
  position: relative;
}

.project-preview-tab-inner-item.included .left::before {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  color: var(--success-color);
  font-size: 18px;
}

.project-preview-tab-inner-item.not_included .left {
  position: relative;
  opacity: 0.6;
}

.project-preview-tab-inner-item.not_included .left::before {
  content: "\f00d";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  color: var(--paragraph-color);
  font-size: 18px;
  opacity: 0.5;
}

.project-preview-tab-inner-item.not_included .right {
  opacity: 0.6;
}

.project-preview-tab-inner-item .left {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-inner-item .left {
    font-size: 15px;
  }
}

.project-preview-tab-inner-item .left.price-title {
  font-size: 18px;
  line-height: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-inner-item .left.price-title {
    font-size: 18px;
  }
}

.project-preview-tab-inner-item .right {
  font-weight: 500;
  color: var(--heading-color);
  font-size: 18px;
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-inner-item .right {
    font-size: 16px;
  }
}

.project-preview-tab-inner-item .right.price {
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  color: var(--main-color-one);
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-inner-item .right.price {
    font-size: 18px;
  }
}

.project-preview-tab-inner-item .right.price s {
  color: var(--body-color);
  font-weight: 400;
}

/* Project Preview end */


/* Project Preview Pricing */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper {
    gap: 25px 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper {
    gap: 25px 0;
  }
}

.pricing-wrapper-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-left {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-left .pricing-wrapper-card-top {
    display: none;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-left .pricing-wrapper-card-top {
    display: none;
  }
}

.pricing-wrapper-left .pricing-wrapper-card::before {
  display: none;
}

.pricing-wrapper-left .pricing-wrapper-card-bottom {
  position: relative;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.pricing-wrapper-left .pricing-wrapper-card-bottom::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  height: 100%;
  width: 0%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing-wrapper-left .pricing-wrapper-card:hover .pricing-wrapper-card-bottom {
  border-radius: 0px;
  overflow: hidden;
}

.pricing-wrapper-left .pricing-wrapper-card:hover .pricing-wrapper-card-bottom::before {
  visibility: visible;
  opacity: 1;
  width: 100%;
}

.pricing-wrapper-right {
  -webkit-box-flex: 3;
  -ms-flex: 3;
  flex: 3;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-right {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    gap: 25px 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-right {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    gap: 25px 0;
  }
}

.pricing-wrapper-right .pricing-wrapper-card {
  border: 1px solid rgba(221, 221, 221, 0.4);
  border-left: 0;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-right .pricing-wrapper-card:nth-child(1) {
    border-left: 1px solid rgba(221, 221, 221, 0.4);
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-right .pricing-wrapper-card {
    border-left: 1px solid rgba(221, 221, 221, 0.4);
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .pricing-wrapper-right .pricing-wrapper-card:nth-child(3n+1) {
    border-right: unset;
  }
}

.pricing-wrapper-right .pricing-wrapper-card:nth-child(3n+1) .pricing-wrapper-card-top {
  border-left: 1px solid rgba(221, 221, 221, 0.4);
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-right .pricing-wrapper-card:nth-child(3n+1) .pricing-wrapper-card-top {
    border-left: unset;
  }
}

.pricing-wrapper-right .pricing-wrapper-card-bottom {
  border: 0;
  border-top: 1px solid rgba(221, 221, 221, 0.4);
  border-left: 0;
}

.pricing-wrapper-card {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  position: relative;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-card {
    gap: 25px 0;
  }
}

@media only screen and (max-width: 575.98px) {
  .pricing-wrapper-card {
    -ms-flex-preferred-size: 50%;
    flex-basis: 50%;
  }
}

@media only screen and (max-width: 480px) {
  .pricing-wrapper-card {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

.pricing-wrapper-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  height: 100%;
  width: 0%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing-wrapper-card:hover {
  color: #fff;
  border-radius: 0px;
  overflow: hidden;
  /* IE 9 */
  -webkit-transform: scaleY(1);
  /* Chrome, Safari, Opera */
  transform: scaleY(1);
}

.pricing-wrapper-card:hover::before {
  visibility: visible;
  opacity: 1;
  width: 100%;
}

.pricing-wrapper-card.active .pricing-wrapper-card-top {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.pricing-wrapper-card.active .pricing-wrapper-card-bottom {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.pricing-wrapper-card-top {
  padding: 20px;
  min-height: 70px;
}

.pricing-wrapper-card-top-prices {
  color: var(--heading-color);
  font-size: 16px;
  line-height: 24px;
  margin: -3px 0 0;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.pricing-wrapper-card-bottom {
  border: 1px solid rgba(221, 221, 221, 0.4);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}


  .pricing-wrapper-card-bottom-list ul li {
    color: #585858;
    font-size: 16px;
    font-weight: 400;
    border-bottom: 1px solid rgba(221, 221, 221, 0.4);
    padding: 0px 10px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: 50px;
    display: flex;
    align-items: center;
    overflow-y: auto;
  }
  .text-center .pricing-wrapper-card-bottom-list ul li {
    justify-content: center;
  }

.pricing-wrapper-card-bottom-list ul li:last-child {
  border: 0;
}

.pricing-wrapper-card-bottom-list ul li .price-main {
  font-size: 18px;
  font-weight: 600;
  color: var(--main-color-one);
  display: inline-block;
}

.pricing-wrapper-card-bottom-list ul li .price-old {
  font-size: 14px;
  color: var(--paragraph-color);
  line-height: 1;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 15px;
    padding: 0 10px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 15px;
  }
}

@media only screen and (max-width: 480px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 16px;
  }
}

@media only screen and (max-width: 375px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 15px;
  }
}

.pricing-wrapper-card-bottom-list ul li .check-icon {
  color: var(--main-color-one) !important;
}

/* Project Feedback Css */
.project-feedback-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px;
}

@media only screen and (max-width: 375px) {
  .project-feedback-flex {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}

.project-feedback-thumb {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  max-width: 70px;
}

.project-feedback-thumb img {
  border-radius: 50%;
}

.project-feedback-contents-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 15px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.project-feedback-contents-name {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.project-feedback-contents-title {
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  color: var(--heading-color);
}

.project-feedback-contents-subtitle {
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;
  color: var(--paragraph-color);
}

.project-feedback-contents-review {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  color: #ffac16;
  padding: 3px 0px;
}

.project-feedback-contents-para {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: var(--paragraph-color);
}

.search_wrapper {
  position: relative;
}

.search_wrapper_icon {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 100%;
  padding: 0px 13px;
  border-right: 1px solid var(--border-color);
  text-align: center;
  line-height: 42px;
  font-size: 14px;
  cursor: pointer;
}

.search_wrapper .form--control {
  height: 42px;
  border-radius: 5px;
  padding-left: 55px;
  border: 1px solid var(--border-color);
}

.custom_table {
  overflow-x: auto;
}

.custom_table table {
  width: 100%;
}

.custom_table table thead {
  border: 0;
}

.custom_table table tr {
  border: 0;
}

.custom_table table th {
  white-space: nowrap;
  padding: 10px;
  font-size: 18px;
  font-weight: 700;
  color: var(--heading-color);
}

.custom_table table td {
  padding: 10px;
  border: 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: var(--paragraph-color);
}

.custom_table.style-02 table thead {
  background-color: var(--main-color-one);
}

.custom_table.style-02 table thead th {
  color: #fff;
}

.custom_table.style-02 table tbody tr:nth-child(odd) {
  background-color: var(--section-bg-1);
}

.custom_table.style-02 table tbody tr:nth-child(even) {
  background-color: var(--border-color);
}

.custom_table.style-03 table thead {
  background-color: var(--main-color-one);
}

.custom_table.style-03 table thead th {
  color: #fff;
}

.custom_table.style-03 table tbody tr:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.custom_table.style-04 table thead th {
  border: 1px solid var(--border-color);
}

.custom_table.style-04 table tbody tr:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.custom_table.style-04 table tbody tr td {
  border: 1px solid var(--border-color);
}

.custom_table.style-05 table thead th {
  border: 1px solid rgba(var(--main-color-one-rgb), 0.3);
}

.custom_table.style-05 table tbody tr:not(:last-child) {
  border: 1px solid rgba(var(--main-color-one-rgb), 0.3);
}

.custom_table.style-05 table tbody tr td {
  border: 1px solid rgba(var(--main-color-one-rgb), 0.3);
}

.custom_table .DataTable_activation {
  margin-top: 24px;
  display: inline-table;
  border: initial !important;
  overflow-x: auto;
  margin-bottom: 24px;
}

.custom_table .DataTable_activation thead th {
  border-color: initial;
}

.custom_table .DataTable_activation tbody {
  border: initial;
}

.custom_table .dataTables_wrapper .dataTables_paginate .paginate_button {
  background: var(--border-color) !important;
  border-color: var(--border-color) !important;
}

.custom_table .dataTables_wrapper .dataTables_paginate .paginate_button.current,
.custom_table .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--main-color-one) !important;
  color: #fff !important;
}

/*Pagination Css*/

.custom_pagination {}

.custom_pagination .pagination {
  justify-content: center;
  gap: 5px;
}

.custom_pagination .page-link {
  position: relative;
  display: block;
  color: var(--paragraph-color);
  text-decoration: none;
  background-color: #fff;
  border: 1px solid var(--main-color-one);
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  width: 36px;
  border-radius: 50%;
}

.custom_pagination .page-item.active .page-link,
.custom_pagination .page-item:hover .page-link {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  color: #fff;
}

.custom_pagination .page-item:first-child .page-link {
  border-top-left-radius: 50%;
  border-bottom-left-radius: 50%;
  font-size: 24px;
}

.custom_pagination .page-item:last-child .page-link {
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  font-size: 24px;
}

/*Payment gateway */
.payment_getway_image>ul {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
}

.payment_getway_image>ul li {
  width: calc(100% / 4 - 7.5px);
  border: 1px solid #f1f1f1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.payment_getway_image>ul li.selected {
  border-color: var(--main-color-one);
}

.payment_getway_image>ul li.selected:after {
  content: "\f00c";
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  font-size: 12px;
  height: 18px;
  width: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  color: #fff;
  left: 0px;
  top: 0px;
  z-index: 9;
  background: var(--main-color-one);
  transition: all .3s;
}



/* Category All Start */
.category-shadow-bottom {
  -webkit-box-shadow: 0px 5px 25px 0px #f1f1f1;
  box-shadow: 0px 5px 25px 0px #f1f1f1;
}

@media (min-width: 992px) {
  .sticky-desktop {
    position: sticky;
    top: 0;
    z-index: 992;
  }
}

.sticky-category {
  -webkit-transform: rotateX(90deg);
  transform: rotateX(90deg);
  opacity: 0;
}

.sticky-category.sticky {
  opacity: 1;
  -webkit-transform: rotateX(0deg);
  transform: rotateX(0deg);
  transition: transform 0.2s, -webkit-transform 0.2s;
  visibility: visible !important;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .mobileCategory {
    padding: 10px 0;
  }

  .mobileCategory .categoryBtn {
    padding: 10px 20px;
    border: 1px solid var(--border-color);
    display: inline-block;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
  }

  .mobileCategory .categoryBtn-icon {
    font-size: 18px;
    color: var(--heading-color);
  }

  .mobileCategory .categoryBtn-title {
    font-size: 16px;
    font-weight: 400;
    color: var(--heading-color);
  }

  .mobileCategory .categoryAll-wrap {
    display: none;
  }
}

.categoryAll-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 0 20px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  position: relative;
  margin: 0;
  padding: 0;
  list-style: none;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .categoryAll-list {
    border-top: 1px solid var(--border-color);
    margin-top: 10px !important;
    overflow-y: auto;
    max-height: 320px;
    padding: 0 10px 0 0;
  }
}

.categoryAll-list-submenu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #fff;
  list-style: none;
  padding: 0;
  margin: 0;
  z-index: 9;
  border-bottom: 3px solid var(--main-color-one);
  min-width: 170px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  visibility: hidden;
  opacity: 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .categoryAll-list-submenu {
    position: initial;
    visibility: visible;
    opacity: 1;
    min-width: 100%;
    display: none;
    max-height: 200px;
    overflow-y: auto;
  }
}

.categoryAll-list-submenu li a {
  line-height: 24px;
  font-size: 16px;
  color: var(--heading-color);
  display: block;
  padding: 10px 20px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  position: relative;
  z-index: 0;
}

.categoryAll-list-submenu li a:hover {
  color: #fff;
  background-color: unset;
}

.categoryAll-list-submenu li a:hover::after {
  height: 100%;
}

.categoryAll-list-submenu li a::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 0%;
  width: 100%;
  background-color: var(--main-color-one);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  z-index: -1;
}

.categoryAll-list-submenu li:not(:first-child) a {
  padding-top: 10px;
}

.categoryAll-list-submenu li:not(:last-child) a {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.categoryAll-list-item {
  position: relative;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .categoryAll-list-item {
    width: 100%;
  }
}

.categoryAll-list-item:hover .categoryAll-list-item-link {
  color: var(--main-color-one);
}

@media (min-width: 992px) {
  .categoryAll-list-item:hover .categoryAll-list-item-link .categoryIcon::after {
    content: "\f068";
    color: var(--main-color-one);
  }
}

.categoryAll-list-item:hover .categoryAll-list-item-link::before {
  width: 100%;
}

.categoryAll-list-item:hover .categoryAll-list-submenu {
  visibility: visible;
  opacity: 1;
}

.categoryAll-list-item.show .categoryIcon::after {
  content: "\f068";
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  color: var(--main-color-one);
  border-color: var(--main-color-one);
}

.categoryAll-list-item-link {
  white-space: nowrap;
  display: inline-block;
  padding: 10px 0px;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  position: relative;
  color: var(--paragraph-color);
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .categoryAll-list-item-link {
    display: block;
    margin-right: 35px;
  }
}

.categoryAll-list-item-link::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background-color: var(--main-color-one);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .categoryAll-list-item-link .categoryIcon {
    position: absolute;
    right: -35px;
    top: 9px;
  }
}

.categoryAll-list-item-link .categoryIcon::after {
  content: "+";
  font-family: "Font Awesome 6 Free";
  color: var(--paragraph-color);
  font-size: 14px;
  cursor: pointer;
  font-weight: 900;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .categoryAll-list-item-link .categoryIcon::after {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 25px;
    width: 25px;
    border: 1px solid var(--paragraph-color);
  }
}

/* Sub Category css */
.hidden_item {
  visibility: hidden !important;
}

.jobbookmark.active {
  background-color: var(--main-color-one);
}

.jobbookmark.active svg path {
  fill: #fff;
}

.categorySub-padding {
  padding: 10px 0;
}

.categorySub-arrow {
  position: relative;
  width: 0;
}

.categorySub-arrow::before {
  content: "\f053";
  font-family: "Font Awesome 6 Free";
  font-weight: 700;
  position: absolute;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 32px;
  width: 32px;
  background-color: #fff;
  -webkit-box-shadow: 0 0 10px rgba(221, 221, 221, 0.7);
  box-shadow: 0 0 10px rgba(221, 221, 221, 0.7);
  font-size: 16px;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  z-index: 1;
}

.categorySub-arrow.right-arrow {
  margin-right: 22px;
}

.categorySub-arrow.right-arrow::before {
  content: "\f054";
}

.categorySub-arrow:hover::before {
  background-color: var(--main-color-one);
  color: #fff;
}

.categorySub-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  position: relative;
  position: unset;
}

.categorySub-list-slide {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  gap: 10px;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -ms-flex-line-pack: center;
  align-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow-x: scroll;
  width: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: visible;
  -ms-overflow-style: none;
  scrollbar-width: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.categorySub-list-slide::-webkit-scrollbar {
  height: 5px;
  background-color: var(--border-color);
  border-radius: 30px;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  display: none;
}

.categorySub-list-slide::-webkit-scrollbar-thumb {
  background-color: var(--main-color-one);
  border-radius: 30px;
  visibility: hidden;
  opacity: 0;
}

.categorySub-list-slide-list.open ul {
  visibility: visible;
  opacity: 1;
  display: block;
}

.categorySub-list-slide-list>a {
  white-space: nowrap;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  padding: 4px 12px;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  border: 1px solid var(--border-color);
  color: var(--paragraph-color);
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 0 10px rgba(221, 221, 221, 0.7);
  box-shadow: 0 0 10px rgba(221, 221, 221, 0.7);
  position: relative;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .categorySub-list-slide-list>a {
    padding: 4px 4px 4px 12px;
  }
}

.categorySub-list-slide-list>a:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.categorySub-list-slide-list>a:hover .mobileIcon {
  color: var(--main-color-one);
}

.categorySub-list-slide-list>a .mobileIcon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100%;
  width: 40px;
  background-color: #fff;
  border: 1px solid var(--border-color);
  font-size: 15px;
  z-index: 0;
  position: relative;
  display: none;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .categorySub-list-slide-list>a .mobileIcon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}

.categorySub-list-slide-list>a .mobileIcon::before {
  content: "\f107";
  font-family: "Font Awesome 6 Free";
  font-weight: 600;
}

.categorySub-list-slide-list.open>a .mobileIcon::before {
  content: "\f106";
}

.categorySub-list-slide-list ul {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -ms-flex-line-pack: start;
  align-content: flex-start;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid var(--border-color);
  list-style: none;
  padding: 0;
  visibility: hidden;
  opacity: 0;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  min-width: 100px;
  z-index: 22;
  display: none;
}

.categorySub-list-slide-list ul.open {
  visibility: visible;
  opacity: 1;
  display: block;
}

.categorySub-list-slide-list ul li:not(:last-child) a {
  border-bottom: 1px solid var(--border-color);
}

.categorySub-list-slide-list ul li a {
  white-space: nowrap;
  display: block;
  padding: 5px 20px;
  font-size: 15px;
  font-weight: 400;
  line-height: 24px;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  position: relative;
  color: var(--paragraph-color);
  z-index: 1;
}

.categorySub-list-slide-list ul li a:hover {
  color: #fff;
}

.categorySub-list-slide-list ul li a:hover::before {
  height: 100%;
}

.categorySub-list-slide-list ul li a::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0px;
  background-color: var(--main-color-one);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  z-index: -1;
}

 .categoryWrap-wrapper-item {
   background-color: #fff;
   padding: 20px;
   border-radius: 10px;
   overflow: hidden;
   box-shadow: 0px 15px 8px #efefef;
 }

.categoryWrap-wrapper-item.jobDetails-padding {
  padding: 0;
}

.categoryWrap-wrapper-item.jobDetails-padding .categoryWrap-wrapper-item-inner {
  padding: 20px;
}

.categoryWrap-wrapper-item.jobDetails-padding .categoryWrap-wrapper-item-bottom {
  background-color: #F9F9FB;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

.categoryWrap-wrapper-item:not(:last-child) {
  margin-bottom: 24px;
}

.categoryWrap-wrapper-item-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 20px 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.categoryWrap-wrapper-item-top-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.categoryWrap-wrapper-item-top-right-image {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  background-color: #f3f3f3;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.categoryWrap-wrapper-item-top-right-image svg path {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.categoryWrap-wrapper-item-top-right-image:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.categoryWrap-wrapper-item-top-right-image:hover svg {
  fill: #fff;
}

.categoryWrap-wrapper-item-top-right-image:hover svg path {
  fill: #fff;
}

.categoryWrap-wrapper-item-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  margin: -3px 0 0;
}

@media only screen and (max-width: 480px) {
  .categoryWrap-wrapper-item-title {
    font-size: 20px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 375px) {
  .categoryWrap-wrapper-item-title {
    font-size: 20px;
  }
}

.categoryWrap-wrapper-item-para {
  font-size: 16px;
  margin-top: 10px;
  color: var(--paragraph-color);
}
.categoryWrap-wrapper-item-para > span {
  color: var(--main-color-one);
  font-weight: 600;
}
.categoryWrap-wrapper-item-contents-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  font-size: 20px;
  color: var(--main-color-one);
  font-weight: 600;
}

.categoryWrap-wrapper-item-contents-fixed {
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  border-radius: 30px;
  padding: 2px 12px 3px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.categoryWrap-wrapper-item-contents-review {
  font-size: 16px;
  color: var(--paragraph-color);
}

.categoryWrap-wrapper-item-contents-review i {
  font-size: 18px;
  color: #ffac16;
  margin-right: 5px;
}

.categoryWrap-wrapper-item-contents-tag {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin-bottom: 0;
}

.categoryWrap-wrapper-item-contents-tag-list {
  font-size: 14px;
  color: var(--body-color);
  padding: 4px 15px;
  line-height: 20px;
  border-radius: 30px;
  border: 1px solid #eeeeee;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.categoryWrap-wrapper-item-contents-tag-list:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.categoryWrap-wrapper-item-bottom-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px 30px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
}

.categoryWrap-wrapper-item-bottom-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
  font-size: 16px;
  color: var(--paragraph-color);
}
 .jobFilter-wrapper-item-bottom-list-item .item-icon,
.categoryWrap-wrapper-item-bottom-list-item .item-icon {
  font-size: 18px;
  color: var(--paragraph-color);
  line-height: 1;
}
 .jobFilter-wrapper-item-bottom-list-item .item-icon svg,
 .jobFilter-wrapper-item-bottom-list-item .item-icon img,
 .categoryWrap-wrapper-item-bottom-list-item .item-icon svg,
 .categoryWrap-wrapper-item-bottom-list-item .item-icon img {
   max-width: 16px;
 }

 .jobFilter-wrapper-item-bottom-list-item .item-icon svg path,
 .categoryWrap-wrapper-item-bottom-list-item .item-icon svg path {
   fill: var(--main-color-one);
 }
 .jobFilter-wrapper-item-bottom-list-item .item-para,
 .categoryWrap-wrapper-item-bottom-list-item .item-para {
   font-size: 14px;
   color: var(--paragraph-color);
   line-height: 25px;
   font-weight: 600;
   display: flex;
   align-items: center;
   gap: 7px;
 }

.categoryWrap-wrapper-item-bottom-list-item .item-para strong {
  color: var(--heading-color);
}

.categoryWrap-wrapper-item-tickets-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 20px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.categoryWrap-wrapper-item-tickets-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 65px;
  width: 65px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 50%;
}

.categoryWrap-wrapper-item-tickets-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.categoryWrap-wrapper-item-tickets-icon:hover svg path {
  fill: #fff;
}

.categoryWrap-wrapper-item-tickets-icon svg path {
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: var(--main-color-one);
}

.categoryWrap-wrapper-item-tickets-contents-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--main-color-one);
}

.categoryWrap-wrapper-item-tickets-contents-title sub {
  font-size: 16px;
  bottom: 0;
}

.categoryWrap-wrapper-item-completed {
  font-size: 16px;
  font-weight: 700;
}

.categoryWrap-wrapper-list {
  padding: 10px 15px;
  background-color: var(--border-color);
  margin: 0;
  list-style: none;
  border-radius: 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.categoryWrap-wrapper-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
  font-weight: 400;
  position: relative;
}

.categoryWrap-wrapper-list-item:not(:last-child) {
  margin-right: 20px;
  padding-right: 20px;
}

.categoryWrap-wrapper-list-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  height: 70%;
  width: 1px;
  background-color: var(--paragraph-color);
}

.categoryWrap-wrapper-list-item-icon {
  font-size: 18px;
  color: var(--paragraph-color);
}

.categoryWrap-wrapper-list-item-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
  font-weight: 400;
}

.categoryWrap-wrapper-list-item-para strong {
  font-weight: 600;
  color: var(--heading-color);
}

/* Project Category Css */
.project-category-item {
  background-color: #fff;
  padding: 24px;
  height: 100%;
  box-shadow: 0px 15px 10px #efefef;
}

.project-category-item:not(:first-child) {
  margin-top: 25px;
}

.project-category-item-browse-btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.project-category-item-browse-btn:hover {
  color: var(--main-color-one);
}

.project-category-item-viewAll {
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.project-category-item-viewAll:hover {
  color: var(--main-color-one);
}

.project-category-item-title {
  font-size: 24px;
  line-height: 32px;
  margin: -5px 0 0;
}

.project-category-item-plus {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  border: 1px solid #e3e3e3;
  font-size: 18px;
  cursor: pointer;
}

.project-category-item .single-project {
  padding: 0;
  border: unset;
  -webkit-box-shadow: unset;
  box-shadow: unset;
}

.project-category-item .single-project:hover {
  -webkit-box-shadow: unset;
  box-shadow: unset;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

.project-category-item-tab .tabs-two {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  background-color: var(--border-color);
  border-radius: 10px;
  padding: 5px;
}

.project-category-item-tab .tabs-two li {
  white-space: nowrap;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: var(--heading-color);
  padding: 10px 10px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-align: center;
  border-radius: 10px;
  font-size: 16px;
  line-height: 24px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-category-item-tab .tabs-two li {
    font-size: 15px;
  }
}

.project-category-item-tab .tabs-two li.active {
  background-color: #fff;
}

.single-shop-left .filter-lists {
  padding: 0;
  list-style: none;
}

.single-shop-left .filter-lists .list {
  position: relative;
  z-index: 2;
  display: block;
  padding-left: 30px;
  margin-top: 10px;
}

.single-shop-left .filter-lists .list.active::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "";
  background: var(--main-color-one);
  border-color: var(--main-color-one);
}

.single-shop-left .filter-lists .list a {
  font-size: 16px;
  line-height: 26px;
  color: #FFBA5C;
}

.single-shop-left .filter-lists .list::before {
  content: "";
  position: absolute;
  height: 20px;
  width: 20px;
  border: 1px solid rgba(221, 221, 221, 0.9);
  left: 0;
  top: 4px;
  background: none;
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 15px;
  color: #fff;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.price-range-input-flex {
  display: flex;
  align-items: center;
  flex: 1;
}

.price-range-input-flex input {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--paragraph-color);
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0.25rem;
  transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.price-range-input {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

span.price-range-separator {
  margin-inline: 5px;
  font-size: 24px;
  color: #b9b9b9;
}

.price-range-input-btn .btn-profile {
  font-size: 18px;
  padding: 6px 13.5px;
}

.project-background-img {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 250px;
  border-radius: 10px;
  display: inherit;
}

@media only screen and (max-width: 1399px) {
  .project-background-img {
    height: 500px;
  }
}

@media only screen and (max-width: 1199px) {
  .project-background-img {
    height: 500px;
  }
}

@media only screen and (max-width: 991.98px) {
  .project-background-img {
    height: 350px;
  }
}

@media only screen and (max-width: 767px) {
  .project-background-img {
    height: 300px;
  }
}

@media only screen and (max-width: 575px) {
  .project-background-img {
    height: 250px;
  }
}

@media only screen and (max-width: 425px) {
  .project-background-img {
    height: 180px;
  }
}

@media only screen and (max-width: 375px) {
  .project-background-img {
    height: 150px;
  }
}

.job_publicPrivate_view {
  display: inline-flex;
  width: auto;
  border: 1px solid var(--border-color);
  border-radius: 30px;
  padding: 2px 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: var(--paragraph-color);
}

.modal-content .alert,
.error-message .alert {
  padding: 7px 12px;
}

.custom_pagination .pagination {
  margin-bottom: 0;
}

.jobbookmark {
  background: unset;
  border: 0;
}

.jobbookmark:hover {
  background-color: unset;
}

.jobbookmark .click_to_bookmark:hover {
  background-color: var(--main-color-one);
}

.subsription-tabs .tab-parents {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.subsription-btn {
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  color: var(--paragraph-color);
  border: 1px solid var(--border-color);
  background-color: var(--white);
  padding: 7px 15px;
  border-radius: 5px;
  cursor: pointer;
  transition: all .3s;
}

.subsription-btn:hover,
.subsription-btn.active {
  background-color: var(--main-color-one);
  color: var(--white);
  border-color: var(--main-color-one);
}

.resend-verify-code-wrap strong {
  color: var(--heading-color);
  transition: all .3s;
}

.verify-account-wrapper {
  max-width: 600px;
  border: 1px solid var(--border-color);
  padding: 30px;
  border-radius: 10px;
  margin-inline: auto;
}

.resend-verify-code-wrap strong:hover {
  color: var(--main-color-one);
}

.select2-container--default .select2-selection--multiple {
  background-color: white;
  border-radius: 4px;
  cursor: text;
  padding-bottom: 5px;
  padding-right: 5px;
  position: relative;
  border: 1px solid var(--border-color) !important;
  outline: 0;
  height: 38px;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: solid 1px var(--border-color);
  outline: 0;
  height: 38px;
}

.select2-container--default .select2-selection--single {
  border: 1px solid var(--border-color) !important;
  height: 38px !important;
}

.wallet-balance {
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  color: var(--paragraph-color);
}

.balance {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
}

li>a,
h1>a,
h2>a,
h3>a,
h4>a,
h5>a,
h6>a,
p>a {
  color: inherit;
}

.single-freelancer-author-name>a {
  color: inherit;
}

.categorySub-list-slide-list>a {
  color: inherit;
}

.breadcrumb-contents-list-item>a {
  color: inherit;
}

.compareBtn {
  display: inline-block;
  font-size: 16px;
  font-weight: 500;
  color: var(--paragraph-color);
  border-bottom: 1px solid var(--border-color);
  transition: all .3s;
}

.compareBtn:hover {
  color: var(--main-color-one);
  border-color: var(--main-color-one);
}

 /* solve summernote content issue */
 .note-editable > p {
   max-width: 100%;
   word-break: break-all;
 }
 .note-editable > div {
   max-width: 100%;
   word-break: break-all;
   padding: 10px;
 }
 .note-editable > div pre {
   max-width: 100%;
   word-break: break-all;
   display: contents;
 }

 /* About css start */
 .about-bg {
   background-color: #F7F7F2;
 }
 .about-wrapper-left {
   padding-right: 50px;
 }
 @media (min-width: 1200px) and (max-width: 1399.98px) {
   .about-wrapper-left {
     padding-right: 30px;
   }
 }
 @media (min-width: 992px) and (max-width: 1199.98px) {
   .about-wrapper-left {
     padding-right: 30px;
   }
 }
 @media (min-width: 300px) and (max-width: 991.98px) {
   .about-wrapper-left {
     padding-right: 10px;
   }
 }
 .about-wrapper-thumb {
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   gap: 24px;
   -webkit-box-align: start;
   -ms-flex-align: start;
   align-items: flex-start;
 }
 @media only screen and (max-width: 375px) {
   .about-wrapper-thumb {
     display: -ms-grid;
     display: grid;
   }
 }
 .about-wrapper-thumb-item {
   -webkit-box-flex: 1;
   -ms-flex: 1;
   flex: 1;
   border-radius: 10px;
 }
 .about-wrapper-thumb-item:nth-child(2n+2) {
   margin-top: 50px;
 }
 @media only screen and (max-width: 375px) {
   .about-wrapper-thumb-item:nth-child(2n+2) {
     margin-top: 0;
   }
 }
 .about-wrapper-thumb-item img {
   border-radius: 10px;
 }
 /* About css end */
 /* About What css start */
.aboutWhat-wrapper iframe {
  width: 100%;
  height: 600px;
  border-radius: 20px;
}
@media screen and (max-width: 1199.98px) {
  .aboutWhat-wrapper iframe {
    height: 500px;
  }
}
@media screen and (max-width: 991.98px) {
  .aboutWhat-wrapper iframe {
    height: 400px;
  }
}
@media screen and (max-width: 767.98px) {
  .aboutWhat-wrapper iframe {
    height: 290px;
  }
}
@media screen and (max-width: 575px) {
  .aboutWhat-wrapper iframe {
    height: auto;
  }
}
 .aboutWhat-wrapper-thumb {
   -webkit-box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
   box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
   border-radius: 10px;
   position: relative;
   z-index: 2;
   overflow: hidden;
 }
.aboutWhat-wrapper-thumb:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, .7);
  z-index: 1;
}
.aboutWhat-wrapper-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  width: 80px;
  border: 2px solid var(--white);
  color: var(--white);
  z-index: 3;
  border-radius: 50%;
  font-size: 30px;
  transition: all .3s;
}
@media screen and (max-width: 575px) {
  .aboutWhat-wrapper-icon {
    height: 60px;
    width: 60px;
    font-size: 24px;
  }
}
@media screen and (max-width: 375px) {
  .aboutWhat-wrapper-icon {
    height: 50px;
    width: 50px;
    font-size: 20px;
  }
}
.aboutWhat-wrapper-icon:focus {
  border: 2px solid var(--white);
  color: var(--white);
}
.aboutWhat-wrapper-icon:hover {
  background-color: var(--main-color-one);
  color: var(--white);
  border-color: var(--main-color-one);
}
.aboutWhat-wrapper-thumb img {
   border-radius: 10px;
}
.vide-iframe {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}
/* About What css end */
/* About Credit css start */

.credit-item-title {
  font-size: 48px;
  font-weight: 400;
  line-height: 1.2;
  color: var(--heading-color);
  font-family: var(--Otomanopee-font);
}

@media screen and (max-width: 1199.98px) {
  .credit-item-title {
    font-size: 42px;
  }
}
@media screen and (max-width: 575px) {
  .credit-item-title {
    font-size: 32px;
  }
}
@media screen and (max-width: 375px) {
  .credit-item-title {
    font-size: 28px;
  }
}
.credit-item-para {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
  margin-top: 10px;
}
/* About Credit css end */
/* About Counter css start */
.about-counter {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 24px 0;
}
.about-counter-item {
  position: relative;
}

.about-counter-item:not(:last-child) {
  padding-right: 20px;
  margin-right: 20px;
}
@media screen and (min-width: 992px) and (max-width: 1199.98px) {
  .about-counter-item:not(:last-child) {
    padding-right: 12px;
    margin-right: 12px;
  }
}
.about-counter-item:not(:last-child):after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  height: 80%;
  width: 1px;
  background-color: var(--border-color);
}
.about-counter-item-title {
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
  color: var(--main-color-one);
  font-family: var(--Otomanopee-font);
}
.about-counter-item-para {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
  margin-top: 10px;
}
/* About Counter css end */
/* About Mission css start */
.aboutMission-wrapper-thumb img {
   border-radius: 10px;
   max-width: 100%;
   width: 100%;
 }
 .aboutMission-wrapper-contents {
   padding-left: 0px;
 }
 @media (min-width: 1200px) and (max-width: 1399.98px) {
   .aboutMission-wrapper-contents {
     padding-left: 40px;
   }
 }
 @media (min-width: 992px) and (max-width: 1199.98px) {
   .aboutMission-wrapper-contents {
     padding-left: 30px;
   }
 }
 /* About Mission css end */
 /* About Numbers css start */
 .about-bg-2 {
   background-color: #EDF0F5;
 }
 .aboutNumber-item-icon {
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   -webkit-box-align: center;
   -ms-flex-align: center;
   align-items: center;
   -webkit-box-pack: center;
   -ms-flex-pack: center;
   justify-content: center;
   height: 50px;
   width: 50px;
   background-color: var(--white);
   border: 1px solid var(--border-color);
   font-size: 24px;
   border-radius: 4px;
 }
 .aboutNumber-item-icon img,
 .aboutNumber-item-icon svg {
   max-width: 70%;
 }
 .aboutNumber-item-title {
   font-size: 16px;
   font-weight: 700;
   line-height: 24px;
   color: var(--heading-color);
   font-family: var(--heading-font);
 }
 /* About Numbers css end */
 /* About Team css start */
 .aboutTeam-item-thumb {
   position: relative;
 }
 .aboutTeam-item-thumb img {
   border-radius: 10px;
 }
 .aboutTeam-item-contents {
 }
.aboutTeam-item-title {
  font-size: 24px;
  font-weight: 700;
  line-height: 28px;
  color: var(--heading-color);
}
.aboutTeam-item-para {
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  color: var(--body-color);
  margin-top: 8px;
  font-family: var(--heading-color);
}
 /* About Team css end */

 .header-global-search {
   width: 100%;
   max-width: 550px;
   position: fixed;
   left: 50%;
   transform: translateX(-50%);
   top: 40px;
   z-index: 999;
   visibility: hidden;
   opacity: 0;
   transition: all .3s;
   background: var(--white);
   padding: 12px;
   border-radius: 10px;
 }

 .header-global-search.active {
   visibility: visible;
   opacity: 1;
 }

.header-global-search.global-search-notUpdate {
  position: relative;
  visibility: unset;
  opacity: unset;
  transform: unset;
  max-width: 250px;
  top: 0;
  left: 0;
  padding: 0;
}
@media screen and (min-width: 992px) and (max-width: 1199.98px) {
  .header-global-search.global-search-notUpdate {
    max-width: 190px;
  }
}
@media screen and (max-width: 375px) {
  .header-global-search.global-search-notUpdate {
    max-width: 100%;
    margin-bottom: 5px;
  }
  .navbar-right-flex.global-search-notUpdate .navbar-right-item:first-child {
    width: 100%;
  }
  .navbar-right-flex.global-search-notUpdate {
    flex-wrap: wrap;
  }
}
.global-search-notUpdate .global-search-result-inner {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--white);
  box-shadow: 0 0 10px #dddddd;
  padding: 10px;
  border-radius: 10px;
  width: 340px;
  max-height: 500px;
  overflow-y: auto;
  margin-top: 10px;
  scrollbar-color: #ddd #f1f1f1;
  scrollbar-width: thin;
}
@media screen and (max-width: 375px) {
  .global-search-notUpdate .global-search-result-inner {
    left: -10px;
    max-width: 290px;
  }
}
 .header-global-search.active .display_search_result {
   background-color: var(--white);
 }
 .header-global-search.active .display_search_result > div {
   padding: 20px 0 0;
   height: fit-content;
   max-height: calc(100vh - 178px);
   overflow-y: auto;
   scrollbar-color: #ddd #f1f1f1;
   scrollbar-width: thin;
 }
 .header-global-search.active .display_search_result > div::-webkit-scrollbar {
   width: 6px;
   background-color: #f1f1f1;
 }
 .header-global-search.active .display_search_result > div::-webkit-scrollbar-thumb {
   border-radius: 20px;
   background-color: #ddd;
 }

.global-search-notUpdate .display_search_result > div::-webkit-scrollbar {
   width: 6px;
   background-color: #f1f1f1;
 }
.global-search-notUpdate .display_search_result > div::-webkit-scrollbar-thumb {
   border-radius: 20px;
   background-color: #ddd;
 }
 .header-global-search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 10px;
 }
 .header-global-search-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 20px;
  color: var(--heading-color);
}
 .header-global-search-label {
   font-size: 18px;
   font-weight: 500;
   line-height: 24px;
   color: var(--heading-color);
   margin-bottom: 8px;
 }
 .header-global-search-input {
   position: relative;

 }
 .header-global-search-input-inner {
  position: relative;
  width: 100%;
 }
.header-global-search-input-inner-icon {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 40px;
  align-items: center;
  display: flex;
  justify-content: center;
  border-right: 1px solid var(--border-color);
  font-size: 14px;
  color: var(--body-color);
}
 .header-global-search-input-inner > input {
   height: 38px;
   border: 1px solid var(--border-color);
   width: 100%;
   padding-right: 120px;
   padding-left: 50px;
 }

.global-search-notUpdate .header-global-search-input-inner > input {
   padding-right: 12px;
}

.header-global-search-input-inner > input:focus {
   box-shadow: none;
}
 .header-global-search-select {
  position: absolute;
  right: 5px;
  top: 1px;
  border-left: 1px solid var(--border-color);
  height: calc(100% - 2px);
}

.header-global-search-select select {
  border: 0;
  height: 100%;
  padding-inline: 10px;
}
.header-global-search-close {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 25px;
  width: 25px;
  font-size: 16px;
  line-height: 1;
  background-color: var(--danger-color);
  border: 1px solid var(--danger-color);
  color: var(--white);
  cursor: pointer;
  transition: all .3s;
  border-radius: 4px;
}
 .header-global-search-close:hover {
   background-color: var(--white);
   color: var(--danger-color);
 }

 .global-search-result-inner-title {
   font-size: 14px;
   font-weight: 700;
   line-height: 20px;
   color: var(--heading-color);
 }

 .global-search-result-inner-price {
   font-size: 14px;
   font-weight: 700;
   color: var(--main-color-one);
 }

 .global-search-result-inner-contents {
   display: flex;
   align-items: center;
   gap: 10px;
 }

 .global-search-result-inner-fixed {
   font-size: 14px;
   font-weight: 400;
   line-height: 20px;
   color: var(--review-color);
 }
 .global-search-result-inner-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}
.global-job-item {
  flex-direction: column;
}
 .global-search-result-inner-item:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
   padding-bottom: 10px;
   margin-bottom: 10px;
 }
 .global-search-result-inner-item-thumb {
  max-width: 80px;
  flex-shrink: 0;
}
.global-search-result-inner-item-thumb img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
.global-search-result-inner-item-contents {
  flex-grow: 1;
}
 .download-pdf-chat {
   display: inline-block;
   font-size: 14px;
   font-weight: 400;
   line-height: 20px;
   background: var(--main-color-one);
   border: 1px solid var(--main-color-one);
   color: var(--white);
   padding: 5px 10px;
   border-radius: 4px;
   transition: all .3s;
 }
 .download-pdf-chat:focus,
 .download-pdf-chat:hover {
   color: var(--main-color-one);
   background-color: unset;
   border-collapse: var(--main-color-one);
 }

 .user-details-manage-list .item strong {
   color: var(--heading-color);
 }.user-details-manage-list .item {
   color: var(--paragraph-color);
 }

.modal-body {
  max-height: calc(100vh - 180px);
  overflow-y: auto;
}
.select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 15px !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  margin-top: 4px !important;
  margin-bottom: 5px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice:first-child {
  margin-left: 15px !important;
}
.jobFilter-proposal-author-contents-review-icon {
  line-height: 1;
}
.jobFilter-proposal-author-contents-review-para {
  color: #ffac16;
  font-size: 16px;
  line-height: 24px;
}

.myOrder_single__block__title {
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  color: var(--heading-color);
}

button #send_proposal_load_spinner i,
button #order_create_load_spinner i,
button #job_edit_load_spinner i,
button #job_create_load_spinner i,
button #project_edit_load_spinner i,
button #project_create_load_spinner i {
  margin-left: 5px;
}
.notFoundParent {
  height: 100%;
  padding-block: 50px;
}
.notFound-wrapper {
  max-width: 450px;
  margin-inline: auto;
}
.notFoundThumb {
  max-width: 400px;
  margin-inline: auto;
}
.notFoundTitle {
  font-size: 36px;
  font-weight: 500;
  line-height: 1.2;
  color: var(--heading-color);
  font-family: var(--Otomanopee-font);
}
.notFoundPara {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: var(--paragraph-color);
}
.btn-primary {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
}
.btn-primary:hover {
  color: #fff;
  background-color: rgba(var(--main-color-one-rgb), .8);
  border-color: rgba(var(--main-color-one-rgb), .8);
}
.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

.form-control::placeholder {
  font-size: 14px;
}
.contact-info-item-para span {
  font-size: 14px;
}

.select2-container--default .select2-selection--multiple::placeholder {
  margin-top: 10px;
}

/*badge css*/
.level-badge-wrapper {
  position: relative;
  height: 60px;
  width: 60px;
}

.badge-title {
  left: 50%;
  font-size: 12px;
  position: absolute;
  line-height: 14px;
  color: #fff;
  top: 60%;
  transform: translate(-50%, -50%);
  font-weight:500
}
.freelancer-level-badge {
  position: absolute;
  right: -30px;
  bottom: 0;
}

.sticky {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 111;
  background: #fff;
  -webkit-box-shadow: 0px 2px 5px 0px rgba(104, 104, 104, 0.49);
  -moz-box-shadow: 0px 2px 5px 0px rgba(104, 104, 104, 0.49);
  box-shadow: 0px 2px 5px 0px rgba(104, 104, 104, 0.49);
}
.navbar-area {transition: all .3s}

@media screen and (max-width: 991.98px) {
  .wow {
    animation: none;
  }
}

.description-content > p {
  word-break: break-word;
}

.description-content2 > p {
  word-break: break-word;
}