# Doctrine Persistence

[![GitHub Actions][GA 3.3 image]][GA 3.3]
[![Code Coverage][Coverage 3.3 image]][CodeCov 3.3]

The Doctrine Persistence project is a library that provides common abstractions for object mapper persistence.

## More resources:

* [Website](https://www.doctrine-project.org/)
* [Documentation](https://www.doctrine-project.org/projects/doctrine-persistence/en/latest/index.html)
* [Downloads](https://github.com/doctrine/persistence/releases)

  [Coverage 3.3 image]: https://codecov.io/gh/doctrine/persistence/branch/3.3.x/graph/badge.svg
  [CodeCov 3.3]: https://codecov.io/gh/doctrine/persistence/branch/3.3.x
  [GA 3.3 image]: https://github.com/doctrine/persistence/workflows/Continuous%20Integration/badge.svg?branch=3.3.x
  [GA 3.3]: https://github.com/doctrine/persistence/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A3.3.x
