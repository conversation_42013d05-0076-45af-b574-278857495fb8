# Doctrine Event Manager

[![Build Status](https://github.com/doctrine/event-manager/workflows/Continuous%20Integration/badge.svg)](https://github.com/doctrine/event-manager/actions)
[![Scrutinizer Code Quality](https://scrutinizer-ci.com/g/doctrine/event-manager/badges/quality-score.png?b=1.2.x)](https://scrutinizer-ci.com/g/doctrine/event-manager/?branch=1.2.x)
[![Code Coverage](https://scrutinizer-ci.com/g/doctrine/event-manager/badges/coverage.png?b=1.2.x)](https://scrutinizer-ci.com/g/doctrine/event-manager/?branch=1.2.x)

The Doctrine Event Manager is a library that provides a simple event system.

## More resources:

* [Website](https://www.doctrine-project.org/)
* [Documentation](https://www.doctrine-project.org/projects/doctrine-event-manager/en/latest/)
* [Downloads](https://github.com/doctrine/event-manager/releases)
