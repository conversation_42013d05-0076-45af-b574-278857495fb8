[2025-07-30 10:46:15] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'u455517599_amr5150'@'localhost' (using password: YES) (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'u455517599_amr5150'@'localhost' (using password: YES) (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'u455517599_amr5150'@'localhost' (using password: YES) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=loca...', 'u455517599_amr5...', 'P@#$$w0rd-1', Array)
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'u455517599_amr5...', 'P@#$$w0rd-1', Array)
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1217): Illuminate\\Database\\Connection->getPdo()
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(486): Illuminate\\Database\\Connection->getReadPdo()
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#31 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#32 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#33 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#34 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#35 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#36 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#37 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#38 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-07-30 10:47:02] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'u455517599_amr5150'@'localhost' (using password: YES) (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'u455517599_amr5150'@'localhost' (using password: YES) (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'u455517599_amr5150'@'localhost' (using password: YES) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=loca...', 'u455517599_amr5...', 'P@#$$w0rd-1', Array)
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'u455517599_amr5...', 'P@#$$w0rd-1', Array)
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1217): Illuminate\\Database\\Connection->getPdo()
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(486): Illuminate\\Database\\Connection->getReadPdo()
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#31 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#32 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#33 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#34 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#35 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#36 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#37 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#38 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-07-30 10:49:23] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'u455517599_amr5150'@'localhost' (using password: YES) (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'u455517599_amr5150'@'localhost' (using password: YES) (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'u455517599_amr5150'@'localhost' (using password: YES) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=loca...', 'u455517599_amr5...', 'P@#$$w0rd-1', Array)
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'u455517599_amr5...', 'P@#$$w0rd-1', Array)
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1217): Illuminate\\Database\\Connection->getPdo()
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(486): Illuminate\\Database\\Connection->getReadPdo()
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#31 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#32 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#33 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#34 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#35 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#36 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#37 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#38 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-07-30 10:51:32] production.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare('select * from `...')
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#24 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-07-30 10:51:33] production.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare('select * from `...')
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#24 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-07-30 10:52:13] production.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->run()
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2705): Illuminate\\Database\\Connection->select()
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2693}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2693): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get()
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure:get_static_option():102}()
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember()
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call()
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(102): Illuminate\\Support\\Facades\\Facade::__callStatic()
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option()
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call()
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():909}()
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(909): array_walk()
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith()
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#29 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare()
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():405}()
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->run()
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2705): Illuminate\\Database\\Connection->select()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2693}()
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2693): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get()
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure:get_static_option():102}()
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember()
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call()
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(102): Illuminate\\Support\\Facades\\Facade::__callStatic()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option()
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call()
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider()
#24 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():909}()
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(909): array_walk()
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith()
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#31 {main}
"} 
[2025-07-30 10:52:52] production.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare('select * from `...')
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#24 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-07-30 10:52:54] production.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist (SQL: select * from `static_options` where `option_name` = site_force_ssl_redirection limit 1) at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'travel_offers1.static_options' doesn't exist at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare('select * from `...')
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(103): Illuminate\\Database\\Eloquent\\Builder->first()
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): {closure}()
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(408): Illuminate\\Cache\\Repository->remember('site_force_ssl_...', 600, Object(Closure))
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Helper\\helpers.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Providers\\AppServiceProvider.php(31): get_static_option('site_force_ssl_...')
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#24 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 41)
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(911): array_walk(Array, Object(Closure))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-07-30 11:24:03] production.ERROR: Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm. {"exception":"[object] (RuntimeException(code: 0): Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm. at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php:55)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('b\\x8B\\xAB\\x19\\xE9\\xDE\\xAD\\xAB^t\\xA7\\xB2\\x1D\\xEA\\xDE...', 'AES-256-CBC')
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build(Object(Closure))
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(856): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(841): Illuminate\\Container\\Container->make('encrypter', Array)
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1043): Illuminate\\Foundation\\Application->make('encrypter')
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(959): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(920): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(856): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(841): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-07-30 11:24:03] production.ERROR: Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm. {"exception":"[object] (RuntimeException(code: 0): Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm. at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php:55)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('b\\x8B\\xAB\\x19\\xE9\\xDE\\xAD\\xAB^t\\xA7\\xB2\\x1D\\xEA\\xDE...', 'AES-256-CBC')
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build(Object(Closure))
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(856): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(841): Illuminate\\Container\\Container->make('encrypter', Array)
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1043): Illuminate\\Foundation\\Application->make('encrypter')
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(959): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(920): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(856): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(841): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(239): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 E:\\xampp\\htdocs\\Travel_Offers\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}
"} 
[2025-07-30 11:25:24] production.ERROR: Unable to detect application namespace. {"view":{"view":"E:\\xampp\\htdocs\\Travel_Offers\\core\\resources\\views\\frontend\\layout\\partials\\navbar-variant\\navbar-01.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-2023692267 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1594</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-2023692267\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","global_static_field_data":"<pre class=sf-dump id=sf-dump-1798050876 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>site_favicon</span>\" => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"
  \"<span class=sf-dump-key>site_main_color_one</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#9c3030</span>\"
  \"<span class=sf-dump-key>site_main_color_two</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#000000</span>\"
  \"<span class=sf-dump-key>body_font_family</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Manrope</span>\"
  \"<span class=sf-dump-key>heading_font_family</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Plus Jakarta Sans</span>\"
  \"<span class=sf-dump-key>heading_font</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
  \"<span class=sf-dump-key>site_third_party_tracking_code</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>site_google_analytics</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>site_white_logo</span>\" => \"<span class=sf-dump-str title=\"2 characters\">33</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1798050876\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","home_variant_number":"<pre class=sf-dump id=sf-dump-1584917781 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1584917781\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","primary_menu":"<pre class=sf-dump id=sf-dump-44442368 data-indent-pad=\"  \"><span class=sf-dump-num>1</span>
</pre><script>Sfdump(\"sf-dump-44442368\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","navbar_number":"<pre class=sf-dump id=sf-dump-316825276 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-316825276\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_details":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Pages\\Entities\\Page</span> {<a class=sf-dump-ref>#1623</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home Page One</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">home-page-one</span>\"
    \"<span class=sf-dump-key>page_content</span>\" => \"<span class=sf-dump-str title=\"197 characters\">&lt;p&gt;asdaui sasd aosidj laksdj aklsdj alkfjsdoijqoi aslkd aslkdj asoidj asoidj asd jmoriopi posdf aspod kaspod jaspodij asdiopja siopdjasoid jaspodi jaspdas fdpasoqwe k rokasodk aspodk asdasd asd&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>page_builder_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>layout</span>\" => \"<span class=sf-dump-str title=\"16 characters\">home_page_layout</span>\"
    \"<span class=sf-dump-key>page_class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"
    \"<span class=sf-dump-key>breadcrumb_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>navbar_variant</span>\" => \"<span class=sf-dump-str title=\"2 characters\">01</span>\"
    \"<span class=sf-dump-key>footer_variant</span>\" => \"<span class=sf-dump-str title=\"2 characters\">01</span>\"
    \"<span class=sf-dump-key>visibility</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-10-26 08:33:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-01-17 08:22:19</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home Page One</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">home-page-one</span>\"
    \"<span class=sf-dump-key>page_content</span>\" => \"<span class=sf-dump-str title=\"197 characters\">&lt;p&gt;asdaui sasd aosidj laksdj aklsdj alkfjsdoijqoi aslkd aslkdj asoidj asoidj asd jmoriopi posdf aspod kaspod jaspodij asdiopja siopdjasoid jaspodi jaspdas fdpasoqwe k rokasodk aspodk asdasd asd&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>page_builder_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>layout</span>\" => \"<span class=sf-dump-str title=\"16 characters\">home_page_layout</span>\"
    \"<span class=sf-dump-key>page_class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"
    \"<span class=sf-dump-key>breadcrumb_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>navbar_variant</span>\" => \"<span class=sf-dump-str title=\"2 characters\">01</span>\"
    \"<span class=sf-dump-key>footer_variant</span>\" => \"<span class=sf-dump-str title=\"2 characters\">01</span>\"
    \"<span class=sf-dump-key>visibility</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-10-26 08:33:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-01-17 08:22:19</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">page_content</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">visibility</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"19 characters\">page_builder_status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"6 characters\">layout</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">page_class</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">navbar_variant</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"17 characters\">breadcrumb_status</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">footer_variant</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Unable to detect application namespace. at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1425)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(413): Illuminate\\Foundation\\Application->getNamespace()
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(298): Illuminate\\View\\Compilers\\ComponentTagCompiler->guessClassName('frontend.user-m...')
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(235): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentClass('frontend.user-m...')
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(220): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentString('frontend.user-m...', Array)
#4 [internal function]: Illuminate\\View\\Compilers\\ComponentTagCompiler->Illuminate\\View\\Compilers\\{closure}(Array)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(215): preg_replace_callback('/\\n            <...', Object(Closure), '<header class=\"...')
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(89): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileSelfClosingTags('<header class=\"...')
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(76): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileTags('<header class=\"...')
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(423): Illuminate\\View\\Compilers\\ComponentTagCompiler->compile('<header class=\"...')
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(256): Illuminate\\View\\Compilers\\BladeCompiler->compileComponentTags('<header class=\"...')
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(175): Illuminate\\View\\Compilers\\BladeCompiler->compileString('<header class=\"...')
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(62): Illuminate\\View\\Compilers\\BladeCompiler->compile('E:\\\\xampp\\\\htdocs...')
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\resources\\views\\frontend\\layout\\partials\\navbar.blade.php(4): Illuminate\\View\\View->render()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\resources\\views\\frontend\\layout\\master.blade.php(3): Illuminate\\View\\View->render()
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#31 E:\\xampp\\htdocs\\Travel_Offers\\core\\resources\\views\\frontend\\pages\\frontend-home.blade.php(4): Illuminate\\View\\View->render()
#32 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#33 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#34 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#35 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#36 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#37 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#38 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#39 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#40 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#41 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#42 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(875): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#44 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\SetLang.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\MaintainsMode.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\MaintainsMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\GlobalVariableMiddleware.php(82): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\GlobalVariableMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\CheckUserOnlineStatus.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckUserOnlineStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#61 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#73 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#89 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#90 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#91 {main}

[previous exception] [object] (RuntimeException(code: 0): Unable to detect application namespace. at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1425)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(413): Illuminate\\Foundation\\Application->getNamespace()
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(298): Illuminate\\View\\Compilers\\ComponentTagCompiler->guessClassName('frontend.user-m...')
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(235): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentClass('frontend.user-m...')
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(220): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentString('frontend.user-m...', Array)
#4 [internal function]: Illuminate\\View\\Compilers\\ComponentTagCompiler->Illuminate\\View\\Compilers\\{closure}(Array)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(215): preg_replace_callback('/\\n            <...', Object(Closure), '<header class=\"...')
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(89): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileSelfClosingTags('<header class=\"...')
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(76): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileTags('<header class=\"...')
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(423): Illuminate\\View\\Compilers\\ComponentTagCompiler->compile('<header class=\"...')
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(256): Illuminate\\View\\Compilers\\BladeCompiler->compileComponentTags('<header class=\"...')
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(175): Illuminate\\View\\Compilers\\BladeCompiler->compileString('<header class=\"...')
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(62): Illuminate\\View\\Compilers\\BladeCompiler->compile('E:\\\\xampp\\\\htdocs...')
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\storage\\framework\\views\\3c8efe41f8d31380fcbc745c977aadc6f8d89132.php(4): Illuminate\\View\\View->render()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\storage\\framework\\views\\10b72af7a3e8bbc40c237624ae2532b96cd4b8aa.php(3): Illuminate\\View\\View->render()
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#31 E:\\xampp\\htdocs\\Travel_Offers\\core\\storage\\framework\\views\\48fd65b7b156d314bd01304535beb7932f52c826.php(5): Illuminate\\View\\View->render()
#32 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#33 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#34 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#35 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#36 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#37 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#38 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#39 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#40 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#41 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#42 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(875): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#44 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\SetLang.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\MaintainsMode.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\MaintainsMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\GlobalVariableMiddleware.php(82): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\GlobalVariableMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\CheckUserOnlineStatus.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckUserOnlineStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#61 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#73 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#89 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#90 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#91 {main}
"} 
[2025-07-30 11:29:04] production.ERROR: Unable to detect application namespace. {"view":{"view":"E:\\xampp\\htdocs\\Travel_Offers\\core\\resources\\views\\frontend\\layout\\partials\\navbar-variant\\navbar-01.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1949137326 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1594</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1949137326\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","global_static_field_data":"<pre class=sf-dump id=sf-dump-1665874951 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>body_font_family</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Manrope</span>\"
  \"<span class=sf-dump-key>heading_font</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
  \"<span class=sf-dump-key>heading_font_family</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Plus Jakarta Sans</span>\"
  \"<span class=sf-dump-key>site_favicon</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>site_google_analytics</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>site_heading_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#2E7D32</span>\"
  \"<span class=sf-dump-key>site_main_color_one</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#1E88E5</span>\"
  \"<span class=sf-dump-key>site_main_color_two</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#0D47A1</span>\"
  \"<span class=sf-dump-key>site_paragraph_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#666666</span>\"
  \"<span class=sf-dump-key>site_rtl_enabled</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
  \"<span class=sf-dump-key>site_secondary_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#FF9800</span>\"
  \"<span class=sf-dump-key>site_third_party_tracking_code</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>site_white_logo</span>\" => \"<span class=sf-dump-str title=\"2 characters\">33</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1665874951\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","home_variant_number":"<pre class=sf-dump id=sf-dump-407980192 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-407980192\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","primary_menu":"<pre class=sf-dump id=sf-dump-1530235944 data-indent-pad=\"  \"><span class=sf-dump-num>1</span>
</pre><script>Sfdump(\"sf-dump-1530235944\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","navbar_number":"<pre class=sf-dump id=sf-dump-2114260238 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-2114260238\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_details":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Pages\\Entities\\Page</span> {<a class=sf-dump-ref>#2615</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home Page One</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">home-page-one</span>\"
    \"<span class=sf-dump-key>page_content</span>\" => \"<span class=sf-dump-str title=\"197 characters\">&lt;p&gt;asdaui sasd aosidj laksdj aklsdj alkfjsdoijqoi aslkd aslkdj asoidj asoidj asd jmoriopi posdf aspod kaspod jaspodij asdiopja siopdjasoid jaspodi jaspdas fdpasoqwe k rokasodk aspodk asdasd asd&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>page_builder_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>layout</span>\" => \"<span class=sf-dump-str title=\"16 characters\">home_page_layout</span>\"
    \"<span class=sf-dump-key>page_class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"
    \"<span class=sf-dump-key>breadcrumb_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>navbar_variant</span>\" => \"<span class=sf-dump-str title=\"2 characters\">01</span>\"
    \"<span class=sf-dump-key>footer_variant</span>\" => \"<span class=sf-dump-str title=\"2 characters\">01</span>\"
    \"<span class=sf-dump-key>visibility</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-10-26 08:33:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-01-17 08:22:19</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home Page One</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">home-page-one</span>\"
    \"<span class=sf-dump-key>page_content</span>\" => \"<span class=sf-dump-str title=\"197 characters\">&lt;p&gt;asdaui sasd aosidj laksdj aklsdj alkfjsdoijqoi aslkd aslkdj asoidj asoidj asd jmoriopi posdf aspod kaspod jaspodij asdiopja siopdjasoid jaspodi jaspdas fdpasoqwe k rokasodk aspodk asdasd asd&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>page_builder_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>layout</span>\" => \"<span class=sf-dump-str title=\"16 characters\">home_page_layout</span>\"
    \"<span class=sf-dump-key>page_class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"
    \"<span class=sf-dump-key>breadcrumb_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>navbar_variant</span>\" => \"<span class=sf-dump-str title=\"2 characters\">01</span>\"
    \"<span class=sf-dump-key>footer_variant</span>\" => \"<span class=sf-dump-str title=\"2 characters\">01</span>\"
    \"<span class=sf-dump-key>visibility</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-10-26 08:33:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-01-17 08:22:19</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">page_content</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">visibility</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"19 characters\">page_builder_status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"6 characters\">layout</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">page_class</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">navbar_variant</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"17 characters\">breadcrumb_status</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">footer_variant</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Unable to detect application namespace. at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1425)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(413): Illuminate\\Foundation\\Application->getNamespace()
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(298): Illuminate\\View\\Compilers\\ComponentTagCompiler->guessClassName('frontend.user-m...')
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(235): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentClass('frontend.user-m...')
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(220): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentString('frontend.user-m...', Array)
#4 [internal function]: Illuminate\\View\\Compilers\\ComponentTagCompiler->Illuminate\\View\\Compilers\\{closure}(Array)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(215): preg_replace_callback('/\\n            <...', Object(Closure), '<header class=\"...')
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(89): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileSelfClosingTags('<header class=\"...')
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(76): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileTags('<header class=\"...')
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(423): Illuminate\\View\\Compilers\\ComponentTagCompiler->compile('<header class=\"...')
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(256): Illuminate\\View\\Compilers\\BladeCompiler->compileComponentTags('<header class=\"...')
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(175): Illuminate\\View\\Compilers\\BladeCompiler->compileString('<header class=\"...')
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(62): Illuminate\\View\\Compilers\\BladeCompiler->compile('E:\\\\xampp\\\\htdocs...')
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\resources\\views\\frontend\\layout\\partials\\navbar.blade.php(4): Illuminate\\View\\View->render()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\resources\\views\\frontend\\layout\\master.blade.php(3): Illuminate\\View\\View->render()
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#31 E:\\xampp\\htdocs\\Travel_Offers\\core\\resources\\views\\frontend\\pages\\frontend-home.blade.php(4): Illuminate\\View\\View->render()
#32 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#33 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#34 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#35 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#36 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#37 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#38 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#39 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#40 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#41 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#42 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(875): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#44 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\SetLang.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\MaintainsMode.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\MaintainsMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\GlobalVariableMiddleware.php(82): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\GlobalVariableMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\CheckUserOnlineStatus.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckUserOnlineStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#61 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#73 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#89 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#90 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#91 {main}

[previous exception] [object] (RuntimeException(code: 0): Unable to detect application namespace. at E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1425)
[stacktrace]
#0 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(413): Illuminate\\Foundation\\Application->getNamespace()
#1 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(298): Illuminate\\View\\Compilers\\ComponentTagCompiler->guessClassName('frontend.user-m...')
#2 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(235): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentClass('frontend.user-m...')
#3 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(220): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentString('frontend.user-m...', Array)
#4 [internal function]: Illuminate\\View\\Compilers\\ComponentTagCompiler->Illuminate\\View\\Compilers\\{closure}(Array)
#5 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(215): preg_replace_callback('/\\n            <...', Object(Closure), '<header class=\"...')
#6 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(89): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileSelfClosingTags('<header class=\"...')
#7 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(76): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileTags('<header class=\"...')
#8 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(423): Illuminate\\View\\Compilers\\ComponentTagCompiler->compile('<header class=\"...')
#9 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(256): Illuminate\\View\\Compilers\\BladeCompiler->compileComponentTags('<header class=\"...')
#10 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(175): Illuminate\\View\\Compilers\\BladeCompiler->compileString('<header class=\"...')
#11 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(62): Illuminate\\View\\Compilers\\BladeCompiler->compile('E:\\\\xampp\\\\htdocs...')
#12 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#13 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#14 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#15 E:\\xampp\\htdocs\\Travel_Offers\\core\\storage\\framework\\views\\3c8efe41f8d31380fcbc745c977aadc6f8d89132.php(4): Illuminate\\View\\View->render()
#16 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#17 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#19 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#20 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#21 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#22 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#23 E:\\xampp\\htdocs\\Travel_Offers\\core\\storage\\framework\\views\\10b72af7a3e8bbc40c237624ae2532b96cd4b8aa.php(3): Illuminate\\View\\View->render()
#24 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#25 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#27 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#28 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#29 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#30 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#31 E:\\xampp\\htdocs\\Travel_Offers\\core\\storage\\framework\\views\\48fd65b7b156d314bd01304535beb7932f52c826.php(5): Illuminate\\View\\View->render()
#32 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('E:\\\\xampp\\\\htdocs...')
#33 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#34 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\xampp\\\\htdocs...', Array)
#35 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\xampp\\\\htdocs...', Array)
#36 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\xampp\\\\htdocs...', Array)
#37 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#38 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#39 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#40 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#41 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#42 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(875): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#43 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#44 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\SetLang.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\MaintainsMode.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\MaintainsMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\GlobalVariableMiddleware.php(82): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\GlobalVariableMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\xampp\\htdocs\\Travel_Offers\\core\\app\\Http\\Middleware\\CheckUserOnlineStatus.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckUserOnlineStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#61 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#73 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#89 E:\\xampp\\htdocs\\Travel_Offers\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#90 E:\\xampp\\htdocs\\Travel_Offers\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#91 {main}
"} 
