
 @font-face {
  font-family: "Otomanopee";
  src: url("../fonts/OtomanopeeOne-Regular.eot");
  src: url("../fonts/OtomanopeeOne-Regular.eot?#iefix") format("embedded-opentype"), url("../fonts/OtomanopeeOne-Regular.woff2") format("woff2"), url("../fonts/OtomanopeeOne-Regular.woff") format("woff"), url("../fonts/OtomanopeeOne-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  overflow-x: hidden;
}

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: none;
  -moz-osx-font-smoothing: grayscale;
  /* Firefox */
  -webkit-font-smoothing: antialiased;
  /* WebKit  */
}

body {
  margin: 0;
  color: var(--body-color);
  overflow-x: hidden;
  font-family: var(--body-font);
  font-size: 14px;
  line-height: 26px;
}

@media only screen and (max-width: 480px) {
  body {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  body {
    font-size: 14px;
  }
}

h1 {
  font-size: 64px;
  line-height: 1.0833333333;
}

h2 {
  font-size: 44px;
  line-height: 1.4444444444;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  h2 {
    font-size: 30px;
  }
}

@media only screen and (max-width: 575.98px) {
  h2 {
    font-size: 27px;
  }
}

@media only screen and (max-width: 480px) {
  h2 {
    font-size: 26px;
  }
}

@media only screen and (max-width: 375px) {
  h2 {
    font-size: 24px;
  }
}

h3 {
  font-size: 32px;
  line-height: 1.0833333333;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  h3 {
    font-size: 26px;
  }
}

@media only screen and (max-width: 575.98px) {
  h3 {
    font-size: 25px;
  }
}

@media only screen and (max-width: 480px) {
  h3 {
    font-size: 24px;
  }
}

@media only screen and (max-width: 375px) {
  h3 {
    font-size: 22px;
  }
}

h4 {
  font-size: 24px;
  line-height: 1.0833333333;
}

@media only screen and (max-width: 575.98px) {
  h4 {
    font-size: 23px;
  }
}

@media only screen and (max-width: 375px) {
  h4 {
    font-size: 21px;
  }
}

h5 {
  font-size: 20px;
  line-height: 1.2380952381;
}

@media only screen and (max-width: 375px) {
  h5 {
    font-size: 18px;
  }
}

h6 {
  font-size: 16px;
  line-height: 1.2380952381;
}

@media only screen and (max-width: 375px) {
  h6 {
    font-size: 16px;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
  margin: 0;
  -webkit-transition: 300ms;
  transition: 300ms;
}

p {
  color: var(--light-color);
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  hyphens: auto;
  margin-bottom: 0;
  line-height: 26px;
  font-size: 16px;
}

a {
  color: inherit;
  text-decoration: none;
  color: var(--extra-light-color);
  -webkit-transition: 300ms;
  transition: 300ms;
}

a,
a:hover,
a:focus,
a:active {
  text-decoration: none;
  outline: none;
  color: inherit;
}

pre {
  word-break: break-word;
}

a i {
  padding: 0 2px;
}

img {
  max-width: 100%;
}

.list-style-none {
  margin: 0;
  padding: 0;
  list-style: none;
}

ol {
  counter-reset: counter;
  padding-left: 0;
}

ol li:before {
  counter-increment: counter;
  content: counter(counter);
  font-weight: 500;
  margin-right: 10px;
}

span {
  display: inline-block;
}

.desktop-center {
  text-align: center;
}

.form--control {
  width: 100%;
}

.color-heading {
  color: var(--heading-color);
}

.bg-gray {
  background-color: var(--border-color);
}

.section-bg-1 {
  background-color: var(--section-bg-1);
}

.section-bg-base {
  background-color: var(--section-bg-base);
}

.section-bg-2 {
  background-color: var(--section-bg-2);
}

.section-bg-gradient {
  background: var(--bg-gradient);
}

.footer-bg-1 {
  background-color: var(--footer-bg-1);
}

.footer-bg-2 {
  background-color: var(--footer-bg-2);
}

.copyright-bg-1 {
  background-color: var(--copyright-bg-1);
}

.color-one {
  color: var(--main-color-one);
}

.color-completed {
  color: var(--success-color);
}

.bg-image {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.no-margin {
  margin: 0;
}

.lazyloads {
  display: block;
}

.center-text {
  text-align: center;
}

.flex-between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px 10px;
}

.form-message {
  font-size: 16px;
  line-height: 24px;
  width: 100%;
  padding: 15px 20px;
  border: 1px solid var(--border-color);
  border-radius: 7px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: var(--paragraph-color);
  -webkit-box-shadow: 0 0 10px transparent;
  box-shadow: 0 0 10px transparent;
}

.form-message:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.2);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.padding-10 {
  padding: 10px;
}

.padding-20 {
  padding: 20px;
}

.radius-20 {
  border-radius: 20px;
}

.radius-10 {
  border-radius: 10px;
}

.radius-5 {
  border-radius: 5px;
}

/* Preloader Css */
.preloader-inner {
  position: fixed;
  height: 100vh;
  width: 100%;
  left: 0%;
  top: 0%;
  z-index: 99990;
  background-color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.preloader-inner span {
  display: inline-block;
  text-transform: uppercase;
  text-align: center;
  font-size: 70px;
  font-weight: 700;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
  color: var(--heading-color);
  -webkit-text-stroke: 2px var(--heading-color);
}

@media only screen and (max-width: 480px) {
  .preloader-inner span {
    font-size: 50px;
  }
}

.preloader-inner span:nth-child(1) {
  -webkit-animation: scaling 1s linear infinite;
  animation: scaling 1s linear infinite;
}

.preloader-inner span:nth-child(2) {
  -webkit-animation: scaling 1s linear infinite 0.125s;
  animation: scaling 1s linear infinite 0.125s;
}

.preloader-inner span:nth-child(3) {
  -webkit-animation: scaling 1s linear infinite 0.25s;
  animation: scaling 1s linear infinite 0.25s;
}

.preloader-inner span:nth-child(4) {
  -webkit-animation: scaling 1s linear infinite 0.375s;
  animation: scaling 1s linear infinite 0.375s;
}

.preloader-inner span:nth-child(5) {
  -webkit-animation: scaling 1s linear infinite 0.5s;
  animation: scaling 1s linear infinite 0.5s;
}

.preloader-inner span:nth-child(6) {
  -webkit-animation: scaling 1s linear infinite 0.675s;
  animation: scaling 1s linear infinite 0.675s;
}

.preloader-inner span:nth-child(7) {
  -webkit-animation: scaling 1s linear infinite 0.75s;
  animation: scaling 1s linear infinite 0.75s;
}

@-webkit-keyframes scaling {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    color: var(--main-color-one);
    -webkit-text-stroke: 2px var(--main-color-one);
  }

  20% {
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
    color: var(--main-color-one);
    -webkit-text-stroke: 3px var(--main-color-one);
  }

  50% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    color: var(--main-color-one);
    -webkit-text-stroke: 2px var(--main-color-one);
  }
}

@keyframes scaling {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    color: var(--main-color-one);
    -webkit-text-stroke: 2px var(--main-color-one);
  }

  20% {
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
    color: var(--main-color-one);
    -webkit-text-stroke: 3px var(--main-color-one);
  }

  50% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    color: var(--main-color-one);
    -webkit-text-stroke: 2px var(--main-color-one);
  }
}

/*---------------------------------------
    ## Button
---------------------------------------*/
.btn-wrapper {
  display: block;
}

.cmn-btn {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 500;
  font-family: var(--body-font);
  display: inline-block;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  line-height: 34px;
  padding: 7px 35px;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

@media only screen and (max-width: 575.98px) {
  .cmn-btn {
    padding: 10px 25px;
    font-size: 15px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 375px) {
  .cmn-btn {
    padding: 5px 15px;
    font-size: 14px;
  }
}

.cmn-btn.btn-bg-1 {
  background: var(--main-color-one);
  color: #fff;
  border: 2px solid transparent;
}

.cmn-btn.btn-bg-1:hover {
  background: var(--secondary-color);
}

.cmn-btn.btn-bg-2 {
  background: var(--main-color-two);
  color: #fff;
  border: 2px solid transparent;
}

.cmn-btn.btn-bg-2:hover {
  background: var(--secondary-color);
}

.cmn-btn.btn-bg-secondary {
  background: var(--secondary-color);
  color: #000;
  border: 2px solid transparent;
}

.cmn-btn.btn-bg-secondary:hover {
  background: rgba(var(--secondary-color-rgb), 0.8);
}

.cmn-btn.btn-bg-gray {
  background-color: var(--border-color);
  color: var(--heading-color);
  border: 2px solid transparent;
}

.cmn-btn.btn-bg-gray:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.cmn-btn.btn-bg-white {
  background-color: #fff;
  color: var(--heading-color);
  border: 2px solid #fff;
}

.cmn-btn.btn-bg-white:hover {
  background-color: var(--main-color-one);
  border-color: #fff;
  color: #fff;
  border-radius: 30px;
}

.cmn-btn.btn-border {
  border: 1px solid var(--border-color);
}

.cmn-btn.btn-border:hover {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

.cmn-btn.btn-outline-1 {
  padding: 7px 35px;
  border: 2px solid var(--main-color-one);
}

.cmn-btn.btn-outline-1.color-one {
  color: var(--main-color-one);
}

.cmn-btn.btn-outline-1:hover {
  background: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .cmn-btn.btn-outline-1 {
    padding: 8px 23px;
    font-size: 15px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 375px) {
  .cmn-btn.btn-outline-1 {
    padding: 4px 13px;
    font-size: 14px;
  }
}

.cmn-btn.btn-outline-white {
  padding: 7px 35px;
  border: 2px solid #fff;
  color: #fff;
}

.cmn-btn.btn-outline-white:hover {
  background: #fff;
  color: var(--main-color-one);
}

@media only screen and (max-width: 575.98px) {
  .cmn-btn.btn-outline-white {
    padding: 8px 23px;
    font-size: 15px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 375px) {
  .cmn-btn.btn-outline-white {
    padding: 4px 13px;
    font-size: 14px;
  }
}

.cmn-btn.btn-medium {
  padding: 7px 20px;
}

@media only screen and (max-width: 375px) {
  .cmn-btn.btn-medium {
    padding: 5px 15px;
    font-size: 14px;
  }
}

.cmn-btn.btn-small {
  padding: 5px 15px;
}

@media only screen and (max-width: 375px) {
  .cmn-btn.btn-small {
    padding: 5px 12px;
    font-size: 14px;
  }
}

.center-text .flex-btn {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.flex-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px 20px;
}

.hire-btn {
  font-weight: 400;
  color: var(--light-color);
  font-size: 16px;
  border: 1px solid var(--border-color);
  padding: 7px 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.hire-btn::after {
  content: "\f061";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 16px;
}

.hire-btn:hover {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

/* btn profile */
.btn-profile {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 500;
  font-family: var(--body-font);
  display: inline-block;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  line-height: 24px;
  padding: 7px 20px;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  border: none;
}

@media only screen and (max-width: 575.98px) {
  .btn-profile {
    padding: 6px 20px;
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-profile {
    padding: 5px 15px;
    font-size: 14px;
  }
}

.btn-profile.btn-bg-1 {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-profile.btn-bg-1:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
}

.btn-profile.btn-bg-2 {
  background-color: #447e9b;
  color: #fff;
}

.btn-profile.btn-bg-2:hover {
  background-color: #479bc6;
}

.btn-profile.btn-bg-3 {
  background-color: #95b36e;
  color: #fff;
}

.btn-profile.btn-bg-3:hover {
  background-color: #6bac16;
}

.btn-profile.btn-bg-cancel {
  background-color: var(--danger-color);
  color: #fff;
}

.btn-profile.btn-bg-cancel:hover {
  background-color: rgba(var(--danger-color-rgb), 0.8);
}

.btn-profile.btn-outline-1 {
  border: 1px solid var(--main-color-one);
  padding: 6px 19px;
}

@media only screen and (max-width: 575.98px) {
  .btn-profile.btn-outline-1 {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-profile.btn-outline-1 {
    padding: 4px 15px;
  }
}

.btn-profile.btn-outline-1.color-one {
  color: var(--main-color-one);
}

.btn-profile.btn-outline-1:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-profile.btn-outline-gray {
  border: 1px solid var(--border-color);
  padding: 6px 19px;
}

.btn-profile.btn-outline-gray:hover {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .btn-profile.btn-outline-gray {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-profile.btn-outline-gray {
    padding: 4px 15px;
  }
}

.btn-profile.btn-medium {
  padding: 5px 15px;
  font-size: 15px;
}

.btn-profile.btn-small {
  padding: 5px 10px;
  font-size: 15px;
}

.btn-profile.btn-hover-danger:hover {
  background-color: var(--danger-color);
  color: #fff;
  border-color: var(--danger-color);
}

/* Btn Delete Edit */
.btn-crud {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 15px;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  border-radius: 5px;
}

.btn-crud.btn-bg-1 {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-crud.btn-bg-1:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
}

.btn-crud.btn-bg-cancel {
  background-color: var(--danger-color);
  color: #fff;
}

.btn-crud.btn-bg-cancel:hover {
  background-color: rgba(var(--danger-color), 0.5);
}

.btn-crud.btn-outline-1 {
  border: 1px solid var(--main-color-one);
  padding: 6px 19px;
}

@media only screen and (max-width: 575.98px) {
  .btn-crud.btn-outline-1 {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-crud.btn-outline-1 {
    padding: 4px 15px;
  }
}

.btn-crud.btn-outline-1.color-one {
  color: var(--main-color-one);
}

.btn-crud.btn-outline-1:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-crud.btn-outline-gray {
  border: 1px solid var(--border-color);
  padding: 6px 19px;
}

.btn-crud.btn-outline-gray:hover {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .btn-crud.btn-outline-gray {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-crud.btn-outline-gray {
    padding: 4px 15px;
  }
}

.btn-crud.btn-medium {
  padding: 5px 15px;
  font-size: 15px;
}

.btn-crud.btn-small {
  padding: 5px 10px;
  font-size: 15px;
}

.btn-crud.btn-hover-danger:hover {
  background-color: var(--danger-color);
  color: #fff;
  border-color: var(--danger-color);
}

/* Btn Notification */
.btn-notification {
  color: var(--paragraph-color);
  font-size: 15px;
  font-weight: 500;
  font-family: var(--body-font);
  display: inline-block;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  line-height: 20px;
  padding: 5px 10px;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.btn-notification.btn-bg-1 {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-notification.btn-bg-1:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
}

.btn-notification.btn-outline-1 {
  border: 1px solid var(--main-color-one);
  padding: 4px 9px;
}

.btn-notification.btn-outline-1.color-one {
  color: var(--main-color-one);
}

.btn-notification.btn-outline-1:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.btn-notification.btn-outline-gray {
  border: 1px solid var(--border-color);
  padding: 4px 9px;
}

.btn-notification.btn-outline-gray:hover {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .btn-notification.btn-outline-gray {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .btn-notification.btn-outline-gray {
    padding: 4px 15px;
  }
}

.btn-notification.btn-hover-danger:hover {
  background-color: var(--danger-color);
  color: #fff;
  border-color: var(--danger-color);
}

/* Mouse Cursor Css */
.mouse-move {
  position: fixed;
  left: 0;
  top: 0;
  pointer-events: none;
  border-radius: 50%;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  visibility: hidden;
}

.mouse-inner {
  margin-left: -3px;
  margin-top: -3px;
  width: 6px;
  height: 6px;
  z-index: 10000001;
  background-color: var(--main-color-one);
  -webkit-transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
  transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.mouse-inner.mouse-hover {
  margin-left: -30px;
  margin-top: -30px;
  width: 60px;
  height: 60px;
  background-color: var(--main-color-one);
  opacity: 0.1;
}

.mouse-outer {
  margin-left: -15px;
  margin-top: -15px;
  width: 30px;
  height: 30px;
  border: 2px solid var(--main-color-one);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 10000000;
  opacity: 0.5;
  -webkit-transition: all 0.08s ease-out;
  transition: all 0.08s ease-out;
}

.mouse-outer.mouse-hover {
  opacity: 0;
}

.mouse-cursor-two .mouse-inner {
  background-color: var(--secondary-color);
}

.mouse-cursor-two .mouse-inner.mouse-hover {
  background-color: var(--secondary-color);
}

.mouse-cursor-two .mouse-outer {
  border-color: var(--secondary-color);
}

/* Nice Select Style */
.js_nice_select {
  width: 100%;
  height: 55px;
  line-height: 55px;
  font-size: 15px;
  font-weight: 400;
  color: var(--paragraph-color);
  border: 1px solid var(--border-color);
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.01);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.01);
  -webkit-transition: 300ms;
  transition: 300ms;
  padding: 0 30px 0 15px;
  margin-bottom: 24px;
}

.js_nice_select:focus {
  border: 1px solid var(--body-color);
}

.js_nice_select .nice-select-search-box {
  height: 100%;
}

.js_nice_select .nice-select-search-box .nice-select-search {
  vertical-align: top;
}

.js_nice_select:after {
  border-bottom: 2px solid var(--body-color);
  border-right: 2px solid var(--body-color);
  height: 7px;
  margin-top: -2px;
  right: 20px;
  width: 7px;
}

@media only screen and (max-width: 375px) {
  .js_nice_select:after {
    right: 10px;
  }
}

.js_nice_select.open {
  border: 1px solid #ddd;
}

.js_nice_select.open .list {
  width: 100%;
}

.js_nice_select .list {
  width: 100%;
  margin-top: 10px;
}

.js_nice_select .list .selected.focus {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
}

.js_nice_select .list .option:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  font-weight: 500;
}

.js-select.select-style-two {
  display: block;
  color: var(--paragraph-color);
  border: 1px solid var(--body-color);
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.js-select.select-style-two.open::after {
  border-color: transparent transparent var(--paragraph-color);
  border-width: 0px 4px 5px 4px;
}

.js-select.select-style-two:after {
  border-color: var(--paragraph-color) transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: auto;
  right: 11px;
  margin-top: 0px;
  position: absolute;
  top: 50%;
  width: 0;
  /* IE 9 */
  -webkit-transform: rotate(0deg);
  /* Chrome, Safari, Opera */
  transform: rotate(0deg);
}

@media only screen and (max-width: 375px) {
  .js-select.select-style-two:after {
    right: 10px;
  }
}

/* Custom Form */
.single-flex-input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 24px;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

@media only screen and (max-width: 480px) {
  .single-flex-input {
    display: block;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}

.custom-form .single-flex-input:not(:first-child) {
  margin-top: 25px;
}

.custom-form .single-flex-input .single-input,
.custom-form .single-flex-input .form-group {
  margin-top: 0 !important;
}

.custom-form .single-flex-input .single-input .js_nice_select,
.custom-form .single-flex-input .form-group .js_nice_select {
  width: 100%;
}

.custom-form .single-input,
.custom-form .form-group {
  width: 100%;
}

.custom-form .single-input:not(:first-child),
.custom-form .form-group:not(:first-child) {
  margin-top: 25px;
}

.custom-form .single-input .label-title,
.custom-form .form-group .label-title {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  display: block;
  margin-bottom: 10px;
  color: var(--heading-color);
}

@media only screen and (max-width: 480px) {

  .custom-form .single-input .label-title,
  .custom-form .form-group .label-title {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {

  .custom-form .single-input .label-title,
  .custom-form .form-group .label-title {
    font-size: 14px;
  }
}

.custom-form .single-input .input-icon,
.custom-form .form-group .input-icon {
  position: absolute;
  bottom: 15px;
  left: 15px;
}

.custom-form .single-input .iti,
.custom-form .form-group .iti {
  width: 100%;
}


.custom-form .single-input .form--control,
.custom-form .single-input .form-control,
.custom-form .form-group .form--control,
.custom-form .form-group .form-control {
  -moz-appearance: textfield;
  width: 100%;
  height: 55px;
  line-height: 55px;
  padding: 0 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--paragraph-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.form-group .form-control,
.form-group .form--control {
  -moz-appearance: textfield;
  width: 100%;
  height: 55px;
  line-height: 55px;
  padding: 0 15px;
  border: 1px solid var(--border-color);
  outline: none;
  color: var(--paragraph-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.form-group .form-control:focus,
.form-group .form--control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.custom-form .single-input .form--control.input-padding-left,
.custom-form .single-input .form-control.input-padding-left,
.custom-form .form-group .form--control.input-padding-left,
.custom-form .form-group .form-control.input-padding-left {
  padding-left: 45px;
}

.custom-form .single-input .form--control#phone,
.custom-form .single-input .form-control#phone,
.custom-form .form-group .form--control#phone,
.custom-form .form-group .form-control#phone {
  width: 100%;
  padding-left: 50px;
}

.custom-form .single-input .form--control::-webkit-outer-spin-button,
.custom-form .single-input .form--control::-webkit-inner-spin-button,
.custom-form .single-input .form-control::-webkit-outer-spin-button,
.custom-form .single-input .form-control::-webkit-inner-spin-button,
.custom-form .form-group .form--control::-webkit-outer-spin-button,
.custom-form .form-group .form--control::-webkit-inner-spin-button,
.custom-form .form-group .form-control::-webkit-outer-spin-button,
.custom-form .form-group .form-control::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
  -moz-appearance: textfield;
}

.custom-form .single-input .form--control:focus,
.custom-form .single-input .form-control:focus,
.custom-form .form-group .form--control:focus,
.custom-form .form-group .form-control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

@media only screen and (max-width: 480px) {

  .custom-form .single-input .form--control,
  .custom-form .single-input .form-control,
  .custom-form .form-group .form--control,
  .custom-form .form-group .form-control {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {

  .custom-form .single-input .form--control,
  .custom-form .single-input .form-control,
  .custom-form .form-group .form--control,
  .custom-form .form-group .form-control {
    font-size: 14px;
  }
}

.custom-form .single-input .form-message,
.custom-form .form-group .form-message {
  width: 100%;
  line-height: 24px;
  padding: 5px 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--paragraph-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.custom-form .single-input .form-message:focus,
.custom-form .form-group .form-message:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.custom-form .single-input .form-message.textarea-height,
.custom-form .form-group .form-message.textarea-height {
  height: 100px;
}

.custom-form .single-input-icon,
.custom-form .form-group-icon {
  position: relative;
}

.custom-form .single-input-icon::after,
.custom-form .form-group-icon::after {
  content: "";
  position: absolute;
  height: 55px;
  width: 2px;
  background-color: #f3f3f3;
  bottom: 0;
  left: 40px;
}

.custom-form .single-input-icon .form--control,
.custom-form .single-input-icon .form-control,
.custom-form .form-group-icon .form--control,
.custom-form .form-group-icon .form-control {
  padding-left: 50px;
  position: relative;
}

.custom-form .submit-btn {
  margin-top: 25px;
}

.submit-btn {
  border: 2px solid var(--main-color-one);
  background-color: var(--main-color-one);
  color: #fff;
  padding: 12px 20px;
  -webkit-transition: 300ms;
  transition: 300ms;
}

.submit-btn:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.9);
}

.photo-uploaded {
  background-color: #f3f3f3;
  border-radius: 10px;
  border: 2px dashed #ddd;
  cursor: pointer;
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  padding: 10px;
}

.photo-uploaded-padding {
  padding: 20px;
}

.photo-uploaded.center-text .photo-uploaded-flex {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.photo-uploaded-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
}

.photo-uploaded-icon {
  font-size: 28px;
  color: var(--light-color);
}

.photo-uploaded-para {
  font-size: 16px;
  color: var(--paragraph-color);
}

.photo-uploaded-file {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  cursor: pointer;
  opacity: 0;
}

/* Toggle Password hide Show */
/*.single-input {*/
/*  position: relative;*/
/*}*/

.toggle-password {
  position: absolute;
  bottom: 13px;
  right: 20px;
  cursor: pointer;
}

.hide-icon {
  display: none;
}

.toggle-password.show-pass .hide-icon {
  display: block;
}

.toggle-password.show-pass .show-icon {
  display: none;
}

/* Ui Range Slider */
.price-range-slider {
  padding: 0 10px;
}

.price-range-slider .ui-range-slider.noUi-target {
  position: relative;
  border-radius: 3px;
  height: 3px;
}

.price-range-slider .ui-range-slider.noUi-target .noUi-handle {
  top: -11px;
  left: -3px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  z-index: 1;
  cursor: pointer;
  background: var(--main-color-one);
}

.price-range-slider .ui-range-slider.noUi-target .noUi-handle.noUi-handle-upper::before {
  background-color: var(--main-color-one);
}

.price-range-slider .ui-range-slider.noUi-target .noUi-handle::before {
  content: "";
  position: absolute;
  background: #fff;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: inline-block;
}

.price-range-slider .ui-range-slider.noUi-target .noUi-base .noUi-origin {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.price-range-slider .ui-range-slider.noUi-target .noUi-base .noUi-connect {
  background: var(--main-color-one);
}

.price-range-slider .ui-range-slider-footer {
  display: table;
  width: 100%;
  padding-top: 20px;
}

.price-range-slider .ui-range-slider-footer .ui-range-values {
  display: inline-block;
  color: var(--heading-color);
}

.price-range-slider .ui-range-slider-footer .ui-range-values .ui-price-title {
  font-size: 18px;
  font-weight: 400;
  margin-right: 10px;
  color: var(--light-color);
}

.price-range-slider .ui-range-slider-footer .ui-range-values .ui-range-value-min,
.price-range-slider .ui-range-slider-footer .ui-range-values .ui-range-value-max {
  display: inline-block;
  font-size: 18px;
  font-weight: 500;
  color: var(--main-color-one);
}

.noUi-background {
  background: #DDDDDD;
}

[dir=rtl] .price-range-slider .ui-range-slider.noUi-target .noUi-base .noUi-origin {
  right: auto;
  left: 0;
}

[dir=rtl] .price-range-slider .ui-range-slider.noUi-target .noUi-base .noUi-connect {
  background: var(--main-color-one);
}

/* Search Popup */
.search-open {
  font-size: 24px;
  cursor: pointer;
  position: relative;
  z-index: 9;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.search-open:hover {
  color: var(--main-color-one);
}

.search-bar {
  position: fixed;
  top: 100px;
  right: 0;
  left: 0;
  height: auto;
  z-index: 991;
  max-width: 600px;
  margin: 0 auto;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  /* IE 9 */
  -webkit-transform: scale(0.5);
  /* Chrome, Safari, Opera */
  transform: scale(0.5);
}

.search-bar.active {
  visibility: visible;
  opacity: 1;
  /* IE 9 */
  -webkit-transform: scale(1);
  /* Chrome, Safari, Opera */
  transform: scale(1);
}

.search-bar .menu-search-form {
  position: relative;
  z-index: 9;
  width: 100%;
  margin: 0 auto;
  border-radius: 0px;
}

.search-bar .menu-search-form .search-close {
  position: absolute;
  right: 0px;
  top: -40px;
  font-size: 24px;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  background: var(--border-color);
  color: #f00;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
}

.search-bar .menu-search-form .search-close:hover {
  color: #fff;
  background: #f00;
}

.search-bar .menu-search-form .item-search {
  width: 100%;
  height: 70px;
  background: none;
  border: 1px solid rgba(0, 0, 0, 0.2);
  outline: none;
  text-transform: capitalize;
  padding: 10px 20px;
  padding-right: 100px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 0px;
  color: var(--paragraph-color);
}

.search-bar .menu-search-form .item-search:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.2);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.search-bar .menu-search-form .item-search::-webkit-input-placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form .item-search::-moz-placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form .item-search:-ms-input-placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form .item-search::-ms-input-placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form .item-search::placeholder {
  color: var(--paragraph-color);
  font-size: 14px;
}

.search-bar .menu-search-form button {
  position: absolute;
  right: 0;
  height: 100%;
  padding: 0 30px;
  background: var(--main-color-one);
  border: 0;
  outline: none;
  font-size: 24px;
  color: #fff;
  cursor: pointer;
}

.search-overlay {
  height: 100%;
  width: 100%;
  left: 100%;
  top: 0;
  left: 0;
  position: fixed;
  z-index: 99;
  cursor: -webkit-zoom-out;
  cursor: zoom-out;
  -webkit-transition: all 0.6s;
  transition: all 0.6s;
  visibility: hidden;
  opacity: 0;
}

.search-overlay.active {
  background: rgba(0, 0, 0, 0.7);
  visibility: visible;
  opacity: 1;
}

/* Search Suggestions */
.search-suggestions {
  padding: 20px;
  display: none;
}

.search-suggestions.active {
  display: block;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  right: 0;
  width: 100%;
  background: #fff;
  padding: 30px 20px;
  -webkit-box-shadow: 0 0 10px rgba(221, 221, 221, 0.4);
  box-shadow: 0 0 10px rgba(221, 221, 221, 0.4);
  visibility: hidden;
  opacity: 0;
  height: calc(100vh - 180px);
  overflow-y: auto;
  scrollbar-color: var(--body-color) var(--border-color);
  scrollbar-width: thin;
}

.search-suggestions::-webkit-scrollbar {
  width: 5px;
  background-color: var(--border-color);
}

.search-suggestions::-webkit-scrollbar-thumb {
  background-color: var(--body-color);
  border-radius: 10px;
}

@media (min-width: 1600px) and (max-width: 1849.98px) {
  .search-suggestions {
    height: 475px;
  }
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .search-suggestions {
    height: 475px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .search-suggestions {
    height: 475px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .search-suggestions {
    height: 475px;
    padding: 30px 10px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .search-suggestions {
    height: 485px;
  }
}

@media only screen and (max-width: 480px) {
  .search-suggestions {
    padding: 30px 10px;
  }
}

.search-suggestions.active {
  visibility: visible;
  opacity: 1;
}

.search-suggestions-title {
  font-size: 18px;
  width: 100%;
  background-color: rgba(221, 221, 221, 0.2);
  padding: 5px 10px;
}

.product-suggestion-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.product-suggestion-list-item:not(:last-child) {
  margin-bottom: 20px;
}

.product-suggestion-list-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 20px;
}

@media only screen and (max-width: 480px) {
  .product-suggestion-list-link {
    display: block;
  }
}

.product-suggestion-list-link .product-image {
  border: 1px solid rgba(221, 221, 221, 0.5);
  padding: 5px;
  height: 100px;
  width: 100px;
}

.product-suggestion-list-link .product-image img {
  height: 100%;
  width: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-suggestion-list-link .product-image {
    width: 80px;
    height: 100px;
    padding: 10px 0px;
  }
}

.product-info {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media only screen and (max-width: 480px) {
  .product-info .product-info-top {
    margin-top: 10px;
  }
}

.product-info .product-info-top .product-name {
  font-size: 18px;
  line-height: 28px;
  -webkit-transition: 300ms;
  transition: 300ms;
}

.product-info .product-info-top .product-name:hover {
  color: var(--main-color-one);
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .product-info-top .product-name {
    font-size: 17px;
  }
}

.product-info .product-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 20px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .product-price {
    gap: 7px;
  }
}

.product-info .product-price .price-update-through {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.product-info .product-price .price-update-through .flash-price {
  font-size: 18px;
  color: var(--heading-color);
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .product-price .price-update-through .flash-price {
    font-size: 16px;
  }
}

.product-info .product-price .price-update-through .flash-old-prices {
  font-size: 14px;
  text-decoration: line-through;
}

.product-info .product-price .main-price {
  font-size: 16px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .product-price .main-price {
    font-size: 17px;
  }
}

.product-info .stock-out {
  font-size: 16px;
  background: #ff0000;
  color: #fff;
  padding: 5px 10px;
  display: inline-block;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .product-info .stock-out {
    font-size: 15px;
    padding: 5px 5px;
  }
}

/* Hover Tooltip Active Css */
.hover-question {
  position: relative;
  z-index: 5;
}

.hover-question:hover .hover-active-content {
  visibility: visible;
  opacity: 1;
  bottom: 110%;
}

.hover-active-content {
  display: inline-block;
  padding: 5px 10px;
  background-color: #f3f3f3;
  color: var(--heading-color);
  position: absolute;
  bottom: 10%;
  left: 0;
  z-index: 9;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 3px;
  min-width: 135px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 250px;
}

.checkbox-inline {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  cursor: pointer;
  gap: 10px;
}

.checkbox-inline .check-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 18px;
  width: 18px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dddddd;
  border-radius: 0px;
  margin-top: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.checkbox-inline .check-input::after {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 10px;
  color: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transform: scale(1.6) rotate(90deg);
  transform: scale(1.6) rotate(90deg);
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.checkbox-inline .check-input:checked {
  background: var(--main-color-one);
  border-color: var(--main-color-one);
  background: var(--main-color-one);
}

.checkbox-inline .check-input:checked::after {
  visibility: visible;
  opacity: 1;
  -webkit-transform: scale(1.2) rotate(0deg);
  transform: scale(1.2) rotate(0deg);
}

.checkbox-inline .checkbox-label {
  cursor: pointer;
  text-align: left;
  line-height: 26px;
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
  margin: 0;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media only screen and (max-width: 575.98px) {
  .checkbox-inline .checkbox-label {
    font-size: 15px;
  }
}

.checkbox-inline .checkbox-label a {
  color: var(--main-color-one);
  font-weight: 500;
}

/* Dashboard area */
.dashboard-area {
  max-width: 1920px;
  margin: auto;
  height: 100vh;
}

.padding-20 {
  padding: 20px;
}

.profile-border-top {
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
  margin-top: 20px;
}

.profile-border-bottom {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.color-profile {
  color: var(--main-color-one);
}

.badge-notification {
  font-size: 10px;
  line-height: 14px;
  padding: 5px 5px;
  background: var(--main-color-one);
  color: #fff;
  border-radius: 3px;
  word-break: break-all;
  max-width: 80px;
}

.dashboard__contents__wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 35px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .dashboard__contents__wrapper {
    gap: 24px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__contents__wrapper {
    gap: 24px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__contents__wrapper {
    display: block;
  }
}

/*.dashboard__inner {*/
/*  width: 100%;*/
/*}*/

.dashboard__inner {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100vh;
}

.dashboard__left {
  background: var(--heading-color);
  text-align: center;
  padding: 0;
  display: block;
  padding: 40px 20px;
  width: 320px;
  height: 100vh;
  height: 100%;
  overflow-y: hidden;
  position: fixed;
  z-index: 99;
}

@media only screen and (max-width: 375px) {
  .dashboard__left {
    max-width: 280px;
  }
}

.dashboard__right {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-left: 320px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .dashboard__right {
    margin-left: 320px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__right {
    margin-left: 320px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__right {
    margin-left: 0;
  }
}

.dashboard__right__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__right__flex {
    display: block;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__right__flex {
    display: block;
  }
}

.dashboard__bottom {
  height: calc(100vh - 150px);
  overflow-y: auto;
  scrollbar-color: #fff var(--body-color);
  scrollbar-width: thin;
  padding-right: 5px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .dashboard__bottom {
    height: calc(100vh - 150px);
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__bottom {
    height: calc(100vh - 150px);
  }
}

.dashboard__bottom::-webkit-scrollbar {
  border-radius: 30px;
  width: 5px;
  background-color: var(--body-color);
}

.dashboard__bottom::-webkit-scrollbar-thumb {
  background-color: #fff;
  border-radius: 30px;
}

.dashboard__bottom__list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.dashboard__bottom__list__item.active>a {
  background: var(--main-color-one);
  font-weight: 500;
  color: #fff;
}

.dashboard__bottom__list__item.has-children.open>a::after {
  content: "\f068";
}

.dashboard__bottom__list__item.has-children>a {
  position: relative;
  padding-right: 35px;
}

.dashboard__bottom__list__item.has-children>a::after {
  content: "\f067";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  right: 15px;
  font-size: 16px;
}

.dashboard__bottom__list__item.show>.submenu {
  display: block;
}

.dashboard__bottom__list__item a {
  font-size: 16px;
  font-weight: 400;
  line-height: 18px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: rgba(255, 255, 255, 0.7);
  gap: 15px;
  padding: 14px 15px;
  cursor: pointer;
  border-radius: 10px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  text-align: left;
}

.dashboard__bottom__list__item>.submenu {
  padding-left: 20px;
  display: none;
  list-style: none;
  margin-top: 5px;
  border-left: 2px solid var(--main-color-one);
}

.dashboard__bottom__list__item>.submenu .submenu {
  border-left: 0;
}

.dashboard__bottom__list__item>.submenu .dashboard__bottom__list__item.has-children>a {
  font-size: 16px;
}

.dashboard__bottom__list__item>.submenu .dashboard__bottom__list__item.selected a {
  background: var(--main-color-one);
  font-size: 16px;
}

.dashboard__bottom__list__item>.submenu .dashboard__bottom__list__item.selected a::before {
  background: var(--main-color-one);
}

.dashboard__bottom__list__item>.submenu .dashboard__bottom__list__item a {
  font-size: 16px;
  line-height: 22px;
  padding: 5px 0 5px 15px;
  position: relative;
  font-weight: 400;
  gap: 7px;
  background: transparent;
  padding: 10px 15px;
  border-radius: 7px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .body-overlay {
    position: fixed;
    height: 100%;
    width: 100%;
    left: -100%;
    top: -100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 990;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: 0.6s;
    transition: 0.6s;
  }

  .body-overlay.active {
    visibility: visible;
    opacity: 1;
    left: 0%;
    top: 0%;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__left {
    z-index: 1;
    position: fixed;
    top: 0;
    left: -100%;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
    z-index: 991;
    background: var(--heading-color);
    text-align: center;
    border-radius: 10px;
    display: block;
    height: 100vh;
    transition: all 0.5s;
  }

  .dashboard__left.active {
    visibility: visible;
    opacity: 1;
    left: 0;
  }
}

@media only screen and (min-width: 300px) and (max-width: 991.98px) and (max-width: 480px) {
  .dashboard__left {
    width: 280px;
  }
}

.dashboard__left__close {
  position: absolute;
  right: 3px;
  top: 3px;
  color: var(--danger-color);
  background: #fff;
  -webkit-box-shadow: 0 0 10px rgba(var(--border-color), 0.5);
  box-shadow: 0 0 10px rgba(var(--border-color), 0.5);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 30px;
  width: 30px;
  font-size: 20px;
  border-radius: 5px;
  visibility: hidden;
  opacity: 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__left__close {
    visibility: visible;
    opacity: 1;
  }
}

.dashboard__icon {
  display: none;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__icon {
    display: block;
    margin-bottom: 20px;
    padding: 10px 10px 0;
  }
}

.dashboard__icon__bars {
  font-size: 24px;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 45px;
  width: 45px;
  font-size: 24px;
  background: var(--main-color-one);
  -webkit-box-shadow: 0 0 10px rgba(var(--border-color), 0.5);
  box-shadow: 0 0 10px rgba(var(--border-color), 0.5);
  z-index: 95;
  border-radius: 5px;
  color: #fff;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__icon__bars {
    height: 40px;
    width: 40px;
    border-radius: 0;
  }
}

.dashboard__header__flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  gap: 12px;
}

@media screen and (max-width: 480px) {
  .dashboard__header__flex {
    flex-direction: column;
    text-align: center;
    align-items: center;
    justify-content: center;
  }
}

.dashboard__header__left {
  flex: 1;
}

.dashboard__header__right {
  flex-shrink: 0;
}

.dashboard__search__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 18px;
  background-color: var(--border-color);
  border-radius: 50%;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.dashboard__search__icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.dash-btn-notification {
  color: var(--paragraph-color);
  font-size: 15px;
  font-weight: 500;
  font-family: var(--body-font);
  display: inline-block;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  line-height: 20px;
  padding: 5px 10px;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.dash-btn-notification.btn-bg-1 {
  background-color: var(--main-color-one);
  color: #fff;
}

.dash-btn-notification.btn-bg-1:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.8);
}

.dash-btn-notification.btn-outline-1 {
  border: 1px solid var(--main-color-one);
  padding: 4px 9px;
}

.dash-btn-notification.btn-outline-1.color-one {
  color: var(--main-color-one);
}

.dash-btn-notification.btn-outline-1:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.dash-btn-notification.btn-outline-gray {
  border: 1px solid var(--border-color-2);
  padding: 4px 9px;
}

.dash-btn-notification.btn-outline-gray:hover {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  color: #fff;
}

@media only screen and (max-width: 575.98px) {
  .dash-btn-notification.btn-outline-gray {
    padding: 5px 15px;
  }
}

@media only screen and (max-width: 375px) {
  .dash-btn-notification.btn-outline-gray {
    padding: 4px 15px;
  }
}

.dash-btn-notification.btn-hover-danger:hover {
  background-color: var(--danger-color);
  color: #fff;
  border-color: var(--danger-color);
}

.dashboard__notification {
  position: relative;
}

.dashboard__notification .dashboard__notification__wrapper.active {
  visibility: visible;
  opacity: 1;
  -webkit-transform: matrix(1, 0, 0, 1, 0, 0);
  transform: matrix(1, 0, 0, 1, 0, 0);
  top: 100%;
}

.dashboard__notification__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 18px;
  background-color: var(--border-color);
  border-radius: 50%;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.dashboard__notification__icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.dashboard__notification__wrapper {
  position: absolute;
  top: 120%;
  right: 0;
  z-index: 9998;
  width: 430px;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 0 10px var(--border-color);
  box-shadow: 0 0 10px var(--border-color);
  border-radius: 5px;
  overflow: hidden;
  border: 1px solid #fff;
  border-bottom: 4px solid var(--main-color-one);
  max-height: 450px;
  overflow-y: auto;
  scrollbar-color: var(--main-color-one) var(--border-color);
  scrollbar-width: thin;
}

@media screen and (max-width: 480px) {
  .dashboard__notification .dashboard__notification__wrapper.active {
    transform: translateX(22%);
    max-width: 320px;
  }
}

@media screen and (max-width: 375px) {
  .dashboard__notification .dashboard__notification__wrapper.active {
    transform: translateX(15%);
    max-width: 300px;
  }
}

.dashboard__notification__wrapper::-webkit-scrollbar {
  width: 5px;
  border-radius: 10px;
  background-color: var(--border-color);
}

.dashboard__notification__wrapper::-webkit-scrollbar-thumb {
  background-color: var(--main-color-one);
  border-radius: 10px;
}

.dashboard__notification__list {
  display: block;
  font-size: 15px;
  font-weight: 400;
  color: var(--heading-color);
  border-bottom: 1px solid var(--border-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.dashboard__notification__list__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px;
  background-color: var(--section-bg-2);
  padding: 20px;
}

.dashboard__notification__list__item.active {
  background-color: unset;
}

.dashboard__notification__list__item:not(:first-child) {
  padding-top: 20px;
}

.dashboard__notification__list__item:not(:last-child) {
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.dashboard__notification__list__thumb {
  max-width: 50px;
}

.dashboard__notification__list__thumb img {
  border-radius: 50%;
}

.dashboard__notification__list__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  font-size: 18px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 50%;
}

.dashboard__notification__list__icon.decline {
  background-color: var(--border-color);
  color: var(--paragraph-color);
}

.dashboard__notification__list__content {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.dashboard__notification__list__content__title {
  font-size: 15px;
  font-weight: 400;
  color: var(--heading-color);
  line-height: 24px;
}

.dashboard__notification__list__content__time {
  margin-top: 7px;
  font-size: 15px;
}

.dashboard__author {
  position: relative;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.dashboard__author:hover .dashboard__author__wrapper {
  visibility: visible;
  opacity: 1;
  top: 100%;
}

.dashboard__author__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  border: 1px solid var(--border-color);
  border-radius: 30px;
}

.dashboard__author__thumb {
  max-width: 40px;
}

.dashboard__author__thumb img {
  border-radius: 50%;
}

.dashboard__author__name {
  position: relative;
  font-size: 16px;
  font-weight: 500;
  color: var(--heading-color);
  padding-right: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.dashboard__author__name::after {
  content: "\f078";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 15px;
}

.dashboard__author__wrapper {
  position: absolute;
  top: 130%;
  right: 0;
  z-index: 9998;
  max-width: 200px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 0 10px var(--border-color);
  box-shadow: 0 0 10px var(--border-color);
  border-radius: 5px;
  overflow: hidden;
}

.dashboard__author__wrapper__list {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-color: var(--main-color-one) var(--border-color);
  scrollbar-width: thin;
}

.dashboard__author__wrapper__list::-webkit-scrollbar {
  width: 5px;
  background-color: var(--border-color);
}

.dashboard__author__wrapper__list::-webkit-scrollbar-thumb {
  background-color: var(--main-color-one);
}

.dashboard__author__wrapper__list__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 15px;
  line-height: 24px;
  font-weight: 400;
  padding: 10px 20px;
  color: var(--heading-color);
  border-bottom: 1px solid var(--border-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
  white-space: nowrap;
}

.dashboard__author__wrapper__list__item:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.dashboard__header {
  background-color: #fff;
  padding: 20px 40px;
  flex-shrink: 0;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__header {
    padding: 20px 30px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__header {
    padding: 20px 30px;
  }
}

@media only screen and (max-width: 575.98px) {
  .dashboard__header {
    padding: 20px 20px;
  }
}

@media only screen and (max-width: 375px) {
  .dashboard__header {
    padding: 20px 10px;
  }
}

.dashboard__header__title {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

.dashboard__header__para {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: var(--paragraph-color);
  display: block;
}

.dashboard__header__right__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.visitSite__btn {
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  border: 1px solid var(--main-color-one);
  background: var(--main-color-one);
  color: #fff;
  padding: 9px 20px;
  border-radius: 30px;
  transition: all .3s;
}

.visitSite__btn:hover {
  background-color: unset;
  color: var(--main-color-one);
  border-color: var(--main-color-one);
}

.dashboard__body {
  padding: 40px;
  background-color: var(--section-bg-2);
}

.dashboard__body {
  flex-grow: 1;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__body {
    padding: 30px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__body {
    padding: 30px;
  }
}

@media only screen and (max-width: 575.98px) {
  .dashboard__body {
    padding: 20px;
  }
}

@media only screen and (max-width: 375px) {
  .dashboard__body {
    padding: 10px;
  }
}

.dashboard__promo {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.dashboard__promo__row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.dashboard__promo__col {
  width: calc(25% - 18px);
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .dashboard__promo__col {
    width: calc(50% - 12px);
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__promo__col {
    width: calc(50% - 12px);
  }
}

@media only screen and (max-width: 575.98px) {
  .dashboard__promo__col {
    width: calc(100% - 0px);
  }
}

.promo_child:nth-child(4n+2) .single_promo {
  background: var(--promo-two);
}

.promo_child:nth-child(4n+3) .single_promo {
  background: var(--promo-three);
}

.promo_child:nth-child(4n+4) .single_promo {
  background: var(--promo-four);
}

.promo_child:nth-child(4n+5) .single_promo {
  background: var(--promo-five);
}

.promo_child:nth-child(4n+6) .single_promo {
  background: var(--promo-six);
}

.promo_child:nth-child(4n+7) .single_promo {
  background: var(--promo-seven);
}

.promo_child:nth-child(4n+8) .single_promo {
  background: var(--promo-eight);
}

.single_promo {
  background: var(--promo-one);
  padding: 24px;
  border-radius: 10px;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .single_promo {
    padding: 20px 15px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .single_promo {
    padding: 20px 20px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .single_promo {
    padding: 24px 20px;
  }
}

@media only screen and (max-width: 767.98px) {
  .single_promo {
    padding: 20px 15px;
  }
}

.single_promo__subtitle {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--paragraph-color);
}

.single_promo__title {
  font-size: 24px;
  line-height: 28px;
  font-weight: 600;
  color: var(--heading-color);
}

.dashboard__select .js_nice_select {
  height: 40px;
  line-height: 40px;
  border: 1px solid var(--border-color);
}

.dashboard__charts__title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
}

.dashboard__maps {
  position: sticky;
  top: 0;
  z-index: 9;
}

.dashboard__maps__footer__title {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--paragraph-color);
}

.dashboard__maps__footer__list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.dashboard__maps__footer__list_item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px 10px;
}

.dashboard__maps__footer__list_item:not(:first-child) {
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.dashboard__maps__footer__list_item:not(:last-child) {
  padding-bottom: 20px;
}

.dashboard__maps__footer__list__country {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--paragraph-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
}

.dashboard__maps__footer__list__count {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

.contents-map .mapael .dotmap {
  position: relative;
  background: none;
}

.contents-map .mapael .dotmap .plot {
  fill: var(--main-color-one);
}

.contents-map .mapael .dotmap .area {
  stroke: rgba(255, 255, 255, 0.7);
  fill: rgba(var(--main-color-one-rgb), 0.1);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.contents-map .mapael .dotmap .area:hover {
  fill: rgba(var(--main-color-one-rgb), 0.7);
}

.contents-map .mapael .mapTooltip {
  position: absolute;
  background-color: var(--main-color-one);
  border-radius: 5px;
  padding: 10px 20px;
  z-index: 1000;
  max-width: 200px;
  display: none;
  color: #fff;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.contents-map.contents-map-two .dotmap {
  background: none;
}

.contents-map.contents-map-two .dotmap .plot {
  fill: var(--secondary-color);
}

.contents-map.contents-map-two .dotmap .area {
  fill: rgba(var(--secondary-color-rgb), 0.1);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.contents-map.contents-map-two .dotmap .area:hover {
  fill: rgba(var(--secondary-color-rgb), 0.7);
}

.contents-map.contents-map-two .mapTooltip {
  background-color: var(--secondary-color);
}

.activities {
  background-color: #fff;
}

.activities-single {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.activities-single-header-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
  color: var(--heading-color);
}

.activities-single-table {
  overflow-x: auto;
}

.activities-single-table table {
  margin: 0;
}

.activities-single-table .table> :not(:first-child) {
  border-top: 0;
}

.activities-single-table thead {
  background-color: var(--section-bg-1);
  border-radius: 5px;
}

.activities-single-table thead tr th {
  padding: 20px 10px;
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
  white-space: nowrap;
}

.activities-single-table tbody {
  border-top: 0;
  background-color: #fff;
}

.activities-single-table tbody tr:not(:last-child) {
  border-bottom: 0;
}

.activities-single-table tbody tr:not(:last-child) td {
  border-bottom: 1px solid var(--border-color);
}

.activities-single-table tbody tr:last-child td {
  border: 0;
  padding-bottom: 0;
}

.activities-single-table tbody tr td {
  padding: 20px 10px;
  font-size: 15px;
  line-height: 20px;
  font-weight: 500;
  color: var(--paragraph-color);
  vertical-align: middle;
}

.activities-single-table .invoice_number {
  color: var(--main-color-one);
  font-weight: 500;
}

.activities-single-table .amount {
  color: var(--danger-color);
  font-weight: 500;
}

.activities-single-table .amount__added {
  color: var(--success-color);
}

.activities-single-table .balance {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 5px 15px;
  border: 1px solid var(--border-color);
  position: relative;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
  border-radius: 30px;
  white-space: nowrap;
}

.activities-single-table .balance::before {
  content: "$";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

.activities-single-table .balance__added {
  color: var(--success-color);
  border-color: var(--success-color);
}

.activities-single-table .source_balance strong {
  color: var(--heading-color);
}

.activities-single-table .status_successful {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 5px 15px;
  background-color: rgba(var(--success-color-rgb), 0.2);
  color: var(--success-color);
  border-radius: 30px;
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
}

.activities-single-table .status_successful.failed {
  background-color: rgba(var(--danger-color-rgb), 0.2);
  color: var(--danger-color);
}

.activities__footer {
  padding: 20px 10px;
}

.tab-style-one {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 15px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.tab-style-one li,
.tab-style-one .tab-list {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 28px;
  cursor: pointer;
  -webkit-transition: 300ms;
  transition: 300ms;
  padding-bottom: 10px;
  position: relative;
}

.tab-style-one li::before,
.tab-style-one .tab-list::before {
  content: "";
  position: absolute;
  height: 2px;
  width: 0%;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  bottom: 0;
  background-color: var(--main-color-one);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.tab-style-one li.active,
.tab-style-one .tab-list.active {
  color: var(--main-color-one);
  font-weight: 500;
}

.tab-style-one li.active::before,
.tab-style-one .tab-list.active::before {
  width: 100%;
}

.tab-style-two {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  background-color: var(--border-color);
  border-radius: 10px;
  padding: 5px;
  margin: 0;
}

.tab-style-two li {
  cursor: pointer;
  white-space: nowrap;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: var(--heading-color);
  padding: 10px 10px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-align: center;
  border-radius: 10px;
  font-size: 16px;
  line-height: 24px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .tab-style-two li {
    font-size: 15px;
  }
}

.tab-style-two li.active {
  background-color: #fff;
}

.dashboard-tab-content-item {
  display: none;
}

.dashboard-tab-content-item.active {
  display: block;
  -webkit-animation: 1s fade-effects;
  animation: 1s fade-effects;
}

@-webkit-keyframes fade-effects {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}

@keyframes fade-effects {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}

.dashboard__jobFilter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__jobFilter {
    display: block;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .dashboard__jobFilter.responsive-lg .shop-sidebar-content {
    width: 280px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__jobFilter.responsive-lg {
    display: block;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__jobFilter.responsive-lg .shop-sidebar-content {
    position: unset;
  }
}

.dashboard__jobFilter-right {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.dashboard__jobFilter .shop-sidebar-content {
  width: 330px;
  position: sticky;
  top: 0;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .dashboard__jobFilter .shop-sidebar-content {
    width: 300px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__jobFilter .shop-sidebar-content {
    width: 250px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__jobFilter .shop-sidebar-content {
    width: 375px;
    position: unset;
  }
}

.dashboard__jobFilter .single-shop-left {
  padding: 20px 20px;
  border: 1px solid var(--border-color);
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .dashboard__jobFilter .single-shop-left {
    padding: 22px 20px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .dashboard__jobFilter .single-shop-left {
    padding: 15px 20px 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__jobFilter .single-shop-left {
    padding: 15px 20px 20px;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__jobFilter .single-shop-left {
    padding: 15px 20px 20px;
  }
}

@media only screen and (max-width: 375px) {
  .dashboard__jobFilter .single-shop-left {
    padding: 10px;
  }
}

.dashboard__jobFilter .single-shop-left-inner {
  margin-top: 20px;
}

.dashboard__jobFilter .single-shop-left-inner .custom-form .single-input .form--control,
.dashboard__jobFilter .single-shop-left-inner .custom-form .single-input .form-control,
.dashboard__jobFilter .single-shop-left-inner .custom-form .form-group .form--control,
.dashboard__jobFilter .single-shop-left-inner .custom-form .form-group .form-control {
  height: 46px;
}

.dashboard__jobFilter .single-shop-left-title.open .title::after {
  -webkit-transform: rotate(0);
  transform: rotate(0);
}

.dashboard__jobFilter .single-shop-left-title .title {
  cursor: pointer;
  position: relative;
  font-size: 18px;
  line-height: 28px;
  font-weight: 700;
  margin: -2px 0 0;
}

.dashboard__jobFilter .single-shop-left-title .title::after {
  content: "\f106";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 16px;
  position: absolute;
  right: 0;
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
  -webkit-transition: all 300ms;
  transition: all 300ms;
}

.dashboard__jobFilter .single-shop-left-select {
  display: -ms-grid;
  display: grid;
}

.dashboard__jobFilter .single-shop-left-select .js_nice_select {
  height: 46px;
  line-height: 46px;
}

.dashboard__jobFilter .single-shop-left-select .js_nice_select::after {
  margin-top: -4px;
}

.dashboard__jobFilter .single-shop-left-list .item-search {
  position: relative;
  margin: 15px 0;
}

.dashboard__jobFilter .single-shop-left-list .item-search .form--control {
  width: 100%;
  height: 50px;
  line-height: 50px;
  padding: 0 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--light-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  padding-right: 40px;
}

.dashboard__jobFilter .single-shop-left-list .item-search .form--control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

@media only screen and (max-width: 480px) {
  .dashboard__jobFilter .single-shop-left-list .item-search .form--control {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .dashboard__jobFilter .single-shop-left-list .item-search .form--control {
    font-size: 14px;
  }
}

.dashboard__jobFilter .single-shop-left-list .item-search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  font-size: 20px;
  cursor: pointer;
}

.dashboard__jobFilter .single-shop-left-list .more-amenities {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  text-decoration: underline;
  margin-top: 15px;
  font-family: var(--heading-font);
}

.dashboard__jobFilter .single-shop-left-list .item {
  font-size: 16px;
  line-height: 30px;
  position: relative;
  text-align: left;
  z-index: 2;
  padding: 0px 30px 0px 0px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border-radius: 5px;
  border: 1px solid #ddd;
}

.dashboard__jobFilter .single-shop-left-list .item:not(:last-child) {
  margin-bottom: 10px;
}

.dashboard__jobFilter .single-shop-left-list .item::before {
  content: "";
  position: absolute;
  height: 22px;
  width: 22px;
  border: 1px solid #ddd;
  right: 10px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  background: none;
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border-radius: 50%;
}

.dashboard__jobFilter .single-shop-left-list .item a {
  display: block;
  padding: 8px 0 8px 10px;
}

.dashboard__jobFilter .single-shop-left-list .item.show>.submenu {
  display: block;
}

.dashboard__jobFilter .single-shop-left-list .item.active {
  border-color: var(--main-color-one);
}

.dashboard__jobFilter .single-shop-left-list .item.active::before {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  background: var(--main-color-one);
  border-color: var(--main-color-one);
}

.dashboard__jobFilter .single-shop-left-list .item.active>a {
  color: var(--heading-color);
  font-weight: 600;
}

.dashboard__jobFilter .single-shop-left-list .item:hover>a {
  color: var(--main-color-one);
}

.dashboard__jobFilter .single-shop-left-list .item:hover::before {
  border-color: var(--main-color-one);
}

.dashboard__jobFilter .single-shop-left-filter-title .title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
}

.dashboard__jobFilter .single-shop-left-filter-reset {
  font-size: 14px;
  font-weight: 500;
  color: var(--main-color-one);
  text-decoration: underline;
}

.dashboard__jobFilter .shop-icon {
  display: none;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__jobFilter .shop-icon {
    display: block;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__jobFilter .shop-icon-sidebar {
    font-size: 24px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    width: 40px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: var(--main-color-one);
    -webkit-box-shadow: 0 0 10px rgba(221, 221, 221, 0.5);
    box-shadow: 0 0 10px rgba(221, 221, 221, 0.5);
    z-index: 95;
    color: #fff;
    margin-bottom: 20px;
  }
}

/* Responsive lg Device */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .dash-responsive-overlay-lg {
    position: fixed;
    height: 100vh;
    width: 100%;
    top: 0;
    left: -100%;
    z-index: -1;
    visibility: hidden;
    opacity: 0;
    background: rgba(0, 0, 0, 0.7);
    -webkit-transition: 0.6s;
    transition: 0.6s;
  }

  .dash-responsive-overlay-lg.active {
    visibility: visible;
    opacity: 1;
    z-index: 99;
    left: 0;
  }
}

.dashboard__jobFilter .shop-close-content {
  background-color: #fff;
  padding: 20px;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__jobFilter .shop-close-content {
    z-index: 1;
    position: fixed;
    top: 0;
    right: -100%;
    -webkit-transform: translateX(130%);
    transform: translateX(130%);
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
    z-index: 99;
    background: #fff;
    width: 375px;
    display: block;
    padding: 60px 20px 20px;
    transition: all 0.5s;
    height: 100vh;
    overflow-y: auto;
    scrollbar-color: var(--body-color) var(--border-color);
    scrollbar-width: thin;
  }

  .dashboard__jobFilter .shop-close-content::-webkit-scrollbar {
    border-radius: 30px;
    width: 5px;
    background-color: var(--border-color);
  }

  .dashboard__jobFilter .shop-close-content::-webkit-scrollbar-thumb {
    background-color: var(--body-color);
    border-radius: 30px;
  }

  .dashboard__jobFilter .shop-close-content.active {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
    left: unset;
    right: 0;
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
  }
}

@media only screen and (min-width: 300px) and (max-width: 991.98px) and (max-width: 480px) {
  .dashboard__jobFilter .shop-close-content {
    max-width: 320px;
  }
}

@media only screen and (min-width: 300px) and (max-width: 991.98px) and (max-width: 375px) {
  .dashboard__jobFilter .shop-close-content {
    max-width: 290px;
    padding: 50px 10px 10px;
  }
}

.dashboard__jobFilter .shop-close-content-icon {
  visibility: hidden;
  opacity: 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__jobFilter .shop-close-content-icon {
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute;
    right: auto;
    left: 0px;
    top: 0;
    font-size: 24px;
    color: #fff;
    background: var(--main-color-one);
    -webkit-box-shadow: none;
    box-shadow: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    width: 40px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__jobFilter.responsive-lg .shop-icon {
    display: block;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__jobFilter.responsive-lg .shop-icon-sidebar {
    font-size: 20px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    width: 40px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: var(--main-color-one);
    -webkit-box-shadow: none;
    box-shadow: none;
    z-index: 95;
    color: #fff;
    margin-bottom: 20px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__jobFilter.responsive-lg .shop-close-content {
    z-index: 1;
    position: fixed;
    top: 0;
    right: 0%;
    -webkit-transform: translateX(130%);
    transform: translateX(130%);
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
    z-index: 99;
    background: #fff;
    width: 375px;
    display: block;
    padding: 60px 20px 20px;
    transition: all 0.5s;
    height: 100vh;
    overflow-y: auto;
    scrollbar-color: var(--border-color) var(--body-color);
    scrollbar-width: thin;
  }

  .dashboard__jobFilter.responsive-lg .shop-close-content::-webkit-scrollbar {
    border-radius: 30px;
    width: 5px;
    background-color: var(--border-color);
  }

  .dashboard__jobFilter.responsive-lg .shop-close-content::-webkit-scrollbar-thumb {
    background-color: var(--body-color);
    border-radius: 30px;
  }

  .dashboard__jobFilter.responsive-lg .shop-close-content.active {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
    left: unset;
    right: 0;
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
  }
}

.dashboard__jobFilter.responsive-lg .shop-close-content-icon {
  visibility: hidden;
  opacity: 0;
  height: 0;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dashboard__jobFilter.responsive-lg .shop-close-content-icon {
    height: 40px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .dashboard__jobFilter.responsive-lg .shop-close-content-icon {
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute;
    right: auto;
    left: 0px;
    top: 0;
    font-size: 24px;
    color: #fff;
    background: var(--main-color-one);
    -webkit-box-shadow: none;
    box-shadow: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    width: 40px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .dash-responsive-overlay {
    position: fixed;
    height: 100vh;
    width: 100%;
    top: 0;
    left: 0%;
    z-index: -1;
    visibility: hidden;
    opacity: 0;
    background: rgba(0, 0, 0, 0.7);
    -webkit-transition: 0.6s;
    transition: 0.6s;
  }

  .dash-responsive-overlay.active {
    visibility: visible;
    opacity: 1;
    z-index: 99;
    left: 0;
  }
}

.elipsis {
  position: relative;
}

.elipsis__icon {
  font-size: 16px;
  color: var(--paragraph-color);
  cursor: pointer;
  padding: 0 5px;
}

.elipsis__wrap {
  background-color: #fff;
  -webkit-box-shadow: 0 0 20px #f3f3f3;
  box-shadow: 0 0 20px #f3f3f3;
  position: absolute;
  bottom: 105%;
  right: 0;
  z-index: 9;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 5px;
  /* IE 9 */
  -webkit-transform: scale(0.7);
  /* Chrome, Safari, Opera */
  transform: scale(0.7);
}

.elipsis__wrap.show {
  visibility: visible;
  opacity: 1;
  /* IE 9 */
  -webkit-transform: scale(1);
  /* Chrome, Safari, Opera */
  transform: scale(1);
}

.elipsis__wrap__contents__icon {
  display: block;
  font-size: 15px;
  line-height: 24px;
  font-weight: 400;
  padding: 10px 20px;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
}

.elipsis__wrap__contents__icon:not(:first-child) {
  border-top: 1px solid var(--border-color);
}

.elipsis__wrap__contents__icon.editing {
  color: var(--main-color-one);
}

.elipsis__wrap__contents__icon.deleting {
  color: var(--danger-color);
}

.profile-wrapper-tabs {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.profile-wrapper-item {
  background-color: #fff;
  padding: 24px;
}

.profile-wrapper-item:not(:first-child) {
  margin-top: 25px;
}

.job-progress {
  display: inline-block;
  padding: 5px 15px;
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
  border: 1px solid transparent;
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.job-progress:hover {
  background-color: var(--success-color);
  color: #fff;
}

.job-progress.cancel {
  background-color: rgba(var(--danger-color-rgb), 0.1);
  color: var(--danger-color);
}

.job-progress.active {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  border-color: transparent;
  color: var(--main-color-one);
}

.job-progress.active:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.job-progress.active-outline {
  background-color: unset;
  border-color: var(--main-color-one);
  color: var(--main-color-one);
}

.job-progress.active-outline:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.job-progress.not-started {
  border-color: var(--border-color);
  background-color: var(--border-color);
  color: var(--heading-color);
}

.job-progress.not-started:hover {
  background-color: var(--border-color);
  color: var(--heading-color);
}

.fixed-btn {
  font-size: 14px;
  line-height: 23px;
  font-weight: 400;
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  border-radius: 30px;
  border-radius: 30px;
  padding: 2px 10px;
  display: inline-block;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.fixed-btn:hover {
  background-color: var(--secondary-color);
  color: var(--heading-color);
}

.fixed-btn.completed {
  border-color: var(--success-color);
  color: var(--success-color);
}

.fixed-btn.completed:hover {
  background-color: var(--success-color);
  color: #fff;
}

.fixed-btn.active {
  border-color: var(--main-color-one);
  color: var(--main-color-one);
}

.fixed-btn.active:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.fixed-btn.cancel {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.fixed-btn.cancel:hover {
  background-color: var(--danger-color);
  color: #fff;
}

.fixed-btn.report {
  border-color: var(--danger-color);
  color: #fff;
  background-color: var(--danger-color);
}

.fixed-btn.report:hover {
  background-color: unset;
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.fixed-btn.not-started {
  border-color: var(--border-color);
  color: var(--paragraph-color);
}

.fixed-btn.not-started:hover {
  background-color: var(--border-color);
  color: var(--heading-color);
}

.btn_profile_dropdown {
  position: relative;
  padding-right: 40px !important;
}

.btn_profile_dropdown:hover::after {
  border-color: #fff;
}

.btn_profile_dropdown:hover .btn_profile_dropdown__list {
  visibility: visible;
  opacity: 1;
}

.btn_profile_dropdown::after {
  content: "";
  position: absolute;
  right: 15px;
  top: 10px;
  font-size: 16px;
  height: 10px;
  width: 10px;
  border-right: 2px solid var(--body-color);
  border-bottom: 2px solid var(--body-color);
  background-color: transparent;
  /* IE 9 */
  -webkit-transform: rotate(45deg);
  /* Chrome, Safari, Opera */
  transform: rotate(45deg);
}

.btn_profile_dropdown__list {
  margin: 0;
  padding: 0;
  list-style: none;
  width: 100%;
  background-color: #fff;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 5;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.btn_profile_dropdown__list li {
  text-align: left;
  color: var(--paragraph-color);
  font-size: 15px;
  font-weight: 400;
  line-height: 24px;
  padding: 5px 20px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.btn_profile_dropdown__list li:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
}

.jobFilter-wrapper-item {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.jobFilter-wrapper-item:not(:last-child) {
  margin-bottom: 24px;
}

.jobFilter-wrapper-item-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 20px 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.jobFilter-wrapper-item-top-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.jobFilter-wrapper-item-top-right-image {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  background-color: #f3f3f3;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.jobFilter-wrapper-item-top-right-image svg path {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.jobFilter-wrapper-item-top-right-image:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.jobFilter-wrapper-item-top-right-image:hover svg {
  fill: #fff;
}

.jobFilter-wrapper-item-top-right-image:hover svg path {
  fill: #fff;
}

.jobFilter-wrapper-item-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  margin: -3px 0 0;
}

@media only screen and (max-width: 480px) {
  .jobFilter-wrapper-item-title {
    font-size: 22px;
    line-height: 28px;
  }
}

@media only screen and (max-width: 375px) {
  .jobFilter-wrapper-item-title {
    font-size: 20px;
  }
}

.jobFilter-wrapper-item-para {
  font-size: 16px;
  margin-top: 10px;
  color: var(--paragraph-color);
}

.jobFilter-wrapper-item-contents-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  color: var(--main-color-one);
  font-weight: 600;
}

.jobFilter-wrapper-item-contents-fixed {
  font-size: 14px;
  font-weight: 400;
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  border-radius: 30px;
  padding: 5px 15px;
}

.jobFilter-wrapper-item-contents-review {
  font-size: 16px;
  color: var(--paragraph-color);
}

.jobFilter-wrapper-item-contents-review i {
  font-size: 18px;
  color: #ffac16;
  margin-right: 5px;
}

.jobFilter-wrapper-item-contents-tag {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
}

.jobFilter-wrapper-item-contents-tag-list {
  font-size: 16px;
  color: var(--heading-color);
  padding: 5px 15px;
  border-radius: 30px;
  background-color: #eeeeee;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.jobFilter-wrapper-item-contents-tag-list:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.jobFilter-wrapper-item-bottom-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px 30px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
}

.jobFilter-wrapper-item-bottom-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 7px;
  font-size: 16px;
  color: var(--paragraph-color);
}

.jobFilter-wrapper-item-bottom-list-item .item-icon {
  font-size: 18px;
  color: var(--paragraph-color);
}

.jobFilter-wrapper-item-bottom-list-item .item-para {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 26px;
}

.jobFilter-wrapper-item-bottom-list-item .item-para strong {
  color: var(--heading-color);
}

.jobFilter-wrapper-item-bottom-list-item .item-para.color-one {
  color: var(--main-color-one);
}

.jobFilter-wrapper-item-tickets-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 20px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.jobFilter-wrapper-item-tickets-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 65px;
  width: 65px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-radius: 50%;
}

.jobFilter-wrapper-item-tickets-icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.jobFilter-wrapper-item-tickets-icon:hover svg path {
  fill: #fff;
}

.jobFilter-wrapper-item-tickets-icon svg path {
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: var(--main-color-one);
}

.jobFilter-wrapper-item-tickets-contents-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--main-color-one);
}

.jobFilter-wrapper-item-tickets-contents-title sub {
  font-size: 16px;
  bottom: 0;
}

.jobFilter-wrapper-item-completed {
  font-size: 16px;
  font-weight: 700;
}

.jobFilter-proposal-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.jobFilter-proposal-single {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.jobFilter-proposal-single:not(:first-child) {
  margin-top: 24px;
}

.jobFilter-proposal-single-title {
  font-size: 24px;
  font-weight: 500;
  color: var(--heading-color);
  line-height: 28px;
}

@media only screen and (max-width: 575.98px) {
  .jobFilter-proposal-single-title {
    font-size: 20px;
  }
}

@media only screen and (max-width: 375px) {
  .jobFilter-proposal-single-title {
    font-size: 18px;
  }
}

.jobFilter-proposal-single-contents {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
}

@media only screen and (max-width: 375px) {
  .jobFilter-proposal-single-contents {
    -ms-flex-preferred-size: unset;
    flex-basis: unset;
  }
}

.jobFilter-proposal-single-contents-item:not(:first-child) {
  margin-top: 15px;
}

.jobFilter-proposal-single-contents-para {
  color: var(--paragraph-color);
  font-size: 16px;
  line-height: 24px;
}

.jobFilter-proposal-single-uploads {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  text-decoration: underline;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  display: block;
}

.jobFilter-proposal-single-uploads:hover {
  color: var(--main-color-one);
}

.jobFilter-proposal-single-uploads:not(:first-child) {
  margin-top: 15px;
}

.jobFilter-proposal-author-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 20px 15px;
}

.jobFilter-proposal-author-thumb {
  width: 70px;
  height: 70px;
  border-radius: 50%;
}

.jobFilter-proposal-author-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.jobFilter-proposal-author-contents-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
}

.jobFilter-proposal-author-contents-subtitle {
  color: var(--paragraph-color);
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  margin-top: 7px;
}

.jobFilter-proposal-author-contents-subtitle span {
  display: inline-block;
  color: var(--heading-color);
  font-weight: 500;
}

.jobFilter-proposal-author-contents-review {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
}

.jobFilter-proposal-author-contents-review-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  background-color: rgba(255, 172, 22, 0.1);
  color: #ffac16;
  padding: 3px 7px;
}

.jobFilter-proposal-author-contents-review-para {
  color: #ffac16;
}

.jobFilter-proposal-author-contents-jobs {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 3px 10px;
  border: 1px solid var(--border-color);
  color: var(--paragraph-color);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  border-radius: 3px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.jobFilter-proposal-author-contents-jobs:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.jobFilter-proposal-offered-single {
  display: inline-block;
  position: relative;
}

.jobFilter-proposal-offered-single:not(:last-child) {
  padding-right: 15px;
  margin-right: 15px;
}

.jobFilter-proposal-offered-single:not(:last-child)::after {
  content: "";
  position: absolute;
  height: 70%;
  width: 1px;
  background-color: #ddd;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
}

.jobFilter-proposal-offered-single .offered {
  font-size: 15px;
  color: var(--paragraph-color);
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

.jobFilter-proposal-offered-single .offered-price {
  font-size: 18px;
  font-weight: 600;
  color: var(--main-color-one);
}

.jobFilter-proposal-offered-single .offered-days {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
}

.contract_single {
  background-color: #fff;
  padding: 20px;
  border-radius: 20px;
}

.contract_single:not(:first-child) {
  margin-top: 24px;
}

.contract_single__id {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
}

.contract_single__title {
  font-size: 24px;
  font-weight: 500;
  color: var(--heading-color);
  line-height: 28px;
}

.contract_single__para {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.contract_single__price {
  font-size: 24px;
  line-height: 28px;
  font-weight: 600;
}

.contract_single__price.color-one {
  color: var(--main-color-one);
}

.contract_single__right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.contract_single__date {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  position: relative;
}

.contract_single__date strong {
  color: var(--heading-color);
}

.contract_single__date strong.color-one {
  color: var(--main-color-one);
}

.contract_single__date__para {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--paragraph-color);
}

.contract_single__date:not(:first-child) {
  padding-left: 15px;
  margin-left: 15px;
}

.contract_single__date:not(:first-child)::before {
  content: "";
  position: absolute;
  height: 60%;
  width: 2px;
  background-color: var(--border-color);
  left: -1px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.contract_single__contractor {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.contract_single__contractor__thumb {
  max-width: 50px;
  border: 2px solid rgba(var(--main-color-one-rgb), 0.3);
  border-radius: 50%;
  position: relative;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.contract_single__contractor__thumb:not(:first-child) {
  margin-left: -10px;
}

.contract_single__contractor__thumb:hover {
  z-index: 2;
}

.contract_single__contractor__thumb:hover img {
  /* IE 9 */
  -webkit-transform: scale(1.3);
  /* Chrome, Safari, Opera */
  transform: scale(1.3);
}

.contract_single__contractor__thumb img {
  border-radius: 50%;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.contract_single__balance {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  padding: 10px;
  border-radius: 10px;
}

.contract_single__balance-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.contract_single__balance-price {
  font-size: 24px;
  line-height: 24px;
  font-weight: 600;
}

.contract_single__balance-para {
  color: var(--paragraph-color);
  font-size: 16px;
  margin-top: 7px;
}

.contract_single__balance-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 30px;
  width: 30px;
  font-size: 15px;
  border: 1px solid #ddd;
  border-radius: 50%;
}

.contract_single__milestone__item {
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 15px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.contract_single__milestone__item:not(:first-child) {
  margin-top: 24px;
}

.contract_single__milestone__item.remove .remove-milestone-contractor {
  visibility: visible;
  opacity: 1;
  display: block;
}

.contract_single__milestone__item .remove-milestone-contractor {
  visibility: hidden;
  opacity: 0;
  display: none;
}

div[class*=col]:nth-child(4n+2) .contract_single__balance {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
}

div[class*=col]:nth-child(4n+3) .contract_single__balance {
  background-color: rgba(var(--success-color-rgb), 0.1);
}

div[class*=col]:nth-child(4n+4) .contract_single__balance {
  background-color: rgba(185, 131, 255, 0.1);
}

.pending-approval {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: var(--border-color);
  color: var(--heading-color);
}

.pending-approval.active {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
}

.pending-approval.completed {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.pending-approval.cancel {
  background-color: rgba(var(--danger-color-rgb), 0.1);
  color: var(--danger-color);
}

.pending-approval.report {
  background-color: var(--danger-color);
  color: #fff;
}

.pending-approval.secondary-color {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
}

.myOrder_single:not(:last-child) {
  margin-bottom: 25px;
}

.myOrder_single__item:not(:last-child) {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.myOrder_single__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

.myOrder_single__invoice__title {
  font-size: 16px;
  line-height: 24px;
  color: var(--paragraph-color);
  font-weight: 500;
}

.myOrder_single__invoice__id {
  color: var(--main-color-one);
  font-weight: 600;
  font-size: 18px;
}

.myOrder_single__content {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.myOrder_single__content__id {
  color: var(--main-color-one);
  font-weight: 600;
  font-size: 16px;
}

.myOrder_single__content__title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
}

.myOrder_single__content__time {
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
}

.myOrder_single__content__para {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 400;
}

.myOrder_single__content__list {
  -webkit-columns: 250px 3;
  -moz-columns: 250px 3;
  columns: 250px 3;
  padding: 0;
  list-style: none;
}

.myOrder_single__content__list__item {
  padding-left: 20px;
  position: relative;
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myOrder_single__content__list__item:hover {
  color: var(--main-color-one);
}

.myOrder_single__content__list__item::before {
  content: "";
  position: absolute;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  left: 0;
  top: 8px;
}

.myOrder_single__block {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px 0;
}

.myOrder_single__block__item {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 12px;
}

.myOrder_single__block__item:not(:last-child) {
  padding-right: 30px;
  margin-right: 30px;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .myOrder_single__block__item:not(:last-child) {
    padding-right: 20px;
    margin-right: 20px;
  }
}

.myOrder_single__block__item:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
  right: 0;
  height: 70%;
  width: 1px;
  background-color: #ddd;
}

.myOrder_single__block__item__author {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.myOrder_single__block__item__author img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.myOrder_single__block__subtitle {
  font-size: 15px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.myOrder_single__block__title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .myOrder_single__block__title {
    display: -ms-grid;
    display: grid;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .myOrder_single__block__title {
    display: -ms-grid;
    display: grid;
  }
}

.myOrder_single__block__title .order-funded-btn {
  display: inline-block;
  padding: 5px 20px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  font-size: 15px;
  font-weight: 400;
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.myOrder_single__block__title .order-funded-btn:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.profile_wrapper__item {
  background-color: #fff;
  padding: 24px;
}

.profile_wrapper__item:not(:first-child) {
  margin-top: 25px;
}

.profile_wrapper__item__title {
  font-size: 24px;
  line-height: 32px;
  margin: -5px 0 0;
}

.profile_wrapper__item__single {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.profile_wrapper__content {
  margin-top: 25px;
}

.profile_wrapper__content__review {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
  padding: 5px 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 5px;
  gap: 5px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.profile_wrapper__content__title {
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
  margin: 15px 0 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.profile_wrapper__content__title:hover {
  color: var(--main-color-one);
}

.profile_wrapper__content__para {
  font-size: 14px;
  line-height: 24px;
  color: var(--paragraph-color);
  margin-top: 20px;
}

.profile_wrapper__content__price {
  font-size: 24px;
  color: var(--main-color-one);
  font-family: var(--heading-font);
  font-weight: 600;
}

.profile_wrapper__content__price s {
  font-size: 22px;
  font-weight: 400;
  color: var(--light-color);
}

.profile_wrapper__author__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 20px;
}

.profile_wrapper__author__thumb {
  max-width: 110px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  margin-inline: auto;
}

.profile_wrapper__author__thumb img {
  border-radius: 50%;
}

.profile_wrapper__author__status {
  display: inline-block;
  padding: 3px 15px 3px 30px;
  border: 1px solid var(--active-color);
  color: var(--active-color);
  line-height: 1;
  font-size: 16px;
  font-weight: 500;
  position: relative;
  border-radius: 30px;
}

.profile_wrapper__author__status::before {
  content: "";
  position: absolute;
  height: 8px;
  width: 8px;
  background-color: var(--active-color);
  border-radius: 50%;
  left: 15px;
  top: 50%;
  /* IE 9 */
  -webkit-transform: translateY(-50%);
  /* Chrome, Safari, Opera */
  transform: translateY(-50%);
}

.profile_wrapper__author__status.offline {
  border: 1px solid var(--light-color);
  color: var(--light-color);
}

.profile_wrapper__author__status.offline::before {
  background-color: var(--light-color);
}

.profile_wrapper__author__name {
  font-size: 20px;
  font-weight: 600;
}

.profile_wrapper__author__para {
  font-size: 15px;
  font-weight: 400;
  display: block;
}

.profile_wrapper__details {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px 0;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.profile_wrapper__details__edit {
  font-size: 20px;
  color: var(--main-color-one);
  margin-left: 18px;
  display: inline-block;
  cursor: pointer;
}

.profile_wrapper__details__para {
  font-size: 16px;
  color: var(--paragraph-color);
}

.profile_wrapper__details__single {
  display: inline-block;
}

.profile_wrapper__details__single:not(:first-child) {
  border-left: 1px solid #e3e3e3;
  padding-left: 20px;
}

.profile_wrapper__details__single:not(:last-child) {
  padding-right: 20px;
}

.profile_wrapper__details__single__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
}

.profile_wrapper__details__single__icon {
  font-size: 18px;
}

.profile_wrapper__details__single__title {
  font-size: 18px;
}

.profile_wrapper__details__single__price {
  font-size: 24px;
  line-height: 32px;
  color: var(--main-color-one);
}

.profile_wrapper__details__single__price sub {
  font-size: 14px;
  margin: 0;
  bottom: 0;
  padding: 0px 10px 2px;
  border: 1px solid var(--secondary-color);
  color: var(--secondary-color);
  line-height: 16px;
  border-radius: 5px;
}

.profile_wrapper__details__single__flag {
  max-width: 35px;
}

.profile_wrapper__details__single__flag img {
  border-radius: 50%;
}

.profile_wrapper__details__single__thumb {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  border-radius: 50%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.profile_wrapper__details__single__contents__title {
  font-size: 20px;
  font-weight: 600;
}

.profile_wrapper__details__single__contents__para {
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
  margin-top: 7px;
}

.profile_wrapper__details__single:nth-child(3n+2) .profile-wrapper-details-single-thumb {
  background-color: rgba(0, 200, 151, 0.1);
}

.profile_wrapper__details__single:nth-child(3n+3) .profile-wrapper-details-single-thumb {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
}

.profile_wrapper__about__title {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
  margin: -4px 0 0;
}

.profile_wrapper__about__para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.profile_wrapper__experience__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.profile_wrapper__experience__details {
  padding: 20px;
  border: 1px solid #f3f3f3;
  border-radius: 10px;
}

.profile_wrapper__experience__details:not(:last-child) {
  margin-bottom: 25px;
}

.profile_wrapper__experience__details__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 20px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.profile_wrapper__experience__details__left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.profile_wrapper__experience__details__title {
  font-size: 20px;
  font-weight: 600;
}

.profile_wrapper__experience__details__subtitle {
  font-size: 16px;
  line-height: 24px;
  margin-top: 8px;
}

.profile_wrapper__experience__details__edit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border: 1px solid #f3f3f3;
  font-size: 16px;
  border-radius: 4px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.profile_wrapper__experience__details__edit:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.profile_wrapper__experience__details__list {
  margin: 0;
  padding: 0;
  list-style: none;
  border-top: 1px solid #f3f3f3;
  padding-top: 20px;
  margin-top: 20px;
}

.profile_wrapper__experience__details__list__item {
  font-size: 16px;
  font-weight: 400;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px 10px;
}

.profile_wrapper__experience__details__list__item:not(:first-child) {
  margin-top: 15px;
}

.profile_wrapper__experience__details__list__item .list__inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.profile_wrapper__experience__details__list__item .list__inner__para a {
  color: var(--main-color-one);
  font-weight: 500;
  margin-left: 2px;
}

.profile_wrapper__skill__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0;
  margin: 0;
  list-style: none;
}

.profile_wrapper__skill__list__item {
  display: inline-block;
  padding: 8px 20px;
  border: 1px solid #f3f3f3;
  font-size: 16px;
  border-radius: 5px;
  cursor: pointer;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .profile_wrapper__skill__list__item {
    padding: 5px 12px;
  }
}

.profile_wrapper__skill__list__item.active,
.profile_wrapper__skill__list__item:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border-color: var(--main-color-one);
}

.profile_wrapper__catalogue__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
}

.profile_wrapper__catalogue__orderCompleted {
  display: inline-block;
  padding: 5px 20px;
  border: 1px solid #f3f3f3;
  font-size: 16px;
  color: var(--light-color);
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.profile_wrapper__catalogue__orderCompleted:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.profile_wrapper__catalogue__content {
  margin-top: 25px;
}

.profile_wrapper__catalogue__content__review {
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
  padding: 5px 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 5px;
  gap: 5px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.profile_wrapper__catalogue__content__title {
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
  margin: 15px 0 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.profile_wrapper__catalogue__content__title:hover {
  color: var(--main-color-one);
}

.profile_wrapper__catalogue__content__para {
  font-size: 14px;
  line-height: 24px;
  color: var(--paragraph-color);
  margin-top: 20px;
}

.profile_wrapper__catalogue__content__price {
  font-size: 24px;
  color: var(--main-color-one);
  font-family: var(--heading-font);
  font-weight: 600;
}

.profile_wrapper__catalogue__content__price s {
  font-size: 22px;
  font-weight: 400;
  color: var(--light-color);
}

.profile_wrapper__catalogue__delivery {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}

.profile_wrapper__catalogue__delivery__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 16px;
}

.profile_wrapper__catalogue__delivery__days {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
}

.dashboard_freelancer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px;
  max-width: 250px;
}

.dashboard_freelancer__thumb {
  max-width: 50px;
  position: relative;
}

.dashboard_freelancer__thumb img {
  border-radius: 50%;
}

.dashboard_freelancer__thumb__dots {
  position: absolute;
  height: 12px;
  width: 12px;
  background-color: var(--active-color);
  border-radius: 50%;
  bottom: 1px;
  right: 0px;
  z-index: 9;
  border: 2px solid var(--border-color);
}

.dashboard_freelancer__thumb__dots.inactive {
  background-color: var(--body-color);
}

.dashboard_freelancer__names {
  font-size: 18px;
  line-height: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

.dashboard_freelancer__designation {
  font-size: 15px;
  line-height: 24px;
  font-weight: 400;
  color: var(--body-color);
}

.status_dropdown {
  display: inline-block;
  position: relative;
}

.status_dropdown__click {
  position: relative;
  z-index: 1;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 15px;
  padding: 7px 10px 7px 10px;
  border: 1px solid var(--border-color);
  color: var(--paragraph-color);
  background-color: #fff;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  gap: 10px;
}

.status_dropdown__click::after {
  display: none;
}

.status_dropdown__click::before {
  content: "";
  /* position: absolute;
  right: 15px;
  top: 10px; */
  font-size: 16px;
  height: 10px;
  width: 10px;
  margin-top: -5px;
  border-right: 2px solid var(--body-color);
  border-bottom: 2px solid var(--body-color);
  background-color: transparent;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.status_dropdown__click:focus {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .status_dropdown__click {
    font-size: 14px;
    padding: 5px 8px;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .status_dropdown__click {
    font-size: 14px;
    padding: 5px 7px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .status_dropdown__click {
    font-size: 13px;
    padding: 5px 5px;
  }
}

.status_dropdown__list {
  margin: 0;
  padding: 0;
  list-style: none;
  position: absolute;
  z-index: 1000;
  display: none;
  min-width: 160px;
  font-size: 15px;
  color: var(--heading-color);
  text-align: left;
  list-style: none;
  background-color: #fff;
  border: 1px solid var(--border-color);
  box-shadow: 0px 10px 20px rgba(var(--main-color-one-rgb), .02);
  border-left: 5px solid var(--main-color-one);
  border-bottom: 1px solid #ddd;
}

.status_dropdown__list__link {
  display: block;
  width: 100%;
  padding: 5px 20px;
  clear: both;
  font-size: 15px;
  line-height: 24px;
  font-weight: 400;
  color: var(--heading-color);
}

.status_dropdown__list__link:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
}

[value=Hold].status_dropdown__click {
  background: #05cd99;
  border: 2px solid #05cd99;
}

[value=Hold].status_dropdown__click:hover {
  background: none;
  color: #05cd99;
}

[value=Warn].status_dropdown__click {
  background: #F9503E;
  border: 2px solid #F9503E;
}

[value=Warn].status_dropdown__click:hover {
  background: none;
  color: #F9503E;
}

[value=Ban].status_dropdown__click {
  background: #FABE50;
  border: 2px solid #FABE50;
}

[value=Ban].status_dropdown__click:hover {
  background: none;
  color: #FABE50;
}

[value=Completed].status_dropdown__click {
  background: #05cd99;
  border: 2px solid #05cd99;
}

[value=Completed].status_dropdown__click:hover {
  background: none;
  color: #05cd99;
}

[value=Canceled].status_dropdown__click {
  background: #F9503E;
  border: 2px solid #F9503E;
}

[value=Canceled].status_dropdown__click:hover {
  background: none;
  color: #F9503E;
}

.dashboard-checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  cursor: pointer;
  gap: 10px;
}

.dashboard-checkbox .check-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 18px;
  width: 18px;
  cursor: pointer;
  background: #fff;
  border: 1px solid var(--border-color);
  border-radius: 0px;
  margin-top: 3px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.dashboard-checkbox .check-input::after {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 10px;
  color: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transform: scale(1.6) rotate(90deg);
  transform: scale(1.6) rotate(90deg);
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.dashboard-checkbox .check-input:checked {
  background: var(--main-color-one);
  border-color: var(--main-color-one);
  background: var(--main-color-one);
}

.dashboard-checkbox .check-input:checked::after {
  visibility: visible;
  opacity: 1;
  -webkit-transform: scale(1.2) rotate(0deg);
  transform: scale(1.2) rotate(0deg);
}

.dashboard-checkbox .checkbox-label {
  cursor: pointer;
  text-align: left;
  line-height: 26px;
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
  margin: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media only screen and (max-width: 575.98px) {
  .dashboard-checkbox .checkbox-label {
    font-size: 15px;
  }
}

.pending-progress {
  display: inline-block;
  padding: 5px 15px;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  color: var(--main-color-one);
  border: 1px solid transparent;
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pending-progress:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.pending-progress.closed {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: var(--border-color);
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pending-progress.closed:hover {
  background-color: var(--heading-color);
  color: #fff;
}

.pending-progress.completed {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.pending-progress.completed:hover {
  background-color: var(--success-color);
  color: #fff;
}

.pending-progress.active {
  background-color: rgba(var(--main-color-oen-rgb), 0.1);
  color: var(--main-color-one);
}

.pending-progress.active:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.pending-progress.cancel {
  background-color: rgba(var(--danger-color-rgb), 0.1);
  color: var(--danger-color);
}

.pending-progress.cancel:hover {
  background-color: var(--danger-color);
  color: #fff;
}

.assign_btn__para {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--paragraph-color);
  white-space: nowrap;
}

.assign_btn__select .js_nice_select {
  height: 42px;
  line-height: 42px;
}

.supportTicket_single:not(:last-child) {
  margin-bottom: 25px;
}

.supportTicket_single__item:not(:last-child) {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.supportTicket_single__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

@media only screen and (max-width: 480px) {
  .supportTicket_single__flex {
    gap: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket_single__flex {
    gap: 10px;
  }
}

.supportTicket_single__content {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.supportTicket_single__content__id {
  color: var(--main-color-one);
  font-weight: 600;
  font-size: 16px;
}

.supportTicket_single__content__title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
}

.supportTicket_single__content__btn .pending-approval {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: var(--border-color);
  color: var(--heading-color);
}

.supportTicket_single__content__btn .custom-order {
  padding: 5px 20px;
  border-radius: 20px;
  font-size: 15px;
  background-color: rgba(var(--secondary-color-rgb), 0.1);
  color: var(--secondary-color);
}

.supportTicket_single__content__time {
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
}

.supportTicket_single__content__para {
  color: var(--paragraph-color);
  font-size: 16px;
  font-weight: 400;
}

.supportTicket_single__content__list {
  -webkit-columns: 250px 3;
  -moz-columns: 250px 3;
  columns: 250px 3;
  padding: 0;
  list-style: none;
}

.supportTicket_single__content__list__item {
  padding-left: 20px;
  position: relative;
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.supportTicket_single__content__list__item:hover {
  color: var(--main-color-one);
}

.supportTicket_single__content__list__item::before {
  content: "";
  position: absolute;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  left: 0;
  top: 8px;
}

.supportTicket_single__uploads {
  font-size: 16px;
  font-weight: 500;
  color: var(--main-color-one);
  text-decoration: underline;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  display: block;
}

.supportTicket_single__uploads:hover {
  color: var(--main-color-one);
}

.supportTicket_single__uploads:not(:first-child) {
  margin-top: 10px;
}

.supportTicket_single__attachment {
  background-color: var(--section-bg-2);
  padding: 5px 20px;
  border: 2px dashed var(--border-color);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: 5px;
  gap: 5px;
  position: relative;
}

.supportTicket_single__attachment__para {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 7px;
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.supportTicket_single__attachment .file_upload {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  opacity: 0;
}

.supportTicket_single__invoice {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.supportTicket_single__invoice__id {
  color: var(--main-color-one);
  font-weight: 500;
}

.supportTicket_single__chat:not(:first-child) {
  margin-top: 24px;
}

.supportTicket_single__chat.reply .supportTicket_single__chat__flex {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
}

.supportTicket_single__chat.reply .supportTicket_single__chat__contents {
  text-align: right;
}

.supportTicket_single__chat__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px 20px;
}

@media only screen and (max-width: 480px) {
  .supportTicket_single__chat__flex {
    gap: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket_single__chat__flex {
    gap: 10px;
  }
}

.supportTicket_single__chat__thumb {
  max-width: 70px;
}

@media only screen and (max-width: 480px) {
  .supportTicket_single__chat__thumb {
    max-width: 50px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket_single__chat__thumb {
    max-width: 40px;
  }
}

.supportTicket_single__chat__thumb img {
  border-radius: 50%;
}

.supportTicket_single__chat__contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.supportTicket_single__chat__box {
  display: inline-block;
  border: 1px solid var(--border-color);
  padding: 20px;
  border-radius: 5px;
}

@media only screen and (max-width: 480px) {
  .supportTicket_single__chat__box {
    padding: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket_single__chat__box {
    padding: 10px;
  }
}

.supportTicket_single__chat__message {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
}

@media only screen and (max-width: 480px) {
  .supportTicket_single__chat__message {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .supportTicket_single__chat__message {
    font-size: 14px;
  }
}

.supportTicket_single__chat__time {
  font-size: 15px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.supportTicket_single__chat__replyForm__input {
  width: 100%;
}

.supportTicket_single__chat__replyForm__input .label-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 24px;
  margin-bottom: 8px;
}

.supportTicket_sidebar {
  position: sticky;
  top: 0;
  z-index: 9;
}

.supportTicket_sidebar__author__thumb {
  max-width: 50px;
}

.supportTicket_sidebar__author__thumb img {
  border-radius: 50%;
}

.supportTicket_sidebar__author__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px;
}

.supportTicket_sidebar__author__name {
  font-size: 20px;
  line-height: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

.supportTicket_sidebar__author__date {
  font-size: 15px;
  line-height: 24px;
  font-weight: 400;
  color: var(--body-color);
}

.supportTicket_sidebar__author__para {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.supportTicket_sidebar__author__contents {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.row_tickets {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.row_tickets_col_8 {
  -ms-flex-preferred-size: 65%;
  flex-basis: 65%;
}

@media (min-width: 1600px) and (max-width: 1849.98px) {
  .row_tickets_col_8 {
    -ms-flex-preferred-size: 60%;
    flex-basis: 60%;
  }
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .row_tickets_col_8 {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .row_tickets_col_8 {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .row_tickets_col_8 {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .row_tickets_col_8 {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

.row_tickets_col_4 {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  position: sticky;
  top: 0;
  z-index: 9;
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .row_tickets_col_4 {
    max-width: 60%;
    -webkit-box-flex: unset;
    -ms-flex: unset;
    flex: unset;
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .row_tickets_col_4 {
    max-width: 100%;
    -webkit-box-flex: unset;
    -ms-flex: unset;
    flex: unset;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .row_tickets_col_4 {
    max-width: 80%;
    -webkit-box-flex: unset;
    -ms-flex: unset;
    flex: unset;
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .row_tickets_col_4 {
    max-width: 80%;
    -webkit-box-flex: unset;
    -ms-flex: unset;
    flex: unset;
  }
}

.topic__search__item {
  position: relative;
  overflow: hidden;
  border-radius: 7px;
}

.topic__search__item__input {
  width: 100%;
  height: 55px;
  line-height: 55px;
  padding: 0 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--paragraph-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px transparent;
  box-shadow: 0 0 10px transparent;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 7px;
  padding-right: 70px;
}

.topic__search__item__input:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.topic__search__item__icon {
  position: absolute;
  right: 0;
  bottom: 0;
  height: 55px;
  font-size: 20px;
  background-color: var(--border-color);
  color: var(--paragraph-color);
  padding: 5px 20px;
  border: 0;
  outline: none;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.topic__search__item__icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.topic_single__row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
}

.topic_single__item {
  width: calc(20% - 19.2px);
  border: 1px solid var(--border-color);
  padding: 20px;
  border-radius: 10px;
  background-color: #fff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  cursor: pointer;
}

@media (min-width: 1600px) and (max-width: 1849.98px) {
  .topic_single__item {
    width: calc(25% - 18px);
  }
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .topic_single__item {
    width: calc(33.3333333333% - 16px);
  }
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .topic_single__item {
    width: calc(25% - 18px);
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .topic_single__item {
    width: calc(33.3333333333% - 16px);
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .topic_single__item {
    width: calc(33.3333333333% - 16px);
  }
}

@media only screen and (max-width: 575.98px) {
  .topic_single__item {
    width: calc(50% - 12px);
  }
}

@media only screen and (max-width: 480px) {
  .topic_single__item {
    width: calc(100% - 0px);
  }
}

.topic_single__item.active {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  border-color: var(--main-color-one);
}

.topic_single__item.active .topic_single__icon {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

.topic_single__item:hover {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  border-color: var(--main-color-one);
}

.topic_single__item:hover .topic_single__icon {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

.topic_single__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  font-size: 20px;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.topic_single__contents__title {
  font-size: 20px;
  line-height: 26px;
  font-weight: 500;
  color: var(--heading-color);
}

.topic_single__contents__para {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--paragraph-color);
}

.single_proposal__item {
  border: 1px solid var(--border-color);
  padding: 20px;
  border-radius: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
}

@media only screen and (max-width: 575.98px) {
  .single_proposal__item {
    display: -ms-grid;
    display: grid;
  }
}

.single_proposal__item:not(:first-child) {
  margin-top: 20px;
}

.single_proposal__item__left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.single_proposal__item__title {
  font-size: 20px;
  line-height: 26px;
  font-weight: 600;
  color: var(--heading-color);
}

.single_proposal__item__para {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--paragraph-color);
}

.single_proposal__item__action__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.single_proposal__item__action__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  font-size: 16px;
  border: 1px solid var(--border-color);
  color: var(--body-color);
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.single_proposal__item__action__icon:hover {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: transparent;
}

.single_proposal__item__action__icon.edit {
  border-color: var(--main-color-one);
  color: var(--main-color-one);
}

.single_proposal__item__action__icon.edit:hover {
  background-color: var(--main-color-one);
  color: #fff;
}

.single_proposal__item__action__icon.delete {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.single_proposal__item__action__icon.delete:hover {
  background-color: var(--danger-color);
  color: #fff;
}

.pagination_wrapper.center-text .pagination {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.pagination_wrapper .pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.pagination_wrapper .pagination li.active a {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

.pagination_wrapper .pagination li a {
  border: 1px solid var(--border-color);
  display: inline-block;
  padding: 7px 13px;
  font-size: 16px;
  line-height: 1;
  font-weight: 400;
  color: var(--body-color);
  border-radius: 3px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pagination_wrapper .pagination li a:focus {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

.pagination_wrapper .pagination li a:hover {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

.pagination_wrapper .pagination li a.active {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}

/* Popup Modal */
.popup-fixed {
  position: fixed;
  top: 50%;
  left: 0%;
  right: 0;
  margin-inline: auto;
  padding: 0 5px;
  -webkit-transform: translateY(-50%) scale(0.6);
  transform: translateY(-50%) scale(0.6);
  z-index: 9992;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  max-width: -webkit-fit-content;
  max-width: -moz-fit-content;
  max-width: fit-content;
}

.popup-fixed.popup-active {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateY(-50%) scale(1);
  transform: translateY(-50%) scale(1);
}

.popup-overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9991;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.popup-overlay.popup-active {
  visibility: visible;
  opacity: 1;
}

.popup-contents {
  max-width: 600px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  background-color: #fff;
  padding: 30px;
  margin: auto;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  max-height: calc(100vh - 50px);
  overflow-y: auto;
  scrollbar-color: var(--main-color-one) #e6e6e6;
  scrollbar-width: thin;
}

.popup-contents::-webkit-scrollbar {
  width: 5px;
  height: 8px;
  background-color: #d3d3d3;
  border-radius: 10px;
}

.popup-contents::-webkit-scrollbar-thumb {
  background-color: var(--main-color-one);
  border-radius: 10px;
}

.popup-contents-close {
  position: absolute;
  right: 0;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  background-color: #f3f3f3;
  color: #ff0000;
  font-size: 18px;
  -webkit-box-shadow: 0 0 10px #f3f3f3;
  box-shadow: 0 0 10px #f3f3f3;
  cursor: pointer;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.popup-contents-close:hover {
  background-color: #ff0000;
  color: #fff;
}

.popup-contents-changePhoto {
  max-width: 550px;
}

.popup-contents-changePhoto img {
  border-radius: 10px;
}

.popup-contents-portfolio-thumb img {
  border-radius: 10px;
}

.popup-contents-title {
  font-size: 24px;
  line-height: 28px;
  font-weight: 600;
  color: var(--heading-color);
  margin: -4px 0 10px;
}

.popup-contents-para {
  color: var(--paragraph-color);
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 30px;
}

.popup-contents-btn {
  margin-top: 25px;
}

.popup-contents-withdraw-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.popup-contents-withdraw-item:not(:last-child) {
  border-bottom: 1px solid #EAECF0;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.popup-contents-withdraw-title {
  font-size: 16px;
  color: var(--heading-color);
  font-weight: 500;
  line-height: 24px;
}

.popup-contents-withdraw-price {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
}

.popup-contents-withdraw-exchange {
  background-color: #EAECF0;
  padding: 20px;
  border-radius: 5px;
}

.popup-contents-withdraw-exchange-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 26px;
}

.popup-contents-withdraw-exchange-para {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
}

.popup-contents-withdraw-exchange-para-link {
  color: var(--main-color-one);
  padding: 0 5px;
  display: inline-block;
  font-weight: 500;
}

.popup-contents-encrypt-para {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 7px;
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
}

.popup-contents-encrypt-para i {
  margin-top: 3px;
}

.popup-contents-transaction-link {
  display: block;
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.popup-contents-transaction-link:not(:first-child) {
  padding-top: 10px;
  margin-top: 10px;
  border-top: 1px solid #EAECF0;
}

.popup-contents-transaction-link:hover {
  color: var(--main-color-one);
}

.popup-contents-milestone-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.popup-contents-milestone-list-item {
  display: block;
}

.popup-contents-milestone-list-item:not(:first-child) {
  margin-top: 10px;
}

.popup-contents-milestone-list-item a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  padding: 10px 20px;
  background-color: var(--border-color);
  border-radius: 5px;
  color: var(--heading-color);
  font-size: 14px;
  font-weight: 500;
}

/* Send offer css */
.sendOffer-para {
  background-color: #F2F4F7;
  padding: 20px;
  color: var(--paragraph-color);
  border-radius: 10px;
}

.proposal-input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
}

.proposal-input-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.proposal-input-left-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.proposal-input-item {
  border: 1px solid #ddd;
  border-right: 0;
  padding: 5px 10px;
  height: 55px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 7px 0 0 7px;
}

.proposal-input-budget {
  white-space: nowrap;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  background-color: #F2F4F7;
  padding: 5px 15px;
  border-radius: 5px;
  color: var(--heading-color);
}

.proposal-input-budget .price {
  font-size: 20px;
  font-weight: 600;
  color: var(--heading-color);
}

.proposal-input .single-input {
  margin-top: 0;
}

.proposal-input .single-input .form--control {
  border-radius: 0 7px 7px 0;
}

.proposal-input-right {
  -ms-flex-preferred-size: 180px;
  flex-basis: 180px;
}

.proposal-input-right-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  height: 55px;
  border: 1px solid #ddd;
  padding: 0 15px;
  background-color: #F2F4F7;
  border-radius: 7px;
}

.proposal-input-right-price {
  font-size: 20px;
  font-weight: 600;
  color: var(--main-color-one);
}

.proposal-input-right-question {
  cursor: pointer;
  font-size: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 22px;
  width: 22px;
  border: 1px solid var(--paragraph-color);
  color: var(--paragraph-color);
  border-radius: 50%;
}

/* Dashboard Custom Css */
.customMarkup__single {
  background-color: #fff;
  padding: 20px;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.customMarkup__single__item:not(:last-child) {
  margin-bottom: 24px;
}

.customMarkup__title {
  font-size: 24px;
  font-weight: 600;
  color: var(--heading-color);
  line-height: 28px;
}

.customMarkup__single__item__flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  flex-wrap: wrap;
}

.custom-form .form-group .form--control,
.custom-form .form-group .form-control,
.custom-form .single-input .form--control,
.custom-form .single-input .form-control {
  -moz-appearance: textfield;
  width: 100%;
  height: 55px;
  line-height: 55px;
  padding: 0 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--paragraph-color);
  background-color: #fff;
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.custom-form .form-group .form--control:focus,
.custom-form .form-group .form-control:focus,
.custom-form .single-input .form--control:focus,
.custom-form .single-input .form-control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.custom-form .form-group textarea,
.custom-form .single-input textarea {
  height: auto !important;
}

.label-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: var(--heading-color);
  margin-bottom: 8px;
  display: block;
}

.form--control {
  -moz-appearance: textfield;
  width: 100%;
  height: 55px;
  line-height: 55px;
  padding: 0 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--paragraph-color);
  background-color: #fff;
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.form--control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

textarea {
  height: auto !important;
}

.checkboxed {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  cursor: pointer;
  gap: 10px;
}

.checkboxed.style-02 .checkboxed__input:checked+.checkboxed__label {
  color: var(--main-color-one);
}

.checkboxed__input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 18px;
  width: 18px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dddddd;
  border-radius: 0px;
  margin-top: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.checkboxed__input::after {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 10px;
  color: #fff;
  visibility: hidden;
  opacity: 0;
  -webkit-transform: scale(1.6) rotate(90deg);
  transform: scale(1.6) rotate(90deg);
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.checkboxed__input:checked {
  background: var(--main-color-one);
  border-color: var(--main-color-one);
  background: var(--main-color-one);
}

.checkboxed__input:checked::after {
  visibility: visible;
  opacity: 1;
  -webkit-transform: scale(1) rotate(0deg);
  transform: scale(1) rotate(0deg);
}

.checkboxed__label {
  cursor: pointer;
  text-align: left;
  line-height: 26px;
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
  margin: 0;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.radioboxed {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  cursor: pointer;
  gap: 10px;
}

.radioboxed.style-02 .radioboxed__input:checked+.radioboxed__label {
  color: var(--main-color-one);
}

.radioboxed__input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 22px;
  width: 22px;
  border-radius: 50%;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dddddd;
  margin-top: 4px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  position: relative;
}

.radioboxed__input::after {
  content: "";
  position: absolute;
  top: 4px;
  left: 4px;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background-color: var(--main-color-one);
  visibility: hidden;
  opacity: 0;
  -webkit-transform: scale(0.2) rotate(90deg);
  transform: scale(0.2) rotate(90deg);
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.radioboxed__input:checked {
  border-color: var(--main-color-one);
}

.radioboxed__input:checked::after {
  visibility: visible;
  opacity: 1;
  -webkit-transform: scale(1) rotate(0deg);
  transform: scale(1) rotate(0deg);
}

.radioboxed__label {
  cursor: pointer;
  text-align: left;
  line-height: 26px;
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
  margin: 0;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.custom-switch {
  height: 0;
  width: 0;
  visibility: hidden;
  position: absolute;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.custom-switch:checked+.switch-label {
  background: var(--main-color-one);
}

.custom-switch:checked+.switch-label::after {
  left: calc(100% - 5px);
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.switch-label {
  cursor: pointer;
  text-indent: -9999px;
  width: 50px;
  height: 25px;
  background: #DDDDDD;
  display: block;
  border-radius: 100px;
  position: relative;
}

.switch-label::after {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  width: 25px;
  height: 15px;
  background: #fff;
  border-radius: 60px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}



/*new style*/



.nice_select_item {
  display: flow-root;
}

.table_bg_1 {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.table_bg_2 {
  background-color: var(--border-color);
}

.table_bg_3 {
  background-color: var(--section-bg-1);
}

.table_bg_4 {
  background-color: var(--section-bg-2);
}

.custom_table {
  overflow-x: auto;
}

.custom_table table {
  width: 100%;
}

.custom_table table thead {
  border: 0;
}

.custom_table table tr {
  border: 0;
}

.custom_table table th {
  white-space: nowrap;
  padding: 10px;
  font-size: 18px;
  font-weight: 700;
  color: var(--heading-color);
}

.custom_table table td {
  padding: 10px;
  border: 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: var(--paragraph-color);
  word-break: break-word;
}

.custom_table.style-02 table thead {
  background-color: var(--main-color-one);
}

.custom_table.style-02 table thead th {
  color: #fff;
}

.custom_table.style-02 table tbody tr:nth-child(odd) {
  background-color: var(--section-bg-1);
}

.custom_table.style-02 table tbody tr:nth-child(even) {
  background-color: var(--border-color);
}

.custom_table.style-03 table thead {
  background-color: var(--main-color-one);
}

.custom_table.style-03 table thead th {
  color: #fff;
}

.custom_table.style-03 table tbody tr:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.custom_table.style-04 table thead th {
  border: 1px solid var(--border-color);
}

.custom_table.style-04 table tbody tr:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.custom_table.style-04 table tbody tr td {
  border: 1px solid var(--border-color);
}

.custom_table.style-05 table thead th {
  border: 1px solid rgba(var(--main-color-one-rgb), 0.3);
}

.custom_table.style-05 table tbody tr:not(:last-child) {
  border: 1px solid rgba(var(--main-color-one-rgb), 0.3);
}

.custom_table.style-05 table tbody tr td {
  border: 1px solid rgba(var(--main-color-one-rgb), 0.3);
}

.custom_table .DataTable_activation {
  margin-top: 24px;
  display: inline-table;
  border: initial !important;
  overflow-x: auto;
  margin-bottom: 24px;
}

.custom_table .DataTable_activation thead th {
  border-color: initial;
}

.custom_table .DataTable_activation tbody {
  border: initial;
}

.custom_table .dataTables_wrapper .dataTables_paginate .paginate_button {
  background: var(--border-color) !important;
  border-color: var(--border-color) !important;
}

.custom_table .dataTables_wrapper .dataTables_paginate .paginate_button.current,
.custom_table .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--main-color-one) !important;
  color: #fff !important;
}

/* Summer Note Css */
.summernote-wrapper .note-editing-area {
  height: 400px;
  overflow-y: auto !important;
}



.search_delete_wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.btn_plus_icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;
  background-color: var(--main-color-one);
  border-radius: 50%;
  color: var(--main-color-one);
  font-size: 14px;
}

.search_wrapper {
  position: relative;
}

.search_wrapper_icon {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 100%;
  padding: 0px 13px;
  border-right: 1px solid var(--border-color);
  text-align: center;
  line-height: 42px;
  font-size: 14px;
  cursor: pointer;
}

.search_wrapper .form--control {
  height: 42px;
  border-radius: 5px;
  padding-left: 55px;
}

.bulk-delete-wrapper .select-box-wrap select {
  height: 36px;
}

/*Pagination Css*/

.custom_pagination {}

.custom_pagination .pagination {
  justify-content: center;
  gap: 5px;
}

.custom_pagination .page-link {
  position: relative;
  display: block;
  color: var(--paragraph-color);
  text-decoration: none;
  background-color: #fff;
  border: 1px solid var(--main-color-one);
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 30px;
  border-radius: 50%;
}

.custom_pagination .page-item.active .page-link,
.custom_pagination .page-item:hover .page-link {
  background-color: var(--main-color-one);
  border-color: var(--main-color-one);
  color: #fff;
}

.custom_pagination .page-item:first-child .page-link {
  border-top-left-radius: 50%;
  border-bottom-left-radius: 50%;
  font-size: 24px;
}

.custom_pagination .page-item:last-child .page-link {
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  font-size: 24px;
}

.select2-dropdown {
  z-index: 1060 !important;
}

.select2-container {
  display: grid !important;
}

.select2-container--default .select2-selection--single {
  border: 1px solid #ced4da !important;
  height: 42px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 42px !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: var(--paragraph-color) !important;
  line-height: 42px !important;
}

.img_100 {
  width: 100px;
  height: 100px;
}

.img_150 {
  width: 150px;
  height: 100px;
}

.img_200 {
  width: 200px;
  height: 100px;
}



/* Project Preview start */
.sticky-sidebar {
  position: sticky;
  top: 0;
  z-index: 9;
}

.project-preview {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.project-preview:not(:first-child) {
  margin-top: 24px;
}

.project-preview-head-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
  margin: -3px 0 0;
}

.project-preview-thumb img {
  border-radius: 10px;
}

.project-preview-contents-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  margin: -3px 0 0;
}

.project-preview-contents-para {
  font-size: 14px;
  line-height: 24px;
  margin-top: 10px;
  color: var(--paragraph-color);
}

 .project-preview-tab .tabs {
   background-color: var(--border-color);
   padding: 10px;
   display: flex;
   -webkit-box-pack: justify;
   -ms-flex-pack: justify;
   justify-content: space-between;
   border-radius: 10px;
   box-shadow: 0px 0px 10px #f1f1f1;
   list-style: none;
 }

@media only screen and (max-width: 375px) {
  .project-preview-tab .tabs {
    padding: 5px;
  }
}

 .project-preview-tab .tabs li {
   -webkit-box-flex: 1;
   -ms-flex-positive: 1;
   flex-grow: 1;
   text-align: center;
   padding: 5px;
   border-radius: 10px;
   color: var(--body-color);
   font-weight: 600;
   cursor: pointer;
 }

@media only screen and (max-width: 375px) {
  .project-preview-tab .tabs li {
    font-size: 15px;
  }
}

.project-preview-tab .tabs li::before {
  display: none;
}

.project-preview-tab .tabs li.active {
  background-color: #fff;
}

 .project-preview-tab-header {
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   -webkit-box-align: start;
   -ms-flex-align: start;
   align-items: flex-start;
   -ms-flex-wrap: wrap;
   flex-wrap: wrap;
   background-color: var(--border-color);
   padding: 10px;
   border-radius: 5px;
 }

@media only screen and (max-width: 375px) {
  .project-preview-tab-header {
    padding: 5px;
  }
}

.project-preview-tab-header-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 12px 20px;
  position: relative;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .project-preview-tab-header-item {
    gap: 12px 12px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item {
    gap: 10px 10px;
  }
}

.project-preview-tab-header-item:not(:last-child) {
  padding-right: 20px;
  margin-right: 20px;
}
 .project-preview-tab-header-item:not(:last-child):after {
   content: "";
   position: absolute;
   height: 80%;
   width: 1px;
   background-color: #bfbfbf;
   right: 0;
   top: 50%;
   transform: translateY(-50%);
 }
@media (min-width: 1200px) and (max-width: 1399.98px) {
  .project-preview-tab-header-item:not(:last-child) {
    padding-right: 10px;
    margin-right: 10px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item:not(:last-child) {
    padding-right: 5px;
    margin-right: 5px;
  }
}

.project-preview-tab-header-item .left {
  font-size: 16px;
  color: var(--paragraph-color);
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item .left {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-header-item .left {
    font-size: 15px;
  }
}

.project-preview-tab-header-item .left i {
  font-size: 15px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item .left i {
    font-size: 15px;
  }
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-header-item .left i {
    font-size: 15px;
  }
}

.project-preview-tab-header-item .right {
  font-weight: 700;
  color: var(--heading-color);
  font-size: 15px;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .project-preview-tab-header-item .right {
    font-size: 16px;
  }
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-header-item .right {
    font-size: 16px;
  }
}

.project-preview-tab-inner-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 12px 20px;
}

.project-preview-tab-inner-item:not(:last-child) {
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
}

.project-preview-tab-inner-item .check-icon {
  color: var(--success-color);
}

.project-preview-tab-inner-item.included .left {
  position: relative;
}

.project-preview-tab-inner-item.included .left::before {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  color: var(--success-color);
  font-size: 18px;
}

.project-preview-tab-inner-item.not_included .left {
  position: relative;
  opacity: 0.6;
}

.project-preview-tab-inner-item.not_included .left::before {
  content: "\f00d";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  color: var(--paragraph-color);
  font-size: 18px;
  opacity: 0.5;
}

.project-preview-tab-inner-item.not_included .right {
  opacity: 0.6;
}

.project-preview-tab-inner-item .left {
  font-size: 16px;
  font-weight: 400;
  color: var(--paragraph-color);
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-inner-item .left {
    font-size: 15px;
  }
}

.project-preview-tab-inner-item .left.price-title {
  font-size: 20px;
  line-height: 24px;
  font-weight: 600;
  color: var(--heading-color);
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-inner-item .left.price-title {
    font-size: 18px;
  }
}

.project-preview-tab-inner-item .right {
  font-weight: 500;
  color: var(--heading-color);
  font-size: 18px;
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-inner-item .right {
    font-size: 16px;
  }
}

.project-preview-tab-inner-item .right.price {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  color: var(--main-color-one);
}

@media only screen and (max-width: 375px) {
  .project-preview-tab-inner-item .right.price {
    font-size: 18px;
  }
}

.project-preview-tab-inner-item .right.price s {
  color: var(--body-color);
  font-weight: 400;
}

/* Project Preview end */


/* Project Preview Pricing */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper {
    gap: 25px 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper {
    gap: 25px 0;
  }
}

.pricing-wrapper-left {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-left {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-left .pricing-wrapper-card-top {
    display: none;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-left .pricing-wrapper-card-top {
    display: none;
  }
}

.pricing-wrapper-left .pricing-wrapper-card::before {
  display: none;
}

.pricing-wrapper-left .pricing-wrapper-card-bottom {
  position: relative;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.pricing-wrapper-left .pricing-wrapper-card-bottom::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  height: 100%;
  width: 0%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing-wrapper-left .pricing-wrapper-card:hover .pricing-wrapper-card-bottom {
  border-radius: 0px;
  overflow: hidden;
}

.pricing-wrapper-left .pricing-wrapper-card:hover .pricing-wrapper-card-bottom::before {
  visibility: visible;
  opacity: 1;
  width: 100%;
}

.pricing-wrapper-right {
  -webkit-box-flex: 3;
  -ms-flex: 3;
  flex: 3;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-right {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    gap: 25px 0;
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-right {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    gap: 25px 0;
  }
}

.pricing-wrapper-right .pricing-wrapper-card {
  border: 1px solid rgba(221, 221, 221, 0.4);
  border-left: 0;
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-right .pricing-wrapper-card:nth-child(1) {
    border-left: 1px solid rgba(221, 221, 221, 0.4);
  }
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-right .pricing-wrapper-card {
    border-left: 1px solid rgba(221, 221, 221, 0.4);
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .pricing-wrapper-right .pricing-wrapper-card:nth-child(3n+1) {
    border-right: unset;
  }
}

.pricing-wrapper-right .pricing-wrapper-card:nth-child(3n+1) .pricing-wrapper-card-top {
  border-left: 1px solid rgba(221, 221, 221, 0.4);
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-right .pricing-wrapper-card:nth-child(3n+1) .pricing-wrapper-card-top {
    border-left: unset;
  }
}

.pricing-wrapper-right .pricing-wrapper-card-bottom {
  border: 0;
  border-top: 1px solid rgba(221, 221, 221, 0.4);
  border-left: 0;
}

.pricing-wrapper-card {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  position: relative;
}

@media (min-width: 300px) and (max-width: 991.98px) {
  .pricing-wrapper-card {
    gap: 25px 0;
  }
}

@media only screen and (max-width: 575.98px) {
  .pricing-wrapper-card {
    -ms-flex-preferred-size: 50%;
    flex-basis: 50%;
  }
}

@media only screen and (max-width: 480px) {
  .pricing-wrapper-card {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}

.pricing-wrapper-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  /* IE 9 */
  -webkit-transform: translateX(-50%);
  /* Chrome, Safari, Opera */
  transform: translateX(-50%);
  height: 100%;
  width: 0%;
  background-color: rgba(var(--main-color-one-rgb), 0.1);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing-wrapper-card:hover {
  color: #fff;
  border-radius: 0px;
  overflow: hidden;
  /* IE 9 */
  -webkit-transform: scaleY(1);
  /* Chrome, Safari, Opera */
  transform: scaleY(1);
}

.pricing-wrapper-card:hover::before {
  visibility: visible;
  opacity: 1;
  width: 100%;
}

.pricing-wrapper-card.active .pricing-wrapper-card-top {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.pricing-wrapper-card.active .pricing-wrapper-card-bottom {
  background-color: rgba(var(--main-color-one-rgb), 0.1);
}

.pricing-wrapper-card-top {
  padding: 20px;
  min-height: 70px;
}

.pricing-wrapper-card-top-prices {
  color: var(--heading-color);
  font-size: 16px;
  line-height: 26px;
  margin: -2px 0 0;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.pricing-wrapper-card-bottom {
  border: 1px solid rgba(221, 221, 221, 0.4);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

 .pricing-wrapper-card-bottom-list ul li {
   color: #585858;
   font-size: 16px;
   font-weight: 400;
   border-bottom: 1px solid rgba(221, 221, 221, 0.4);
   padding: 0px 5px;
   line-height: 50px;
   overflow: hidden;
   text-overflow: ellipsis;
   display: -webkit-box;
   -webkit-line-clamp: 1;
   line-clamp: 1;
   -webkit-box-orient: vertical;
 }

.pricing-wrapper-card-bottom-list ul li:last-child {
  border: 0;
}

.pricing-wrapper-card-bottom-list ul li .price-main {
  font-size: 18px;
  font-weight: 600;
  color: var(--main-color-one);
  display: inline-block;
}

.pricing-wrapper-card-bottom-list ul li .price-old {
  font-size: 14px;
  color: var(--paragraph-color);
  line-height: 1;
}

@media (min-width: 1200px) and (max-width: 1399.98px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 15px;
    padding: 0 10px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 15px;
  }
}

@media only screen and (max-width: 480px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 16px;
  }
}

@media only screen and (max-width: 375px) {
  .pricing-wrapper-card-bottom-list ul li {
    font-size: 15px;
  }
}

.pricing-wrapper-card-bottom-list ul li .check-icon {
  color: var(--main-color-one) !important;
}

/* Project Feedback Css */
.project-feedback-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 15px;
}

@media only screen and (max-width: 375px) {
  .project-feedback-flex {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}

.project-feedback-thumb {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  max-width: 70px;
}

.project-feedback-thumb img {
  border-radius: 50%;
}

.project-feedback-contents-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 15px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.project-feedback-contents-name {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.project-feedback-contents-title {
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  color: var(--heading-color);
}

.project-feedback-contents-subtitle {
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;
  color: var(--paragraph-color);
}

.project-feedback-contents-review {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  background-color: rgba(255, 172, 22, 0.1);
  color: #ffac16;
  padding: 3px 7px;
}

.project-feedback-contents-para {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: var(--paragraph-color);
}

.project--rejected--wrapper {
  background: var(--border-color);
  padding: 10px;
  border-radius: 10px;
}

 .project--rejected--wrapper .customMarkup__single__title {
   font-size: 14px;
   line-height: 20px;
   font-weight: 600;
   color: var(--body-color);
   background-color: #fff;
   padding: 5px 10px;
   display: flex;
   align-items: center;
   gap: 5px;
   border-radius: 10px;
 }

.project--rejected--wrapper .customMarkup__single__title .alert {
  margin-bottom: 0;
  background: var(--border-color);
  color: #fff;
  padding: 5px 10px;
  font-weight: 400;
  font-size: 16px;
  border-radius: 5px;
}

.project--rejected--wrapper .customMarkup__single__title .alert-success {
  background: var(--success-color);
}

.project--rejected--wrapper .customMarkup__single__title .alert-warning {
  background-color: #e0a800;
}

.project--rejected--wrapper .customMarkup__single__title .alert-danger {
  background-color: var(--danger-color);
}

.project-reject-edit-count {
  display: flex;
  background: var(--main-color-one);
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  line-height: 18px;
  height: 20px;
  width: 20px;
  align-items: center;
  justify-content: center;
}

.project-reject-edit-count.delete-count {
  background: var(--danger-color);
}

.copyright-area {
  background: #f0f0f0;
  padding: 20px 30px;
  flex-shrink: 0;
}

.copyright-contents-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px 10px;
}

.copyright-contents-main {
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
}

.copyright-contents-version {
  font-size: 16px;
  font-weight: 400;
  color: var(--heading-color);
}

.user-details-manage-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.user-details-manage-list .item {
  display: flex;
  align-items: center;
}

.user-details-manage-list .item strong {
  min-width: 80px;
}

.user-details-manage-thumb {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.user-details-manage-thumb img {
  border-radius: 50%;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.user-details-manage-title {
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.alert {
  position: relative;
  padding: 7px 15px;
  border-radius: .25rem;
}

.btn:focus {
  box-shadow: none !important;
}

.btn.btn-primary:focus {
  box-shadow: none;
}

.btn span {
  color: inherit;
}

.btn.btn-primary {
  background-color: var(--main-color-one);
  box-shadow: 0 0 20px rgba(var(--main-color-one-rgb), .1);
  color: #fff;
}

.single-input .form__control,
.single-input .form_control,
.single-input .form--control,
.single-input .form-control {
  -moz-appearance: textfield;
  width: 100%;
  height: 48px;
  line-height: 48px;
  padding: 5px 15px;
  border: 1px solid var(--border-color);
  background-color: unset;
  outline: none;
  color: var(--paragraph-color);
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.single-input .form-control.spectrum_picker {
  border: 0 !important;
  color: unset;
  background: unset;
  box-shadow: none;
}

.single-input textarea,
.single-input textarea.form__control,
.single-input textarea.form_control,
.single-input textarea.form--control,
.single-input textarea.form-control {
  height: unset;
}

.single-input .form__control:focus,
.single-input .form_control:focus,
.single-input .form--control:focus,
.single-input .form-control:focus {
  border-color: rgba(var(--main-color-one-rgb), 0.3);
  -webkit-box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
  box-shadow: 0 0 10px rgba(var(--main-color-one-rgb), 0.1);
}

.single-input select {
  line-height: 1 !important;
  position: relative;
}

.single-input-inner {
  position: relative;
}

.permission-group-wrapper:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
  padding-bottom: 20px;
}

.permission-group-header {
  margin-bottom: 20px;
}

.permission-group-header-title {
  font-size: 20px;
  font-weight: 700;
  line-height: 20px;
  color: var(--heading-color);
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.userDetails__wrapper__item {
  display: flex;
  align-items: baseline;
  gap: 10px;
  justify-content: space-between;
  flex-wrap: wrap;

}

.userDetails__wrapper__item:not(:last-child) {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.userDetails__wrapper__item strong {
  color: var(--heading-color);
  min-width: 120px;
}

.userDetails__wrapper__item span {
  color: var(--paragraph-color);
  font-weight: 400;
  line-height: 20px;
}

.userProfile__details {
  position: relative;
}

.userProfileDetails__title {
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  color: var(--heading-color);
}

.userProfileDetails__noInfo {
  background: var(--border-color);
}

.userProfileDetails__noInfo__title {
  font-size: 24px;
  font-weight: 500;
  line-height: 1.2;
  color: var(--main-color-one);
  padding: 50px 20px;
}

.allOrders__list,
.userSubscription__list {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 24px;
}

.allOrders__list__item,
.allOrders__list___item,
.userSubscription__list__item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  background-color: unset;
  border: 1px solid var(--border-color);
  color: var(--paragraph-color);
  padding: 7px 15px;
  transition: all .3s;
}

.allOrders__list__item:focus,
.userSubscription__list__item:focus {
  box-shadow: none;
}

.allOrders__list__item:hover,
.allOrders__list__item.btn-success,
.userSubscription__list__item:hover,
.userSubscription__list__item.btn-success {
  background-color: var(--main-color-one);
  color: #fff;
  border-color: var(--main-color-one);
}


/* solve summernote content issue */
.note-editable > p {
  max-width: 100%;
  word-break: break-all;
}
.note-editable > div {
  max-width: 100%;
  word-break: break-all;
  padding: 10px;
}
.note-editable > div pre {
  max-width: 100%;
  word-break: break-all;
  display: contents;
}

/*Chat css*/


 .chat-wrapper-details-inner-chat {
   padding: 0 5px;
 }

 .chat-wrapper-details-inner-chat:not(:last-child) {
   margin-bottom: 30px;
 }

 .chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-flex {
   -webkit-box-orient: horizontal;
   -webkit-box-direction: reverse;
   -ms-flex-direction: row-reverse;
   flex-direction: row-reverse;
 }

 .chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-contents {
   text-align: right;
 }
 .chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-contents-para {
   display: inline-block;
 }
 .chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-contents-para-span {
   border-radius: 10px 0px 10px 10px;
   background-color: #F5F6F9;
   color: var(--heading-color);
 }


 .chat-wrapper-details-inner-chat.chat-reply .chat-wrapper-details-inner-chat-contents-time {
   text-align: right;
 }

 .chat-wrapper-details-inner-chat-flex {
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   -webkit-box-align: start;
   -ms-flex-align: start;
   align-items: flex-start;
   gap: 15px;
   -ms-flex-negative: 0;
   flex-shrink: 0;
   -webkit-box-flex: 1;
   -ms-flex-positive: 1;
   flex-grow: 1;
 }

 .chat-wrapper-details-inner-chat-thumb {
   width: 40px;
 }

 .chat-wrapper-details-inner-chat-thumb img {
   border-radius: 50%;
 }

 .chat-wrapper-details-inner-chat-contents {
   -webkit-box-flex: 1;
   -ms-flex: 1;
   flex: 1;
 }

 .chat-wrapper-details-inner-chat-contents-para {
   display: inline-block;
   word-break: break-all;
 }
 .chat-wrapper-details-inner-chat-contents-para-span {
   background-color: var(--main-color-one);
   padding: 7px 15px;
   border-radius: 0 10px 10px 10px;
   color: #fff;
   font-size: 16px;
   line-height: 22px;
   font-weight: 500;
   display: inline-block;
   word-break: break-all;
 }

 @media only screen and (max-width: 575.98px) {
   .chat-wrapper-details-inner-chat-contents-para {
     font-size: 15px;
     line-height: 24px;
     font-weight: 400;
   }
 }

 @media only screen and (max-width: 480px) {
   .chat-wrapper-details-inner-chat-contents-para {
     padding: 10px;
     font-size: 14px;
     line-height: 24px;
     font-weight: 400;
   }
 }

 .chat-wrapper-details-inner-chat-contents-time {
   font-size: 16px;
   color: var(--paragraph-color);
   font-weight: 500;
   display: block;
 }

 @media only screen and (max-width: 575.98px) {
   .chat-wrapper-details-inner-chat-contents-time {
     font-size: 15px;
     line-height: 24px;
     font-weight: 400;
   }
 }

 @media only screen and (max-width: 480px) {
   .chat-wrapper-details-inner-chat-contents-time {
     padding: 10px;
     font-size: 14px;
     font-weight: 400;
     line-height: 24px;
   }
 }

 .chat-wrapper-details-inner-chat-offer {
   border: 1px solid var(--border-color);
   padding: 20px;
   border-radius: 5px;
   -webkit-box-flex: 1;
   -ms-flex: 1;
   flex: 1;
 }

 @media only screen and (max-width: 375px) {
   .chat-wrapper-details-inner-chat-offer {
     padding: 10px;
   }
 }

 .chat-wrapper-details-inner-chat-offer-title {
   font-size: 24px;
   font-weight: 600;
   color: var(--heading-color);
   line-height: 28px;
 }

 @media only screen and (max-width: 480px) {
   .chat-wrapper-details-inner-chat-offer-title {
     font-size: 22px;
   }
 }

 .chat-wrapper-details-inner-chat-offer-para {
   font-size: 16px;
   line-height: 24px;
   color: var(--paragraph-color);
 }

 @media only screen and (max-width: 480px) {
   .chat-wrapper-details-inner-chat-offer-para {
     font-size: 15px;
   }
 }

 .chat-wrapper-details-inner-chat-offer-contents-list {
   list-style: none;
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   -webkit-box-align: center;
   -ms-flex-align: center;
   align-items: center;
   -ms-flex-wrap: wrap;
   flex-wrap: wrap;
   padding-left: 0;
   gap: 15px 0;
 }

 .chat-wrapper-details-inner-chat-offer-contents-list-item {
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   -webkit-box-align: center;
   -ms-flex-align: center;
   align-items: center;
   gap: 15px;
   font-size: 16px;
   line-height: 24px;
   font-weight: 500;
   color: var(--paragraph-color);
   position: relative;
   word-break: break-word;
   -ms-flex-wrap: wrap;
   flex-wrap: wrap;
 }

 @media only screen and (max-width: 575.98px) {
   .chat-wrapper-details-inner-chat-offer-contents-list-item {
     font-size: 15px;
     font-weight: 400;
   }
 }

 @media only screen and (max-width: 375px) {
   .chat-wrapper-details-inner-chat-offer-contents-list-item {
     font-size: 14px;
   }
 }

 .chat-wrapper-details-inner-chat-offer-contents-list-item:not(:last-child) {
   margin-right: 24px;
   padding-right: 24px;
 }

 .chat-wrapper-details-inner-chat-offer-contents-list-item:not(:last-child)::after {
   content: "";
   position: absolute;
   height: 70%;
   width: 2px;
   background-color: var(--border-color);
   right: -1px;
   top: 50%;
   /* IE 9 */
   -webkit-transform: translateY(-50%);
   /* Chrome, Safari, Opera */
   transform: translateY(-50%);
 }

 .chat-wrapper-details-inner-chat-offer-contents-list-item strong {
   font-weight: 600;
   font-size: 20px;
   color: var(--heading-color);
 }

 @media only screen and (max-width: 480px) {
   .chat-wrapper-details-inner-chat-offer-contents-list-item strong {
     font-size: 18px;
   }
 }

 @media only screen and (max-width: 375px) {
   .chat-wrapper-details-inner-chat-offer-contents-list-item strong {
     font-size: 16px;
   }
 }

 .chat-wrapper-details-inner-chat-offer-contents-list-item strong.color-one {
   color: var(--main-color-one);
 }
 .chat-wrapper-details-footer-btn-right {
   text-align: right;
   margin-top: 3px;
 }
 .milestone-list {
   margin: 0;
   padding: 0;
   list-style: none;
 }

 .milestone-list-item {
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   -webkit-box-align: center;
   -ms-flex-align: center;
   align-items: center;
   -webkit-box-pack: justify;
   -ms-flex-pack: justify;
   justify-content: space-between;
   -ms-flex-wrap: wrap;
   flex-wrap: wrap;
   gap: 10px;
   background-color: var(--border-color);
   padding: 10px;
   border-radius: 5px;
 }

 .milestone-list-item:not(:first-child) {
   margin-top: 10px;
 }

 .milestone-list-item-para {
   font-size: 15px;
   font-weight: 400;
   color: var(--paragraph-color);
 }

 .milestone-list-item-price {
   font-size: 20px;
   font-weight: 600;
   color: var(--main-color-one);
 }

 .milestone-list-item-btn {
   display: -webkit-inline-box;
   display: -ms-inline-flexbox;
   display: inline-flex;
   padding: 3px 15px;
   background-color: rgba(var(--main-color-one-rgb), 0.1);
   color: var(--main-color-one);
   border-radius: 30px;
   font-size: 15px;
   font-weight: 500;
 }

 .milestone-list-item-btn.funded {
   background-color: var(--main-color-one);
   color: #fff;
 }

 .milestone-list-item.funded {
   background-color: rgba(var(--main-color-one-rgb), 0.1);
 }

 .milestone-list-item.funded .milestone-list-item-btn {
   background-color: var(--main-color-one);
   color: #fff;
 }
 .download-pdf-chat {
   display: inline-block;
   font-size: 14px;
   font-weight: 400;
   line-height: 20px;
   background: var(--main-color-one);
   border: 1px solid var(--main-color-one);
   color: var(--white);
   padding: 5px 10px;
   border-radius: 4px;
   transition: all .3s;
 }
 .download-pdf-chat:focus,
 .download-pdf-chat:hover {
   color: var(--main-color-one);
   background-color: unset;
   border-collapse: var(--main-color-one);
 }
 .modal-body {
   max-height: calc(100vh - 180px);
   overflow-y: auto;
 }