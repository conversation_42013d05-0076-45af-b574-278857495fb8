<?php

namespace App\Http\Controllers\Frontend\Client;

use App\Http\Controllers\Controller;
use App\Mail\BasicMail;
use App\Models\IdentityVerification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Intervention\Image\Facades\Image;
use PragmaRX\Google2FALaravel\Support\Authenticator;

class ClientController extends Controller
{
    //view profile
    public function profile()
    {
        return view('frontend.user.client.profile.profile-settings');
    }

    //edit profile info
    public function edit_profile(Request $request)
    {
        $request->validate(
            [
            'first_name'=>'required|min:1|max:50',
            'last_name'=>'required|min:1|max:50',
            'email'=>'required|email|unique:users,email,'.Auth::guard('web')->user()->id,
            'country'=>'required',
        ],
        [
            'first_name.required'=>'First name is required',
            'last_name.required'=>'Last name is required',
            'country_id.required'=>'Country is required',
        ]);

        if($request->ajax()){
            User::where('id',Auth::guard('web')->user()->id)->update([
                'first_name'=>$request->first_name,
                'last_name'=>$request->last_name,
                'email'=>$request->email,
                'country_id'=>$request->country,
                'state_id'=>$request->state,
                'city_id'=>$request->city,
            ]);
            return response()->json([
                'status'=>'ok',
            ]);
        }

    }

    //edit profile photo
    public function edit_profile_photo(Request $request)
    {
        $user_id = Auth::guard('web')->user()->id;
        $user_image = User::where('id',$user_id)->first();
        $delete_old_img =  'assets/uploads/profile/'.$user_image->image;

        if ($image = $request->file('image')) {
            $request->validate(
                ['image'=>'required|mimes:jpg,jpeg,png,gif,svg|max:1024'],
                ['image.required'=>'Image is required']
            );
            if(file_exists($delete_old_img)){
                File::delete($delete_old_img);
            }
            $imageName = time().'-'.uniqid().'.'.$image->getClientOriginalExtension();
            $image->move('assets/uploads/profile', $imageName);
        }else{
            $imageName = $user_image->image;
        }
        if($request->ajax()){
            User::where('id',$user_id)->update(['image'=>$imageName]);
            return response()->json(['status'=>'ok']);
        }
    }

    // freelancer identity verification
    public function identity_verification(Request $request)
    {
        $user_id = Auth::guard('web')->user()->id;
        if($request->isMethod('post')){
            $request->validate([
                'country'=>'required',
                'state'=>'required',
                'address'=>'required|max:191',
                'zipcode'=>'required|max:191',
                'national_id_number'=>'required|max:255',
                'front_image'=>'required|image|mimes:jpeg,png,jpg|max:5120',
                'back_image'=>'required|image|mimes:jpeg,png,jpg|max:5120',
            ]);

            $verification_image = IdentityVerification::where('user_id',$user_id)->first();
            $delete_front_img = '';
            $delete_back_img = '';

            if(!empty($verification_image)){
                $delete_front_img =  'assets/uploads/verification/'.$verification_image->front_image;
                $delete_back_img =  'assets/uploads/verification/'.$verification_image->back_image;
            }

            if ($image = $request->file('front_image')) {
                if(file_exists($delete_front_img)){
                    File::delete($delete_front_img);
                }
                $front_image_name = time().'-'.uniqid().'.'.$image->getClientOriginalExtension();
                $image->move('assets/uploads/verification', $front_image_name);
            }else{
                $front_image_name = $verification_image->front_image;
            }

            if ($image = $request->file('back_image')) {
                if(file_exists($delete_back_img)){
                    File::delete($delete_back_img);
                }
                $back_image_name= time().'-'.uniqid().'.'.$image->getClientOriginalExtension();
                $image->move('assets/uploads/verification', $back_image_name);
            }else{
                $back_image_name = $verification_image->back_image;
            }

            IdentityVerification::updateOrCreate(
                ['user_id'=> $user_id],
                [
                    'user_id'=>$user_id,
                    'verify_by'=>$request->verify_by,
                    'country_id'=>$request->country,
                    'state_id'=>$request->state,
                    'city_id'=>$request->city ?? 0,
                    'address'=>$request->address,
                    'zipcode'=>$request->zipcode,
                    'national_id_number'=>$request->national_id_number,
                    'front_image'=>$front_image_name,
                    'back_image'=>$back_image_name,
                    'status'=>null,
                ]
            );
            try {
                $message = get_static_option('user_identity_verify_message') ?? "<p>{{ __('Hello')}},</p></p>{{ __('You have a new request for user identity verification')}}</p>";
                Mail::to(get_static_option('site_global_email'))->send(new BasicMail([
                    'subject' => get_static_option('user_identity_verify_subject') ?? __('User Identity Verify Email'),
                    'message' => $message
                ]));
            }
            catch (\Exception $e) {}
            return response()->json(['status'=>'success']);
        }

        $user_identity = IdentityVerification::where('user_id',$user_id)->first();
        return view('frontend.user.client.identity.verification',compact('user_identity'));
    }

    // check password
    public function check_password(Request $request)
    {
        if ($request->isMethod('post')) {
            $current_password = User::select('password')->where('id',Auth::user()->id)->first();
            if (Hash::check($request->current_password, $current_password->password)) {
                return response()->json([
                    'status'=>'match',
                    'msg'=>__('Current password match'),
                ]);
            }else{
                return response()->json([
                    'msg'=>__('Current password is wrong'),
                ]);
            }
        }
    }

    // password change
    public function change_password(Request $request)
    {
        if ($request->isMethod('post')) {
            $request->validate([
                'current_password' => 'required|min:6',
                'new_password' => 'required|min:6',
                'confirm_new_password' => 'required|min:6',
            ]);
            $user = User::select(['id','password'])->where('id',Auth::user()->id)->first();

            if (Hash::check($request->current_password, $user->password)) {
                if ($request->new_password == $request->confirm_new_password) {
                    User::where('id', $user->id)->update(['password' => Hash::make($request->new_password)]);
                    return response()->json(['status'=>'success']);
                }
                return response()->json(['status'=>'not_match']);
            }
            return response()->json(['status'=>'current_pass_wrong']);
        }
        return view('frontend.user.client.password.password');
    }

    public function logout()
    {
        Auth::guard('web')->logout();
        (new Authenticator(request()))->logout();
        return redirect('/');
    }
}
